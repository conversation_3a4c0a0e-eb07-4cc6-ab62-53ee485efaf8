/**
 * ML Historical Analysis Module
 * Handles historical data analysis and visualization for ML predictions
 */

class MLHistoricalAnalysis {
  constructor() {
    this.isInitialized = false;
    this.signalSystem = null;
    this.mlVisualization = null;
    this.currentTimeframe = '1h';
    this.historicalData = {};
    this.analysisResults = {};
    
    // Initialize with error handling
    this.initialize().catch(error => {
      console.error('[MLHistoricalAnalysis] Initialization error:', error);
    });
  }

  async initialize() {
    if (this.isInitialized) return;
    
    console.log('[MLHistoricalAnalysis] Initializing...');
    
    try {
      // Wait for required dependencies
      await this.waitForDependencies();
      
      // Initialize components
      this.initializeEventListeners();
      this.initializeUI();
      
      this.isInitialized = true;
      console.log('[MLHistoricalAnalysis] Initialized successfully');
      
      // Notify other components
      this.dispatchEvent('mlHistoricalAnalysisReady', { timestamp: Date.now() });
      
    } catch (error) {
      console.error('[MLHistoricalAnalysis] Initialization failed:', error);
      // Schedule retry
      setTimeout(() => this.initialize(), 5000);
    }
  }
  
  async waitForDependencies() {
    const maxRetries = 10;
    const retryDelay = 1000;
    
    for (let i = 0; i < maxRetries; i++) {
      if (window.signalCoreInstance && window.mlVisualization) {
        this.signalSystem = window.signalCoreInstance;
        this.mlVisualization = window.mlVisualization;
        return true;
      }
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
    
    throw new Error('Dependencies not available after maximum retries');
  }
  
  initializeEventListeners() {
    // Listen for signal updates
    document.addEventListener('signalUpdate', this.handleSignalUpdate.bind(this));
    
    // Listen for timeframe changes
    document.addEventListener('timeframeChanged', (event) => {
      this.currentTimeframe = event.detail.timeframe;
      this.updateAnalysis();
    });
    
    // Listen for ML prediction updates
    document.addEventListener('mlPredictionUpdate', this.handlePredictionUpdate.bind(this));
  }
  
  initializeUI() {
    // Create or update the historical analysis panel
    let panel = document.getElementById('ml-historical-analysis');
    
    if (!panel) {
      panel = document.createElement('div');
      panel.id = 'ml-historical-analysis';
      panel.className = 'ml-control-panel';
      
      // Add panel to the DOM (adjust selector as needed)
      const container = document.querySelector('.ml-controls-section') || document.body;
      container.appendChild(panel);
    }
    
    // Update panel content
    panel.innerHTML = `
      <div class="panel-header">
        <h4>Historical Analysis</h4>
        <button class="close-panel" onclick="document.getElementById('ml-historical-analysis').style.display='none'">×</button>
      </div>
      <div class="panel-content">
        <div class="analysis-controls">
          <div class="form-group">
            <label>Analysis Period</label>
            <select id="analysis-period">
              <option value="1d">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d" selected>Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
          </div>
          <div class="form-group">
            <label>Confidence Threshold</label>
            <input type="range" id="confidence-threshold" min="0.5" max="1" step="0.05" value="0.8">
            <span id="confidence-value">80%</span>
          </div>
          <button id="run-analysis" class="btn btn-primary">Run Analysis</button>
        </div>
        <div class="analysis-results">
          <div class="loading-indicator" style="display: none;">
            <div class="spinner"></div>
            <p>Analyzing historical data...</p>
          </div>
          <div id="analysis-output"></div>
        </div>
      </div>
    `;
    
    // Add event listeners for UI controls
    document.getElementById('run-analysis').addEventListener('click', () => this.runAnalysis());
    document.getElementById('analysis-period').addEventListener('change', () => this.updateAnalysis());
    document.getElementById('confidence-threshold').addEventListener('input', (e) => {
      document.getElementById('confidence-value').textContent = 
        `${Math.round(e.target.value * 100)}%`;
      this.updateAnalysis();
    });
  }
  
  async runAnalysis() {
    try {
      // Show loading state
      const loadingIndicator = document.querySelector('.loading-indicator');
      const output = document.getElementById('analysis-output');
      loadingIndicator.style.display = 'block';
      output.innerHTML = '';
      
      // Get analysis parameters
      const period = document.getElementById('analysis-period').value;
      const confidenceThreshold = parseFloat(document.getElementById('confidence-threshold').value);
      
      // Simulate analysis (replace with actual implementation)
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Generate sample results
      const results = this.generateSampleResults(period, confidenceThreshold);
      
      // Display results
      this.displayResults(results);
      
    } catch (error) {
      console.error('[MLHistoricalAnalysis] Analysis failed:', error);
      this.showError('Analysis failed. Please try again.');
    } finally {
      document.querySelector('.loading-indicator').style.display = 'none';
    }
  }
  
  generateSampleResults(period, confidenceThreshold) {
    // This is a placeholder - replace with actual analysis logic
    return {
      period,
      confidenceThreshold,
      totalSignals: Math.floor(Math.random() * 100) + 50,
      accuracy: (Math.random() * 0.5 + 0.5).toFixed(2),
      bestPerformingIndicators: [
        { name: 'RSI', accuracy: (Math.random() * 0.5 + 0.5).toFixed(2) },
        { name: 'MACD', accuracy: (Math.random() * 0.5 + 0.5).toFixed(2) },
        { name: 'Bollinger Bands', accuracy: (Math.random() * 0.5 + 0.5).toFixed(2) }
      ].sort((a, b) => b.accuracy - a.accuracy),
      predictions: [
        { timestamp: Date.now() - 86400000, symbol: 'BTC/USD', direction: 'bullish', confidence: 0.85 },
        { timestamp: Date.now() - 172800000, symbol: 'ETH/USD', direction: 'bearish', confidence: 0.72 },
        { timestamp: Date.now() - 259200000, symbol: 'XRP/USD', direction: 'bullish', confidence: 0.91 }
      ]
    };
  }
  
  displayResults(results) {
    const output = document.getElementById('analysis-output');
    
    // Create HTML for results
    let html = `
      <div class="analysis-summary">
        <h5>Analysis Summary (${results.period})</h5>
        <div class="metrics">
          <div class="metric">
            <span class="metric-value">${results.totalSignals}</span>
            <span class="metric-label">Total Signals</span>
          </div>
          <div class="metric">
            <span class="metric-value">${(results.accuracy * 100).toFixed(1)}%</span>
            <span class="metric-label">Accuracy</span>
          </div>
          <div class="metric">
            <span class="metric-value">${(results.confidenceThreshold * 100).toFixed(0)}%</span>
            <span class="metric-label">Confidence</span>
          </div>
        </div>
        
        <div class="top-indicators">
          <h6>Best Performing Indicators</h6>
          <ul>
            ${results.bestPerformingIndicators.map(ind => 
              `<li>${ind.name}: <strong>${(ind.accuracy * 100).toFixed(1)}%</strong></li>`
            ).join('')}
          </ul>
        </div>
      </div>
      
      <div class="recent-predictions">
        <h5>Recent Predictions</h5>
        <table>
          <thead>
            <tr>
              <th>Time</th>
              <th>Symbol</th>
              <th>Direction</th>
              <th>Confidence</th>
            </tr>
          </thead>
          <tbody>
            ${results.predictions.map(pred => `
              <tr>
                <td>${new Date(pred.timestamp).toLocaleTimeString()}</td>
                <td>${pred.symbol}</td>
                <td class="direction-${pred.direction}">${pred.direction}</td>
                <td>${(pred.confidence * 100).toFixed(0)}%</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    `;
    
    // Add some basic styling if not already present
    this.ensureStyles();
    
    // Update the DOM
    output.innerHTML = html;
  }
  
  ensureStyles() {
    // Only add styles once
    if (document.getElementById('ml-historical-analysis-styles')) return;
    
    const style = document.createElement('style');
    style.id = 'ml-historical-analysis-styles';
    style.textContent = `
      .ml-control-panel {
        position: fixed;
        right: 20px;
        top: 100px;
        width: 400px;
        max-width: 90vw;
        max-height: 80vh;
        background: var(--bg-color-secondary);
        border: 1px solid var(--border-color);
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        z-index: 1000;
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }
      
      .panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        background: var(--bg-color-tertiary);
        border-bottom: 1px solid var(--border-color);
      }
      
      .panel-header h4 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color-primary);
      }
      
      .close-panel {
        background: none;
        border: none;
        color: var(--text-color-secondary);
        font-size: 20px;
        cursor: pointer;
        padding: 0 4px;
        line-height: 1;
      }
      
      .close-panel:hover {
        color: var(--text-color-primary);
      }
      
      .panel-content {
        padding: 16px;
        overflow-y: auto;
        flex: 1;
      }
      
      .form-group {
        margin-bottom: 12px;
      }
      
      .form-group label {
        display: block;
        margin-bottom: 6px;
        font-size: 13px;
        color: var(--text-color-secondary);
      }
      
      .form-group input[type="range"] {
        width: 100%;
        margin: 8px 0;
      }
      
      .btn {
        display: inline-block;
        padding: 6px 12px;
        background: var(--bg-color-primary);
        border: 1px solid var(--border-color);
        border-radius: 4px;
        color: var(--text-color-primary);
        cursor: pointer;
        font-size: 13px;
      }
      
      .btn-primary {
        background: var(--accent-color);
        border-color: var(--accent-color);
        color: white;
      }
      
      .btn:hover {
        background: var(--bg-color-hover);
      }
      
      .btn-primary:hover {
        background: var(--accent-color-hover);
        border-color: var(--accent-color-hover);
      }
      
      .metrics {
        display: flex;
        justify-content: space-between;
        margin: 16px 0;
      }
      
      .metric {
        text-align: center;
        padding: 8px;
        background: var(--bg-color-tertiary);
        border-radius: 4px;
        flex: 1;
        margin: 0 4px;
      }
      
      .metric-value {
        display: block;
        font-size: 18px;
        font-weight: 600;
        color: var(--accent-color);
      }
      
      .metric-label {
        font-size: 12px;
        color: var(--text-color-secondary);
      }
      
      table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 12px;
        font-size: 13px;
      }
      
      th, td {
        padding: 8px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
      }
      
      th {
        font-weight: 600;
        color: var(--text-color-secondary);
      }
      
      .direction-bullish {
        color: #4caf50;
      }
      
      .direction-bearish {
        color: #f44336;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .spinner {
        border: 3px solid rgba(0, 0, 0, 0.1);
        border-radius: 50%;
        border-top: 3px solid var(--accent-color);
        width: 20px;
        height: 20px;
        animation: spin 1s linear infinite;
        margin: 0 auto 8px;
      }
      
      .loading-indicator {
        text-align: center;
        padding: 20px;
        color: var(--text-color-secondary);
      }
      
      /* Dark theme overrides */
      [data-theme="dark"] {
        --bg-color-primary: #1e1e2d;
        --bg-color-secondary: #2a2a3c;
        --bg-color-tertiary: #323248;
        --text-color-primary: #e1e1ff;
        --text-color-secondary: #a2a3bd;
        --border-color: #3a3a59;
        --accent-color: #5d78ff;
        --accent-color-hover: #4a5fff;
        --bg-color-hover: #3a3a59;
      }
      
      /* Light theme overrides */
      [data-theme="light"] {
        --bg-color-primary: #ffffff;
        --bg-color-secondary: #f5f5fa;
        --bg-color-tertiary: #eef0f8;
        --text-color-primary: #2c2e3a;
        --text-color-secondary: #6c7293;
        --border-color: #e4e6ef;
        --accent-color: #5d78ff;
        --accent-color-hover: #4a5fff;
        --bg-color-hover: #f0f2ff;
      }
    `;
    
    document.head.appendChild(style);
  }
  
  handleSignalUpdate(event) {
    const { indicator, timeframe, value } = event.detail;
    
    // Store the signal data
    if (!this.historicalData[indicator]) {
      this.historicalData[indicator] = {};
    }
    
    if (!this.historicalData[indicator][timeframe]) {
      this.historicalData[indicator][timeframe] = [];
    }
    
    this.historicalData[indicator][timeframe].push({
      timestamp: Date.now(),
      value
    });
    
    // Keep only the most recent data points (e.g., last 1000)
    if (this.historicalData[indicator][timeframe].length > 1000) {
      this.historicalData[indicator][timeframe].shift();
    }
    
    // Update analysis if needed
    if (this.shouldUpdateAnalysis(indicator, timeframe)) {
      this.updateAnalysis();
    }
  }
  
  handlePredictionUpdate(event) {
    const { predictions } = event.detail;
    // Process new predictions
    this.latestPredictions = predictions;
    this.updateAnalysis();
  }
  
  shouldUpdateAnalysis(indicator, timeframe) {
    // Add logic to determine if analysis should be updated
    // For example, only update for the current timeframe
    return timeframe === this.currentTimeframe;
  }
  
  updateAnalysis() {
    // Update the analysis based on current data and settings
    if (!this.isInitialized) return;
    
    // Get current settings
    const period = document.getElementById('analysis-period')?.value || '30d';
    const confidenceThreshold = parseFloat(
      document.getElementById('confidence-threshold')?.value || '0.8'
    );
    
    // Run analysis with current parameters
    this.runAnalysis();
  }
  
  showError(message) {
    const output = document.getElementById('analysis-output');
    if (output) {
      output.innerHTML = `
        <div class="error-message" style="color: #f44336; padding: 12px; background: #ffebee; border-radius: 4px; margin: 8px 0;">
          ${message}
        </div>
      `;
    }
  }
  
  dispatchEvent(eventName, detail = {}) {
    const event = new CustomEvent(eventName, { detail });
    document.dispatchEvent(event);
  }
}

// Initialize the module when the DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.mlHistoricalAnalysis = new MLHistoricalAnalysis();
  });
} else {
  window.mlHistoricalAnalysis = new MLHistoricalAnalysis();
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MLHistoricalAnalysis;
}
