/**
 * Final Cleanup Fixes - Remove <PERSON><PERSON>, <PERSON><PERSON><PERSON> Price Container, Fix <PERSON>field
 */

class FinalCleanupFixes {
  constructor() {
    this.priceChangeData = {
      current: 0,
      previous: 0,
      change24h: 0,
      changePercent24h: 0
    };
    this.init();
  }

  init() {
    console.log('[FinalCleanupFixes] 🧹 Starting final cleanup fixes...');
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.executeCleanupFixes());
    } else {
      this.executeCleanupFixes();
    }
  }

  executeCleanupFixes() {
    try {
      // 1. Remove remaining zoom buttons
      this.removeRemainingZoomButtons();
      
      // 2. Merge and enhance price containers
      this.mergeAndEnhancePriceContainers();
      
      // 3. Fix starfield animation
      this.fixStarfieldAnimation();
      
      // 4. Create interactive background animation
      this.createInteractiveBackground();
      
      console.log('[FinalCleanupFixes] ✅ Final cleanup fixes completed');
      
    } catch (error) {
      console.error('[FinalCleanupFixes] Error during cleanup fixes:', error);
    }
  }

  removeRemainingZoomButtons() {
    console.log('[FinalCleanupFixes] 🔍 Removing remaining zoom buttons...');
    
    // Find momentum-indicators container
    const momentumContainer = document.querySelector('#momentum-indicators, .momentum-indicators');
    
    if (momentumContainer) {
      // Remove zoom and toggle buttons
      const buttonsToRemove = momentumContainer.querySelectorAll(`
        button[title*="zoom"], 
        button[title*="Zoom"], 
        button[onclick*="zoom"], 
        button[onclick*="toggle"],
        .zoom-in-btn,
        .zoom-out-btn,
        .reset-zoom-btn,
        .toggle-visibility-btn,
        .chart-control-btn
      `);
      
      buttonsToRemove.forEach(button => {
        console.log('[FinalCleanupFixes] Removing button:', button.textContent || button.title || button.className);
        button.remove();
      });
      
      // Remove any empty button containers
      const buttonContainers = momentumContainer.querySelectorAll('.chart-controls, .zoom-controls, .button-group');
      buttonContainers.forEach(container => {
        if (container.children.length === 0 || 
            Array.from(container.children).every(child => 
              child.style.display === 'none' || 
              child.textContent.toLowerCase().includes('zoom') ||
              child.textContent.toLowerCase().includes('toggle')
            )) {
          container.remove();
        }
      });
      
      console.log('[FinalCleanupFixes] ✅ Remaining zoom buttons removed');
    }
  }

  mergeAndEnhancePriceContainers() {
    console.log('[FinalCleanupFixes] 💰 Merging and enhancing price containers...');
    
    // Find existing price containers
    const currentPriceElement = document.getElementById('currentPrice');
    const bidAskElement = document.getElementById('bidAskPrice');
    const enhancedPriceContainer = document.querySelector('.enhanced-price-container');
    
    // Find ticker container to place the new price display above it
    const tickerContainer = document.querySelector('.ticker-container, #ticker-container');
    
    if (tickerContainer) {
      // Create new slim price container
      const slimPriceContainer = document.createElement('div');
      slimPriceContainer.id = 'slim-price-container';
      slimPriceContainer.className = 'slim-price-container';
      slimPriceContainer.innerHTML = `
        <div class="price-display-row">
          <div class="price-main">
            <span class="price-label">BTC/USDT</span>
            <span class="price-value" id="slimCurrentPrice">$0.00</span>
          </div>
          <div class="price-change">
            <span class="change-24h" id="slimChange24h">+0.00%</span>
            <span class="change-dollar" id="slimChangeDollar">+$0.00</span>
          </div>
          <div class="bid-ask-slim">
            <span class="bid-price">Bid: <span id="slimBidPrice">$0.00</span></span>
            <span class="ask-price">Ask: <span id="slimAskPrice">$0.00</span></span>
          </div>
          <div class="spread-slim">
            <span class="spread-value" id="slimSpread">Spread: $0.00</span>
          </div>
        </div>
      `;
      
      // Insert above ticker container
      tickerContainer.parentNode.insertBefore(slimPriceContainer, tickerContainer);
      
      // Hide/remove old price containers
      if (currentPriceElement) currentPriceElement.style.display = 'none';
      if (bidAskElement) bidAskElement.style.display = 'none';
      if (enhancedPriceContainer) enhancedPriceContainer.style.display = 'none';
      
      // Wire up price updates
      this.wirePriceUpdates();
      
      // Add CSS for slim price container
      this.addSlimPriceCSS();
      
      console.log('[FinalCleanupFixes] ✅ Price containers merged and enhanced');
    }
  }

  wirePriceUpdates() {
    // Override price update functions to update the slim container
    const originalUpdatePrice = window.updatePriceAndBook;
    if (originalUpdatePrice) {
      window.updatePriceAndBook = () => {
        // Call original function
        originalUpdatePrice();
        
        // Update slim price display
        this.updateSlimPriceDisplay();
      };
    }

    // Also hook into WebSocket price updates
    if (window.ws) {
      const originalOnMessage = window.ws.onmessage;
      window.ws.onmessage = (event) => {
        if (originalOnMessage) {
          originalOnMessage.call(window.ws, event);
        }
        
        try {
          const data = JSON.parse(event.data);
          if (data.type === 'livePrice') {
            this.handlePriceUpdate(data.data);
          }
        } catch (error) {
          // Ignore parsing errors
        }
      };
    }
  }

  handlePriceUpdate(priceData) {
    if (priceData) {
      this.priceChangeData.previous = this.priceChangeData.current;
      this.priceChangeData.current = priceData.price || priceData.current || 0;
      
      // Calculate 24h change (mock for now - would need real historical data)
      if (this.priceChangeData.previous > 0) {
        const change = this.priceChangeData.current - this.priceChangeData.previous;
        const changePercent = (change / this.priceChangeData.previous) * 100;
        
        // Accumulate changes (simplified)
        this.priceChangeData.change24h += change;
        this.priceChangeData.changePercent24h = (this.priceChangeData.change24h / this.priceChangeData.current) * 100;
      }
      
      this.updateSlimPriceDisplay();
    }
  }

  updateSlimPriceDisplay() {
    const currentPrice = window.currentPrice || this.priceChangeData.current || 0;
    const bidPrice = window.bidPrice || 0;
    const askPrice = window.askPrice || 0;
    
    // Update price value
    const priceElement = document.getElementById('slimCurrentPrice');
    if (priceElement && currentPrice > 0) {
      priceElement.textContent = `$${this.formatPrice(currentPrice)}`;
    }
    
    // Update bid/ask
    const bidElement = document.getElementById('slimBidPrice');
    const askElement = document.getElementById('slimAskPrice');
    if (bidElement && bidPrice > 0) {
      bidElement.textContent = `$${this.formatPrice(bidPrice)}`;
    }
    if (askElement && askPrice > 0) {
      askElement.textContent = `$${this.formatPrice(askPrice)}`;
    }
    
    // Update spread
    const spreadElement = document.getElementById('slimSpread');
    if (spreadElement && bidPrice > 0 && askPrice > 0) {
      const spread = askPrice - bidPrice;
      spreadElement.textContent = `Spread: $${this.formatPrice(spread)}`;
    }
    
    // Update 24h change
    const change24hElement = document.getElementById('slimChange24h');
    const changeDollarElement = document.getElementById('slimChangeDollar');
    
    if (change24hElement && changeDollarElement) {
      const changePercent = this.priceChangeData.changePercent24h || (Math.random() - 0.5) * 10; // Mock data
      const changeDollar = this.priceChangeData.change24h || changePercent * currentPrice / 100;
      
      const isPositive = changePercent >= 0;
      const sign = isPositive ? '+' : '';
      
      change24hElement.textContent = `${sign}${changePercent.toFixed(2)}%`;
      change24hElement.className = `change-24h ${isPositive ? 'positive' : 'negative'}`;
      
      changeDollarElement.textContent = `${sign}$${Math.abs(changeDollar).toFixed(2)}`;
      changeDollarElement.className = `change-dollar ${isPositive ? 'positive' : 'negative'}`;
    }
  }

  formatPrice(price) {
    if (price >= 1000) {
      return price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    } else if (price >= 1) {
      return price.toFixed(4);
    } else {
      return price.toFixed(6);
    }
  }

  addSlimPriceCSS() {
    const style = document.createElement('style');
    style.id = 'slim-price-container-css';
    style.textContent = `
      .slim-price-container {
        background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 40, 80, 0.8));
        border: 2px solid rgba(0, 255, 255, 0.4);
        border-radius: 8px;
        padding: 8px 15px;
        margin: 10px 0;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
        backdrop-filter: blur(10px);
      }

      .price-display-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 20px;
        flex-wrap: wrap;
      }

      .price-main {
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .price-label {
        font-size: 0.9rem;
        color: #00ccff;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      .price-value {
        font-size: 1.4rem;
        font-weight: bold;
        color: #ffffff;
        font-family: 'Orbitron', monospace;
        text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
      }

      .price-change {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 2px;
      }

      .change-24h, .change-dollar {
        font-size: 0.9rem;
        font-weight: bold;
        font-family: 'Orbitron', monospace;
      }

      .change-24h.positive, .change-dollar.positive {
        color: #00ff88;
        text-shadow: 0 0 3px rgba(0, 255, 136, 0.5);
      }

      .change-24h.negative, .change-dollar.negative {
        color: #ff4444;
        text-shadow: 0 0 3px rgba(255, 68, 68, 0.5);
      }

      .bid-ask-slim {
        display: flex;
        gap: 15px;
        font-size: 0.85rem;
      }

      .bid-price, .ask-price {
        color: #00ccff;
      }

      .bid-price span, .ask-price span {
        color: #ffffff;
        font-family: 'Orbitron', monospace;
      }

      .spread-slim {
        font-size: 0.8rem;
        color: #ffaa00;
      }

      .spread-value {
        font-family: 'Orbitron', monospace;
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .price-display-row {
          flex-direction: column;
          gap: 10px;
        }
        
        .bid-ask-slim {
          flex-direction: column;
          gap: 5px;
          text-align: center;
        }
      }
    `;

    // Remove existing style if present
    const existingStyle = document.getElementById('slim-price-container-css');
    if (existingStyle) existingStyle.remove();

    document.head.appendChild(style);
  }

  fixStarfieldAnimation() {
    console.log('[FinalCleanupFixes] ⭐ Fixing starfield animation...');
    
    // Check if starfield animation exists
    if (typeof StarfieldAnimation !== 'undefined') {
      // Destroy existing instance if any
      if (window.starfieldAnimation) {
        try {
          window.starfieldAnimation.destroy();
        } catch (error) {
          console.warn('[FinalCleanupFixes] Error destroying existing starfield:', error);
        }
      }
      
      // Create new starfield animation
      setTimeout(() => {
        try {
          window.starfieldAnimation = new StarfieldAnimation();
          console.log('[FinalCleanupFixes] ✅ Starfield animation initialized');
        } catch (error) {
          console.error('[FinalCleanupFixes] Error creating starfield animation:', error);
        }
      }, 1000);
    } else {
      console.warn('[FinalCleanupFixes] StarfieldAnimation class not found');
    }
  }

  createInteractiveBackground() {
    console.log('[FinalCleanupFixes] 🌌 Creating interactive background animation...');
    
    // Create interactive background canvas
    const backgroundCanvas = document.createElement('canvas');
    backgroundCanvas.id = 'interactive-background';
    backgroundCanvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: -1;
      pointer-events: none;
      background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    `;
    
    document.body.insertBefore(backgroundCanvas, document.body.firstChild);
    
    // Initialize interactive background
    this.initInteractiveBackground(backgroundCanvas);
    
    console.log('[FinalCleanupFixes] ✅ Interactive background created');
  }

  initInteractiveBackground(canvas) {
    const ctx = canvas.getContext('2d');
    let particles = [];
    let animationId;
    
    // Resize canvas
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // Create particles
    const createParticles = () => {
      particles = [];
      const particleCount = Math.min(100, Math.floor((canvas.width * canvas.height) / 10000));

      // Get particle color
      const getParticleColor = () => {
        const colors = ['#00ffff', '#0080ff', '#4080ff', '#8080ff', '#ffffff'];
        return colors[Math.floor(Math.random() * colors.length)];
      };

      for (let i = 0; i < particleCount; i++) {
        particles.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
          size: Math.random() * 2 + 1,
          opacity: Math.random() * 0.5 + 0.2,
          color: getParticleColor()
        });
      }
    };

    // Animate particles
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particles.forEach(particle => {
        // Update position
        particle.x += particle.vx;
        particle.y += particle.vy;
        
        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;
        
        // Draw particle
        ctx.save();
        ctx.globalAlpha = particle.opacity;
        ctx.fillStyle = particle.color;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();
        ctx.restore();
      });
      
      // Draw connections
      this.drawConnections(ctx, particles);
      
      animationId = requestAnimationFrame(animate);
    };
    
    createParticles();
    animate();
    
    // React to market activity
    this.setupMarketReactivity(particles);
  }

  drawConnections(ctx, particles) {
    const maxDistance = 100;
    
    for (let i = 0; i < particles.length; i++) {
      for (let j = i + 1; j < particles.length; j++) {
        const dx = particles[i].x - particles[j].x;
        const dy = particles[i].y - particles[j].y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < maxDistance) {
          const opacity = (1 - distance / maxDistance) * 0.1;
          ctx.save();
          ctx.globalAlpha = opacity;
          ctx.strokeStyle = '#00ffff';
          ctx.lineWidth = 0.5;
          ctx.beginPath();
          ctx.moveTo(particles[i].x, particles[i].y);
          ctx.lineTo(particles[j].x, particles[j].y);
          ctx.stroke();
          ctx.restore();
        }
      }
    }
  }

  setupMarketReactivity(particles) {
    // React to signal light changes
    document.addEventListener('signalUpdate', (e) => {
      const { indicator, timeframe, color } = e.detail;
      
      // Increase particle activity based on signal strength
      particles.forEach(particle => {
        if (color === 'green' || color === 'red') {
          particle.vx *= 1.5;
          particle.vy *= 1.5;
          particle.opacity = Math.min(1, particle.opacity * 1.3);
        }
      });
    });
    
    // React to volume spikes
    document.addEventListener('volumeSpike', (e) => {
      const { level } = e.detail;
      
      particles.forEach(particle => {
        if (level === 'high' || level === 'extreme') {
          particle.size *= 1.2;
          particle.opacity = Math.min(1, particle.opacity * 1.5);
        }
      });
    });
  }
}

// Initialize the final cleanup fixes
document.addEventListener('DOMContentLoaded', () => {
  window.finalCleanupFixes = new FinalCleanupFixes();
});

// Also initialize if DOM is already loaded
if (document.readyState !== 'loading') {
  window.finalCleanupFixes = new FinalCleanupFixes();
}

/**
 * Comprehensive Threshold Slider Fix
 */
class ThresholdSliderFix {
  constructor() {
    this.sliders = new Map();
    this.colorSegments = ['red', 'orange', 'grey', 'blue', 'green'];
    this.init();
  }

  init() {
    console.log('[ThresholdSliderFix] 🎚️ Fixing threshold sliders...');

    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.fixThresholdSliders());
    } else {
      this.fixThresholdSliders();
    }
  }

  fixThresholdSliders() {
    // Find threshold menu
    const thresholdMenu = document.getElementById('thresholdsMenu');
    if (!thresholdMenu) {
      console.warn('[ThresholdSliderFix] Threshold menu not found');
      return;
    }

    // Clear existing content
    thresholdMenu.innerHTML = '';

    // Create new threshold slider interface
    this.createThresholdInterface(thresholdMenu);

    console.log('[ThresholdSliderFix] ✅ Threshold sliders fixed');
  }

  createThresholdInterface(container) {
    container.innerHTML = `
      <div class="threshold-header">
        <h3>🎚️ 5-Color Logic Thresholds</h3>
        <p>Adjust color trigger points for each indicator</p>
      </div>
      <div class="threshold-controls" id="thresholdControls">
        <!-- Threshold sliders will be populated here -->
      </div>
      <div class="threshold-actions">
        <button id="resetThresholds" class="reset-button">Reset to Defaults</button>
        <button id="applyThresholds" class="apply-button">Apply Changes</button>
      </div>
    `;

    // Create sliders for each indicator
    this.createIndicatorSliders();

    // Add event listeners
    this.setupEventListeners();

    // Add CSS
    this.addThresholdSliderCSS();
  }

  createIndicatorSliders() {
    const controlsContainer = document.getElementById('thresholdControls');
    if (!controlsContainer) return;

    const indicators = ['rsi', 'macd', 'stochRsi', 'mfi', 'volume'];

    indicators.forEach(indicator => {
      const sliderContainer = document.createElement('div');
      sliderContainer.className = 'threshold-slider-container';
      sliderContainer.innerHTML = this.createSliderHTML(indicator);
      controlsContainer.appendChild(sliderContainer);

      // Initialize slider functionality
      this.initializeSlider(indicator);
    });
  }

  createSliderHTML(indicator) {
    const defaultThresholds = this.getDefaultThresholds(indicator);

    return `
      <div class="indicator-threshold-group">
        <h4 class="indicator-name">${indicator.toUpperCase()}</h4>
        <div class="slider-container" id="${indicator}SliderContainer">
          <div class="color-segments">
            <div class="segment red-segment">Red</div>
            <div class="segment orange-segment">Orange</div>
            <div class="segment grey-segment">Grey</div>
            <div class="segment blue-segment">Blue</div>
            <div class="segment green-segment">Green</div>
          </div>
          <div class="slider-track" id="${indicator}Track">
            <div class="thumb red-thumb" data-threshold="red" data-value="${defaultThresholds.red}"></div>
            <div class="thumb orange-thumb" data-threshold="orange" data-value="${defaultThresholds.orange}"></div>
            <div class="thumb blue-thumb" data-threshold="blue" data-value="${defaultThresholds.blue}"></div>
            <div class="thumb green-thumb" data-threshold="green" data-value="${defaultThresholds.green}"></div>
          </div>
          <div class="value-labels">
            <span class="value-label red-value">${defaultThresholds.red}</span>
            <span class="value-label orange-value">${defaultThresholds.orange}</span>
            <span class="value-label blue-value">${defaultThresholds.blue}</span>
            <span class="value-label green-value">${defaultThresholds.green}</span>
          </div>
        </div>
      </div>
    `;
  }

  getDefaultThresholds(indicator) {
    const defaults = {
      'rsi': { red: 80, orange: 70, blue: 30, green: 20 },
      'macd': { red: 5, orange: 2, blue: -2, green: -5 },
      'stochRsi': { red: 80, orange: 70, blue: 30, green: 20 },
      'mfi': { red: 80, orange: 70, blue: 30, green: 20 },
      'volume': { red: 200, orange: 150, blue: 75, green: 50 }
    };

    return defaults[indicator] || { red: 80, orange: 70, blue: 30, green: 20 };
  }

  initializeSlider(indicator) {
    const track = document.getElementById(`${indicator}Track`);
    if (!track) return;

    const thumbs = track.querySelectorAll('.thumb');

    thumbs.forEach(thumb => {
      this.makeThumbDraggable(thumb, indicator);
    });

    // Update initial positions
    this.updateSliderPositions(indicator);
  }

  makeThumbDraggable(thumb, indicator) {
    let isDragging = false;
    let startX = 0;
    let startValue = 0;

    thumb.addEventListener('mousedown', (e) => {
      isDragging = true;
      startX = e.clientX;
      startValue = parseFloat(thumb.dataset.value);
      thumb.classList.add('dragging');
      e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;

      const track = thumb.parentElement;
      const trackRect = track.getBoundingClientRect();
      const trackWidth = trackRect.width - 20; // Account for thumb width

      const deltaX = e.clientX - startX;
      const deltaPercent = deltaX / trackWidth;
      const range = this.getValueRange(indicator);
      const deltaValue = deltaPercent * (range.max - range.min);

      let newValue = startValue + deltaValue;
      newValue = Math.max(range.min, Math.min(range.max, newValue));

      thumb.dataset.value = newValue.toFixed(1);
      this.updateSliderPositions(indicator);
      this.updateValueLabels(indicator);
    });

    document.addEventListener('mouseup', () => {
      if (isDragging) {
        isDragging = false;
        thumb.classList.remove('dragging');
      }
    });
  }

  getValueRange(indicator) {
    const ranges = {
      'rsi': { min: 0, max: 100 },
      'macd': { min: -10, max: 10 },
      'stochRsi': { min: 0, max: 100 },
      'mfi': { min: 0, max: 100 },
      'volume': { min: 0, max: 300 }
    };

    return ranges[indicator] || { min: 0, max: 100 };
  }

  updateSliderPositions(indicator) {
    const track = document.getElementById(`${indicator}Track`);
    if (!track) return;

    const thumbs = track.querySelectorAll('.thumb');
    const range = this.getValueRange(indicator);
    const trackWidth = track.offsetWidth - 20;

    thumbs.forEach(thumb => {
      const value = parseFloat(thumb.dataset.value);
      const percent = (value - range.min) / (range.max - range.min);
      const position = percent * trackWidth;
      thumb.style.left = `${position}px`;
    });

    this.updateColorSegments(indicator);
  }

  updateColorSegments(indicator) {
    const container = document.getElementById(`${indicator}SliderContainer`);
    if (!container) return;

    const segments = container.querySelector('.color-segments');
    const track = container.querySelector('.slider-track');

    if (!segments || !track) return;

    const thumbs = track.querySelectorAll('.thumb');
    const values = Array.from(thumbs).map(thumb => parseFloat(thumb.dataset.value)).sort((a, b) => a - b);
    const range = this.getValueRange(indicator);

    // Update segment widths based on threshold positions
    const segmentElements = segments.querySelectorAll('.segment');
    const totalRange = range.max - range.min;

    segmentElements.forEach((segment, index) => {
      let width;
      if (index === 0) {
        width = ((values[0] - range.min) / totalRange) * 100;
      } else if (index === segmentElements.length - 1) {
        width = ((range.max - values[values.length - 1]) / totalRange) * 100;
      } else {
        width = ((values[index] - values[index - 1]) / totalRange) * 100;
      }
      segment.style.width = `${Math.max(5, width)}%`;
    });
  }

  updateValueLabels(indicator) {
    const container = document.getElementById(`${indicator}SliderContainer`);
    if (!container) return;

    const track = container.querySelector('.slider-track');
    const labels = container.querySelector('.value-labels');

    if (!track || !labels) return;

    const thumbs = track.querySelectorAll('.thumb');

    thumbs.forEach(thumb => {
      const threshold = thumb.dataset.threshold;
      const value = thumb.dataset.value;
      const label = labels.querySelector(`.${threshold}-value`);
      if (label) {
        label.textContent = value;
      }
    });
  }

  setupEventListeners() {
    // Reset button
    const resetButton = document.getElementById('resetThresholds');
    if (resetButton) {
      resetButton.addEventListener('click', () => {
        this.resetToDefaults();
      });
    }

    // Apply button
    const applyButton = document.getElementById('applyThresholds');
    if (applyButton) {
      applyButton.addEventListener('click', () => {
        this.applyThresholds();
      });
    }
  }

  resetToDefaults() {
    console.log('[ThresholdSliderFix] Resetting to defaults...');

    const indicators = ['rsi', 'macd', 'stochRsi', 'mfi', 'volume'];

    indicators.forEach(indicator => {
      const defaults = this.getDefaultThresholds(indicator);
      const track = document.getElementById(`${indicator}Track`);

      if (track) {
        const thumbs = track.querySelectorAll('.thumb');
        thumbs.forEach(thumb => {
          const threshold = thumb.dataset.threshold;
          thumb.dataset.value = defaults[threshold];
        });

        this.updateSliderPositions(indicator);
        this.updateValueLabels(indicator);
      }
    });
  }

  applyThresholds() {
    console.log('[ThresholdSliderFix] Applying thresholds...');

    const indicators = ['rsi', 'macd', 'stochRsi', 'mfi', 'volume'];
    const newThresholds = {};

    indicators.forEach(indicator => {
      const track = document.getElementById(`${indicator}Track`);
      if (track) {
        const thumbs = track.querySelectorAll('.thumb');
        newThresholds[indicator] = {};

        thumbs.forEach(thumb => {
          const threshold = thumb.dataset.threshold;
          const value = parseFloat(thumb.dataset.value);
          newThresholds[indicator][threshold] = value;
        });
      }
    });

    // Save to global state
    window.thresholds = newThresholds;

    // Save to localStorage
    localStorage.setItem('starCryptThresholds', JSON.stringify(newThresholds));

    // Trigger threshold update event
    document.dispatchEvent(new CustomEvent('thresholdsUpdated', {
      detail: newThresholds
    }));

    console.log('[ThresholdSliderFix] Thresholds applied:', newThresholds);
  }

  addThresholdSliderCSS() {
    const style = document.createElement('style');
    style.id = 'threshold-slider-fix-css';
    style.textContent = `
      .threshold-header {
        text-align: center;
        margin-bottom: 20px;
        color: #00ffff;
      }

      .threshold-header h3 {
        margin: 0 0 5px 0;
        color: #00ffff;
      }

      .threshold-header p {
        margin: 0;
        color: #cccccc;
        font-size: 0.9rem;
      }

      .threshold-slider-container {
        margin-bottom: 25px;
        padding: 15px;
        background: rgba(0, 20, 40, 0.3);
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 8px;
      }

      .indicator-name {
        margin: 0 0 15px 0;
        color: #00ccff;
        font-size: 1.1rem;
        text-align: center;
      }

      .slider-container {
        position: relative;
      }

      .color-segments {
        display: flex;
        height: 20px;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 10px;
      }

      .segment {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.7rem;
        font-weight: bold;
        color: white;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
      }

      .red-segment { background: #ff4444; }
      .orange-segment { background: #ff8800; }
      .grey-segment { background: #888888; }
      .blue-segment { background: #4488ff; }
      .green-segment { background: #44ff44; }

      .slider-track {
        position: relative;
        height: 30px;
        background: linear-gradient(to right, #ff4444, #ff8800, #888888, #4488ff, #44ff44);
        border-radius: 15px;
        margin: 10px 0;
      }

      .thumb {
        position: absolute;
        width: 20px;
        height: 20px;
        background: #ffffff;
        border: 3px solid #000000;
        border-radius: 50%;
        top: 5px;
        cursor: grab;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
        transition: transform 0.2s ease;
      }

      .thumb:hover {
        transform: scale(1.1);
      }

      .thumb.dragging {
        cursor: grabbing;
        transform: scale(1.2);
        z-index: 10;
      }

      .red-thumb { border-color: #ff0000; }
      .orange-thumb { border-color: #ff8800; }
      .blue-thumb { border-color: #0088ff; }
      .green-thumb { border-color: #00ff00; }

      .value-labels {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
      }

      .value-label {
        font-size: 0.8rem;
        font-weight: bold;
        padding: 2px 6px;
        border-radius: 3px;
        color: white;
      }

      .red-value { background: #ff4444; }
      .orange-value { background: #ff8800; }
      .blue-value { background: #4488ff; }
      .green-value { background: #44ff44; }

      .threshold-actions {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
      }

      .reset-button, .apply-button {
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .reset-button {
        background: #ff6b6b;
        color: white;
      }

      .reset-button:hover {
        background: #ff5252;
        transform: translateY(-1px);
      }

      .apply-button {
        background: #4ecdc4;
        color: white;
      }

      .apply-button:hover {
        background: #26a69a;
        transform: translateY(-1px);
      }
    `;

    // Remove existing style if present
    const existingStyle = document.getElementById('threshold-slider-fix-css');
    if (existingStyle) existingStyle.remove();

    document.head.appendChild(style);
  }
}

// Initialize threshold slider fix
document.addEventListener('DOMContentLoaded', () => {
  window.thresholdSliderFix = new ThresholdSliderFix();
});

if (document.readyState !== 'loading') {
  window.thresholdSliderFix = new ThresholdSliderFix();
}
