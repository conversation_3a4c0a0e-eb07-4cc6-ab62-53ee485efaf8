/**
 * Historical Data Loader
 * Loads and processes historical data from CSV files for use in charts and indicators
 */

(function() {
  'use strict';

  // Map of timeframe codes to their corresponding CSV files
  const timeframeToFile = {
    '1m': 'XBTUSDT_1.csv',
    '5m': 'XBTUSDT_5.csv',
    '15m': 'XBTUSDT_15.csv',
    '30m': 'XBTUSDT_30.csv',
    '1h': 'XBTUSDT_60.csv',
    '4h': 'XBTUSDT_240.csv',
    '12h': 'XBTUSDT_720.csv',
    '1d': 'XBTUSDT_1440.csv'
  };

  /**
   * Load historical data from CSV file
   * @param {string} timeframe - Timeframe to load data for (e.g., '15m', '1h')
   * @param {number} limit - Maximum number of data points to load
   * @returns {Promise<Array>} - Promise resolving to array of processed data points
   */
  async function loadHistoricalData(timeframe = '15m', limit = 50) {
    try {
      // Default to 15m if timeframe not found
      const filename = timeframeToFile[timeframe] || 'XBTUSDT_15.csv';
      const response = await fetch(`/data/${filename}`);
      
      if (!response.ok) {
        throw new Error(`Failed to load historical data: ${response.status} ${response.statusText}`);
      }
      
      const text = await response.text();
      const lines = text.trim().split('\n');
      
      // Take the most recent data points up to the limit
      const dataPoints = lines.slice(-limit).map(line => {
        const [timestamp, open, high, low, close, volume] = line.split(',');
        return {
          timestamp: parseInt(timestamp) * 1000, // Convert to milliseconds
          open: parseFloat(open),
          high: parseFloat(high),
          low: parseFloat(low),
          close: parseFloat(close),
          volume: parseFloat(volume)
        };
      });
      
      return dataPoints;
    } catch (error) {
      console.error('Error loading historical data:', error);
      return [];
    }
  }

  /**
   * Calculate indicators from historical data
   * @param {Array} dataPoints - Array of OHLCV data points
   * @returns {Object} - Object with calculated indicators
   */
  function calculateIndicators(dataPoints) {
    if (!dataPoints || dataPoints.length === 0) {
      return {};
    }
    
    // Calculate simple indicators
    const lastPoint = dataPoints[dataPoints.length - 1];
    const prices = dataPoints.map(p => p.close);
    
    // Simple Moving Average (14)
    const sma14 = prices.slice(-14).reduce((sum, price) => sum + price, 0) / 14;
    
    // Calculate RSI
    let gains = 0;
    let losses = 0;
    
    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      if (change >= 0) {
        gains += change;
      } else {
        losses -= change;
      }
    }
    
    const avgGain = gains / (prices.length - 1);
    const avgLoss = losses / (prices.length - 1);
    const rs = avgLoss > 0 ? avgGain / avgLoss : 0;
    const rsi = 100 - (100 / (1 + rs));
    
    // Calculate ATR
    let atr = 0;
    if (dataPoints.length > 1) {
      let trSum = 0;
      for (let i = 1; i < dataPoints.length; i++) {
        const high = dataPoints[i].high;
        const low = dataPoints[i].low;
        const prevClose = dataPoints[i - 1].close;
        
        const tr1 = high - low;
        const tr2 = Math.abs(high - prevClose);
        const tr3 = Math.abs(low - prevClose);
        
        const tr = Math.max(tr1, tr2, tr3);
        trSum += tr;
      }
      atr = trSum / (dataPoints.length - 1);
    }
    
    // Calculate MACD
    const ema12 = calculateEMA(prices, 12);
    const ema26 = calculateEMA(prices, 26);
    const macd = ema12 - ema26;
    
    return {
      currentPrice: lastPoint.close,
      timestamp: lastPoint.timestamp,
      rsi: { value: rsi },
      macd: { value: macd },
      atr: { value: atr },
      volume: { value: lastPoint.volume },
      sma: { value: sma14 }
    };
  }
  
  /**
   * Calculate Exponential Moving Average
   * @param {Array} prices - Array of price values
   * @param {number} period - EMA period
   * @returns {number} - Calculated EMA value
   */
  function calculateEMA(prices, period) {
    if (!Array.isArray(prices) || prices.length === 0) {
      return 0;
    }
    
    const k = 2 / (period + 1);
    let ema = prices[0];
    
    for (let i = 1; i < prices.length; i++) {
      ema = prices[i] * k + ema * (1 - k);
    }
    
    return ema;
  }

  /**
   * Process historical data for ML Analysis Chart
   * @param {string} timeframe - Timeframe to load data for
   * @param {number} limit - Maximum number of data points to load
   * @returns {Promise<Array>} - Promise resolving to array of processed data points for ML chart
   */
  async function loadDataForMLChart(timeframe = '15m', limit = 50) {
    try {
      const dataPoints = await loadHistoricalData(timeframe, limit);
      
      if (!dataPoints || dataPoints.length === 0) {
        console.error('No historical data available for ML chart');
        return [];
      }
      
      // Process data points for ML chart
      return dataPoints.map(point => {
        // Calculate indicators for each point
        const indicators = calculateIndicators([point]);
        
        return {
          timestamp: point.timestamp,
          currentPrice: point.close,
          rsi: { value: indicators.rsi?.value || 50 },
          macd: { value: indicators.macd?.value || 0 },
          atr: { value: indicators.atr?.value || 1 },
          volume: point.volume,
          timeframe: timeframe
        };
      });
    } catch (error) {
      console.error('Error processing data for ML chart:', error);
      return [];
    }
  }

  // Make functions available globally
  window.historicalData = {
    loadHistoricalData,
    calculateIndicators,
    loadDataForMLChart
  };
})();
