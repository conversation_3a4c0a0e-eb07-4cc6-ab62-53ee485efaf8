# StarCrypt Call Tree Analysis

## Entry Points

- Backups\05.06 Gemini temporary broken backup\index.html
- Backups\05.06 Gemini temporary broken backup\js\app.js
- Backups\05.06 Gemini temporary broken backup\js\init-app.js
- Backups\05.06 Gemini temporary broken backup\js\main.js
- Backups\12.06 cant get lights back\index.html
- Backups\12.06 cant get lights back\js\init-app.js
- Backups\12.06 cant get lights back\js\main.js
- Backups\StarCrypt 01.06 slimming the errors down shit working better\index.html
- Backups\StarCrypt 01.06 slimming the errors down shit working better\js\app.js
- Backups\StarCrypt 01.06 slimming the errors down shit working better\js\init-app.js
- Backups\StarCrypt 01.06 slimming the errors down shit working better\js\main.js
- Backups\StarCrypt 21.05\index.html
- Backups\StarCrypt 21.05\js\main.js
- Backups\StarCrypt 25.06 success\index.html
- Backups\StarCrypt 25.06 success\js\app.js
- Backups\StarCrypt 25.06 success\js\init-app.js
- Backups\StarCrypt 25.06 success\js\main.js
- index.html
- js\app.js
- js\init-app.js
- js\main.js

## Dependencies

### Backups\05.06 Gemini temporary broken backup\data-fetcher.js
- node-fetch
- ./config

### Backups\05.06 Gemini temporary broken backup\indicators.js
- ./config

### Backups\05.06 Gemini temporary broken backup\js\ai\ai-engine.js
- @tensorflow/tfjs-node
- technicalindicators

### Backups\05.06 Gemini temporary broken backup\js\ai\backtester.js
- @tensorflow/tfjs-node
- fs
- path
- ./market-data
- ./ai-engine

### Backups\05.06 Gemini temporary broken backup\js\ai\index.js
- ./ai-engine
- ./strategy-manager
- ./market-data
- ./backtester

### Backups\05.06 Gemini temporary broken backup\js\ai\market-data.js
- @tensorflow/tfjs-node
- technicalindicators
- ../../server

### Backups\05.06 Gemini temporary broken backup\js\ai\strategy-manager.js
- ./ai-engine
- ../../server

### Backups\05.06 Gemini temporary broken backup\js\ai\utils.js
- @tensorflow/tfjs-node
- path
- fs
- ../../config/ai-config
- perf_hooks

### Backups\05.06 Gemini temporary broken backup\js\app.js
- ./services/AIIntegrationService.js
- ./components/PriceChart.js

### Backups\05.06 Gemini temporary broken backup\js\init-ai-dashboard.js
- ./components/AIDashboard.js

### Backups\05.06 Gemini temporary broken backup\js\services\AIIntegrationService.js
- ./MarketDataService.js
- ./AIAnalysisService.js
- ../components/AIDashboard.js

### Backups\05.06 Gemini temporary broken backup\krakenWsClient.js
- ws
- events

### Backups\05.06 Gemini temporary broken backup\server.js
- express
- ws
- node-fetch
- url
- ./krakenWsClient.js
- XBT/USD
- XBT/USDT

### Backups\05.06 Gemini temporary broken backup\websocket-server.js
- ws
- url
- uuid
- ./data-fetcher
- ./indicators

### Backups\12.06 cant get lights back\data-fetcher.js
- node-fetch
- ./config

### Backups\12.06 cant get lights back\eslint.config.js
- @eslint/js
- globals

### Backups\12.06 cant get lights back\indicators.js
- ./config

### Backups\12.06 cant get lights back\js\for deletion- menu\indicator-menu.js
- ./menu-controller.js
- ./menu-utils.js
- ../indicator-manager.js
- ../utils/error-handler.js
- ../utils/performance-monitor.js

### Backups\12.06 cant get lights back\js\for deletion- menu\logic-manager.js
- ../utils/logger.js

### Backups\12.06 cant get lights back\js\for deletion- menu\logic-menu.js
- ./menu-controller.js
- ./menu-utils.js
- ../logic-manager.js
- ../utils/error-handler.js
- ../utils/performance-monitor.js

### Backups\12.06 cant get lights back\js\for deletion- menu\menu-controller.js
- ./menu-state.js
- ./menu-events.js
- ./menu-utils.js
- ../utils/error-handler.js
- ../utils/performance-monitor.js

### Backups\12.06 cant get lights back\js\for deletion- menu\menu-events.js
- ../utils/event-dispatcher.js

### Backups\12.06 cant get lights back\js\for deletion- menu\menu-state.js
- ../utils/state-manager.js
- ./menu-events.js

### Backups\12.06 cant get lights back\js\for deletion- menu\menu-utils.js
- ../utils/dom-utils.js
- ../utils/animation-utils.js
- ../utils/performance-monitor.js
- ../utils/error-handler.js

### Backups\12.06 cant get lights back\js\for deletion- menu\storage-manager.js
- ../utils/logger.js

### Backups\12.06 cant get lights back\js\for deletion- menu\strategy-menu.js
- ./menu-controller.js
- ./menu-utils.js
- ../strategy-manager.js
- ../utils/error-handler.js
- ../utils/performance-monitor.js

### Backups\12.06 cant get lights back\js\for deletion- menu\threshold-manager.js
- ../utils/logger.js

### Backups\12.06 cant get lights back\js\for deletion- menu\threshold-menu.js
- ./menu-controller.js
- ./menu-utils.js
- ../threshold-manager.js
- ../utils/error-handler.js
- ../utils/performance-monitor.js

### Backups\12.06 cant get lights back\js\for deletion- menu\timeframe-menu.js
- ./menu-controller.js
- ./menu-utils.js
- ../timeframe-manager.js
- ../utils/error-handler.js
- ../utils/performance-monitor.js

### Backups\12.06 cant get lights back\js\init-ai-dashboard.js
- ./components/AIDashboard.js

### Backups\12.06 cant get lights back\js\ui\signal-matrix.js
- indicator-row-rsi

### Backups\12.06 cant get lights back\krakenWsClient.js
- ws
- events

### Backups\12.06 cant get lights back\server.js
- express
- ws
- node-fetch
- url
- ./krakenWsClient.js
- XBT/USD
- XBT/USDT

### Backups\12.06 cant get lights back\websocket-server.js
- ws
- url
- uuid
- ./data-fetcher
- ./indicators

### Backups\StarCrypt 01.06 slimming the errors down shit working better\data-fetcher.js
- node-fetch
- ./config

### Backups\StarCrypt 01.06 slimming the errors down shit working better\indicators.js
- ./config

### Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ai\ai-engine.js
- @tensorflow/tfjs-node
- technicalindicators

### Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ai\backtester.js
- @tensorflow/tfjs-node
- fs
- path
- ./market-data
- ./ai-engine

### Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ai\index.js
- ./ai-engine
- ./strategy-manager
- ./market-data
- ./backtester

### Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ai\market-data.js
- @tensorflow/tfjs-node
- technicalindicators
- ../../server

### Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ai\strategy-manager.js
- ./ai-engine
- ../../server

### Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ai\utils.js
- @tensorflow/tfjs-node
- path
- fs
- ../../config/ai-config
- perf_hooks

### Backups\StarCrypt 01.06 slimming the errors down shit working better\js\app.js
- ./services/AIIntegrationService.js
- ./components/PriceChart.js

### Backups\StarCrypt 01.06 slimming the errors down shit working better\js\init-ai-dashboard.js
- ./components/AIDashboard.js

### Backups\StarCrypt 01.06 slimming the errors down shit working better\js\services\AIIntegrationService.js
- ./MarketDataService.js
- ./AIAnalysisService.js
- ../components/AIDashboard.js

### Backups\StarCrypt 01.06 slimming the errors down shit working better\server.js
- express
- ws
- node-fetch
- url

### Backups\StarCrypt 01.06 slimming the errors down shit working better\websocket-server.js
- ws
- url
- uuid
- ./data-fetcher
- ./indicators

### Backups\StarCrypt 21.05\server.js
- express
- ws
- node-fetch
- url

### Backups\StarCrypt 25.06 success\check.js
- fs

### Backups\StarCrypt 25.06 success\data-fetcher.js
- node-fetch
- ./config

### Backups\StarCrypt 25.06 success\indicators.js
- ./config

### Backups\StarCrypt 25.06 success\js\ai\ai-engine.js
- @tensorflow/tfjs-node
- technicalindicators

### Backups\StarCrypt 25.06 success\js\ai\backtester.js
- @tensorflow/tfjs-node
- fs
- path
- ./market-data
- ./ai-engine

### Backups\StarCrypt 25.06 success\js\ai\index.js
- ./ai-engine
- ./strategy-manager
- ./market-data
- ./backtester

### Backups\StarCrypt 25.06 success\js\ai\market-data.js
- @tensorflow/tfjs-node
- technicalindicators
- ../../server

### Backups\StarCrypt 25.06 success\js\ai\strategy-manager.js
- ./ai-engine
- ../../server

### Backups\StarCrypt 25.06 success\js\ai\utils.js
- @tensorflow/tfjs-node
- path
- fs
- ../../config/ai-config
- perf_hooks

### Backups\StarCrypt 25.06 success\js\app.js
- ./services/AIIntegrationService.js
- ./components/PriceChart.js

### Backups\StarCrypt 25.06 success\js\init-ai-dashboard.js
- ./components/AIDashboard.js

### Backups\StarCrypt 25.06 success\js\services\AIIntegrationService.js
- ./MarketDataService.js
- ./AIAnalysisService.js
- ../components/AIDashboard.js

### Backups\StarCrypt 25.06 success\js\syntax-check.js
- fs
- path

### Backups\StarCrypt 25.06 success\server.js
- express
- ws
- node-fetch
- url
- path
- fs

### Backups\StarCrypt 25.06 success\websocket-server.js
- ws
- url
- uuid
- ./data-fetcher
- ./indicators

### check.js
- fs

### convert-csv-to-json.js
- fs
- path

### data-fetcher.js
- node-fetch
- ./config

### generate-call-tree.js
- fs
- path

### indicators.js
- ./config

### js\ai\ai-engine.js
- @tensorflow/tfjs-node
- technicalindicators

### js\ai\backtester.js
- @tensorflow/tfjs-node
- fs
- path
- ./market-data
- ./ai-engine

### js\ai\index.js
- ./ai-engine
- ./strategy-manager
- ./market-data
- ./backtester

### js\ai\market-data.js
- @tensorflow/tfjs-node
- technicalindicators
- ../../server

### js\ai\strategy-manager.js
- ./ai-engine
- ../../server

### js\ai\utils.js
- @tensorflow/tfjs-node
- path
- fs
- ../../config/ai-config
- perf_hooks

### js\app.js
- ./services/AIIntegrationService.js
- ./components/PriceChart.js

### js\init-ai-dashboard.js
- ./components/AIDashboard.js

### js\services\AIIntegrationService.js
- ./MarketDataService.js
- ./AIAnalysisService.js
- ../components/AIDashboard.js

### js\syntax-check.js
- fs
- path

### server.js
- express
- ws
- node-fetch
- url
- path
- fs
- ./js/services/sentiment-api

### websocket-server.js
- ws
- url
- uuid
- ./data-fetcher
- ./indicators


## Strategy Selector References

- Backups\05.06 Gemini temporary broken backup\index.html (.html)
- Backups\05.06 Gemini temporary broken backup\js\global-variables.js (.js)
- Backups\05.06 Gemini temporary broken backup\js\main.js (.js)
- Backups\05.06 Gemini temporary broken backup\js\menu-system.js (.js)
- Backups\05.06 Gemini temporary broken backup\js\ui\event-handlers.js (.js)
- Backups\05.06 Gemini temporary broken backup\js\ui\menu-strategy-fix.js (.js)
- Backups\05.06 Gemini temporary broken backup\js\ui\strategy-comprehensive-fix.js (.js)
- Backups\05.06 Gemini temporary broken backup\js\ui\strategy-consolidated.js (.js)
- Backups\05.06 Gemini temporary broken backup\js\ui\strategy-fix.js (.js)
- Backups\05.06 Gemini temporary broken backup\js\ui\strategy-manager.js (.js)
- Backups\05.06 Gemini temporary broken backup\js\ui\strategy-persistence.js (.js)
- Backups\05.06 Gemini temporary broken backup\js\ui\strategy-selector.js (.js)
- Backups\12.06 cant get lights back\index.html (.html)
- Backups\12.06 cant get lights back\js\global-variables.js (.js)
- Backups\12.06 cant get lights back\js\main.js (.js)
- Backups\12.06 cant get lights back\js\ui\event-handlers.js (.js)
- Backups\12.06 cant get lights back\js\ui\menu-strategy-fix.js (.js)
- Backups\12.06 cant get lights back\js\ui\strategy-comprehensive-fix.js (.js)
- Backups\12.06 cant get lights back\js\ui\strategy-fix.js (.js)
- Backups\12.06 cant get lights back\js\ui\strategy-persistence.js (.js)
- Backups\12.06 cant get lights back\js\ui\strategy-selector.js (.js)
- Backups\StarCrypt 01.06 slimming the errors down shit working better\index.html (.html)
- Backups\StarCrypt 01.06 slimming the errors down shit working better\js\global-variables.js (.js)
- Backups\StarCrypt 01.06 slimming the errors down shit working better\js\main.js (.js)
- Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\event-handlers.js (.js)
- Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\menu-strategy-fix.js (.js)
- Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\strategy-comprehensive-fix.js (.js)
- Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\strategy-fix.js (.js)
- Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\strategy-persistence.js (.js)
- Backups\StarCrypt 01.06 slimming the errors down shit working better\js\ui\strategy-selector.js (.js)
- Backups\StarCrypt 21.05\index.html (.html)
- Backups\StarCrypt 21.05\js\global-variables.js (.js)
- Backups\StarCrypt 21.05\js\main.js (.js)
- Backups\StarCrypt 21.05\js\ui\event-handlers.js (.js)
- Backups\StarCrypt 21.05\js\ui\menu-strategy-fix.js (.js)
- Backups\StarCrypt 21.05\js\ui\strategy-comprehensive-fix.js (.js)
- Backups\StarCrypt 21.05\js\ui\strategy-fix.js (.js)
- Backups\StarCrypt 21.05\js\ui\strategy-persistence.js (.js)
- Backups\StarCrypt 21.05\js\ui\strategy-selector.js (.js)
- Backups\StarCrypt 25.06 success\index.html (.html)
- Backups\StarCrypt 25.06 success\js\global-variables.js (.js)
- Backups\StarCrypt 25.06 success\js\main.js (.js)
- Backups\StarCrypt 25.06 success\js\ui\event-handlers.js (.js)
- Backups\StarCrypt 25.06 success\js\ui\strategy-persistence.js (.js)
- Backups\StarCrypt 25.06 success\js\ui\strategy-selector.js (.js)
- generate-call-tree.js (.js)
- index.html (.html)
- js\global-variables.js (.js)
- js\main.js (.js)
- js\ui\event-handlers.js (.js)
- js\ui\menu-controller.js (.js)
- js\ui\strategy-persistence.js (.js)
- js\ui\strategy-selector.js (.js)
