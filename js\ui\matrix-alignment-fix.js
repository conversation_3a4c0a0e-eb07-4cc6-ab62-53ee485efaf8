/**
 * Matrix Alignment Fix - Ensures proper alignment on page load
 */

class MatrixAlignmentFix {
  constructor() {
    this.alignmentChecked = false;
    this.retryCount = 0;
    this.maxRetries = 10;
    this.init();
  }

  init() {
    console.log('[MatrixAlignmentFix] 🔧 Initializing matrix alignment fix...');
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.startAlignmentCheck());
    } else {
      this.startAlignmentCheck();
    }
  }

  startAlignmentCheck() {
    // Wait for initial DOM setup
    setTimeout(() => {
      this.checkAndFixAlignment();
    }, 500);
    
    // Also check after WebSocket connection
    this.waitForWebSocketAndCheck();
    
    // Set up periodic checks until alignment is confirmed
    this.setupPeriodicCheck();
  }

  waitForWebSocketAndCheck() {
    const checkWebSocket = () => {
      if (window.ws && window.ws.readyState === WebSocket.OPEN) {
        setTimeout(() => {
          this.checkAndFixAlignment();
        }, 1000);
      } else {
        setTimeout(checkWebSocket, 500);
      }
    };
    checkWebSocket();
  }

  setupPeriodicCheck() {
    const periodicCheck = setInterval(() => {
      if (this.alignmentChecked || this.retryCount >= this.maxRetries) {
        clearInterval(periodicCheck);
        return;
      }
      
      this.checkAndFixAlignment();
      this.retryCount++;
    }, 2000);
  }

  checkAndFixAlignment() {
    console.log('[MatrixAlignmentFix] 🔍 Checking matrix alignment...');
    
    const momentumTable = document.querySelector('#momentum-table, .indicators-table');
    if (!momentumTable) {
      console.warn('[MatrixAlignmentFix] Momentum table not found');
      return;
    }

    // Check if table has proper structure
    const rows = momentumTable.querySelectorAll('tr');
    if (rows.length === 0) {
      console.log('[MatrixAlignmentFix] Table empty, triggering rebuild...');
      this.triggerTableRebuild();
      return;
    }

    // Check for proper signal circles
    const signalCircles = momentumTable.querySelectorAll('.signal-circle, .circle');
    if (signalCircles.length === 0) {
      console.log('[MatrixAlignmentFix] No signal circles found, triggering rebuild...');
      this.triggerTableRebuild();
      return;
    }

    // Check for proper alignment
    if (!this.isProperlyAligned(momentumTable)) {
      console.log('[MatrixAlignmentFix] Alignment issues detected, fixing...');
      this.fixAlignment(momentumTable);
    } else {
      console.log('[MatrixAlignmentFix] ✅ Matrix alignment is correct');
      this.alignmentChecked = true;
    }
  }

  isProperlyAligned(table) {
    const rows = table.querySelectorAll('tr');
    if (rows.length < 2) return false; // Need at least header + one indicator row
    
    // Check if all rows have consistent column count
    let expectedColumns = 0;
    rows.forEach((row, index) => {
      const cells = row.querySelectorAll('td, th');
      if (index === 0) {
        expectedColumns = cells.length;
      } else if (cells.length !== expectedColumns) {
        return false;
      }
    });
    
    // Check if signal circles are properly positioned
    const signalCells = table.querySelectorAll('.signals-cell');
    if (signalCells.length === 0) return false;
    
    // Check if mini charts have proper height
    const miniCharts = table.querySelectorAll('.mini-chart, [id*="Chart"]');
    let hasProperHeight = true;
    miniCharts.forEach(chart => {
      const height = chart.offsetHeight;
      if (height < 35 || height > 45) { // Should be around 40px
        hasProperHeight = false;
      }
    });
    
    return hasProperHeight;
  }

  fixAlignment(table) {
    console.log('[MatrixAlignmentFix] 🔧 Fixing table alignment...');
    
    // Fix table structure
    this.fixTableStructure(table);
    
    // Fix signal circle alignment
    this.fixSignalCircleAlignment(table);
    
    // Fix mini chart heights
    this.fixMiniChartHeights(table);
    
    // Fix row heights
    this.fixRowHeights(table);
    
    // Apply alignment CSS
    this.applyAlignmentCSS();
    
    console.log('[MatrixAlignmentFix] ✅ Alignment fixes applied');
  }

  fixTableStructure(table) {
    // Ensure table has proper CSS classes
    table.classList.add('indicators-table', 'matrix-aligned');
    
    // Fix table layout
    table.style.cssText = `
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;
    `;
    
    // Fix header row
    const headerRow = table.querySelector('tr');
    if (headerRow) {
      headerRow.style.cssText = `
        height: 50px;
        background: rgba(0, 20, 40, 0.8);
        border-bottom: 2px solid rgba(0, 255, 255, 0.3);
      `;
    }
  }

  fixSignalCircleAlignment(table) {
    const signalCells = table.querySelectorAll('.signals-cell');
    
    signalCells.forEach(cell => {
      cell.style.cssText = `
        text-align: center;
        vertical-align: middle;
        padding: 5px;
        width: 40px;
        min-width: 40px;
        max-width: 40px;
      `;
      
      // Fix signal circles within cells
      const circles = cell.querySelectorAll('.signal-circle, .circle');
      circles.forEach(circle => {
        circle.style.cssText = `
          width: 20px;
          height: 20px;
          border-radius: 50%;
          display: inline-block;
          margin: 2px;
          border: 1px solid rgba(255, 255, 255, 0.3);
          transition: all 0.3s ease;
        `;
      });
    });
  }

  fixMiniChartHeights(table) {
    const miniCharts = table.querySelectorAll('.mini-chart, [id*="Chart"], canvas[id*="chart"]');
    
    miniCharts.forEach(chart => {
      chart.style.height = '40px';
      chart.style.maxHeight = '40px';
      chart.style.minHeight = '40px';
      
      if (chart.tagName === 'CANVAS') {
        chart.height = 40;
      }
      
      // Fix parent cell
      const parentCell = chart.closest('td');
      if (parentCell) {
        parentCell.style.cssText = `
          height: 50px;
          vertical-align: middle;
          padding: 5px;
        `;
      }
    });
  }

  fixRowHeights(table) {
    const rows = table.querySelectorAll('tr');
    
    rows.forEach((row, index) => {
      if (index === 0) {
        // Header row
        row.style.height = '50px';
      } else {
        // Data rows
        row.style.height = '50px';
        row.style.cssText += `
          border-bottom: 1px solid rgba(0, 255, 255, 0.1);
        `;
      }
      
      // Fix all cells in row
      const cells = row.querySelectorAll('td, th');
      cells.forEach(cell => {
        cell.style.cssText += `
          vertical-align: middle;
          padding: 5px;
          height: 50px;
        `;
      });
    });
  }

  triggerTableRebuild() {
    console.log('[MatrixAlignmentFix] 🔄 Triggering table rebuild...');
    
    // Try to trigger strategy change to rebuild table
    if (typeof window.changeStrategy === 'function') {
      const currentStrategy = window.currentStrategy || 'momentum_blast';
      
      // Temporarily change to different strategy and back
      setTimeout(() => {
        window.changeStrategy('admiral_toa');
        setTimeout(() => {
          window.changeStrategy(currentStrategy);
          setTimeout(() => {
            this.checkAndFixAlignment();
          }, 1000);
        }, 500);
      }, 100);
    } else if (typeof window.initializeSignalMatrix === 'function') {
      // Try to reinitialize signal matrix
      setTimeout(() => {
        window.initializeSignalMatrix();
        setTimeout(() => {
          this.checkAndFixAlignment();
        }, 1000);
      }, 100);
    } else {
      // Force page refresh as last resort
      console.warn('[MatrixAlignmentFix] No rebuild methods available, manual refresh may be needed');
    }
  }

  applyAlignmentCSS() {
    const style = document.createElement('style');
    style.id = 'matrix-alignment-fix-css';
    style.textContent = `
      /* Matrix Alignment Fix CSS */
      .indicators-table.matrix-aligned {
        width: 100% !important;
        border-collapse: collapse !important;
        table-layout: fixed !important;
      }

      .indicators-table.matrix-aligned tr {
        height: 50px !important;
      }

      .indicators-table.matrix-aligned td,
      .indicators-table.matrix-aligned th {
        vertical-align: middle !important;
        padding: 5px !important;
        height: 50px !important;
        box-sizing: border-box !important;
      }

      .indicators-table.matrix-aligned .signals-cell {
        text-align: center !important;
        width: 40px !important;
        min-width: 40px !important;
        max-width: 40px !important;
      }

      .indicators-table.matrix-aligned .signal-circle,
      .indicators-table.matrix-aligned .circle {
        width: 20px !important;
        height: 20px !important;
        border-radius: 50% !important;
        display: inline-block !important;
        margin: 2px !important;
      }

      .indicators-table.matrix-aligned .mini-chart,
      .indicators-table.matrix-aligned [id*="Chart"],
      .indicators-table.matrix-aligned canvas[id*="chart"] {
        height: 40px !important;
        max-height: 40px !important;
        min-height: 40px !important;
      }

      .indicators-table.matrix-aligned .indicator-name {
        font-weight: bold !important;
        color: #00ccff !important;
        text-align: left !important;
        padding-left: 10px !important;
      }

      /* Ensure proper spacing */
      .indicators-table.matrix-aligned tbody tr:hover {
        background: rgba(0, 255, 255, 0.05) !important;
      }
    `;

    // Remove existing style if present
    const existingStyle = document.getElementById('matrix-alignment-fix-css');
    if (existingStyle) existingStyle.remove();

    document.head.appendChild(style);
  }

  // Public method to manually trigger alignment check
  forceAlignmentCheck() {
    this.alignmentChecked = false;
    this.retryCount = 0;
    this.checkAndFixAlignment();
  }
}

// Initialize the matrix alignment fix
document.addEventListener('DOMContentLoaded', () => {
  window.matrixAlignmentFix = new MatrixAlignmentFix();
});

// Also initialize if DOM is already loaded
if (document.readyState !== 'loading') {
  window.matrixAlignmentFix = new MatrixAlignmentFix();
}
