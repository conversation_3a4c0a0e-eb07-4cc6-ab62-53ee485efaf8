/**
 * ML Historical Analysis Features for StarCrypt
 * History search with golden highlights, convergence analysis, Fibonacci, and time-based predictions
 */

class MLHistoricalAnalysis {
  constructor() {
    this.selectedLights = new Set();
    this.historicalData = [];
    this.convergenceEvents = [];
    this.fibonacciLevels = [];
    this.timeBasedPredictions = {
      today: null,
      yesterday: null,
      lastWeek: null,
      lastMonth: null,
      all: null
    };

    this.analysisOptions = {
      searchDepth: 100, // Number of historical points to analyze
      convergenceThreshold: 0.8, // Minimum correlation for convergence
      fibonacciEnabled: true,
      goldenHighlights: true,
      autoAnalysis: true
    };

    this.init();
  }

  init() {
    console.log('[MLHistoricalAnalysis] Initializing historical analysis features...');
    
    // Apply styles first
    this.applyHistoricalAnalysisStyles();
    
    try {
      // Create the panel UI
      this.createHistoricalAnalysisPanel();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Initialize analysis features
      this.setupLightSelectionSystem();
      this.setupFibonacciAnalysis();
      this.initializeTimeBasedPredictions();
      
      // Start with initial analysis
      this.startHistoricalAnalysis();
      
      console.log('[MLHistoricalAnalysis] Historical analysis features initialized successfully');
    } catch (error) {
      console.error('[MLHistoricalAnalysis] Error initializing historical analysis:', error);
    }
  }

  createHistoricalAnalysisPanel() {
    console.log('[MLHistoricalAnalysis] Creating historical analysis panel...');
    
    // Get or create the container
    let analysisContainer = document.querySelector('#ml-historical-analysis');
    if (!analysisContainer) {
      console.warn('[MLHistoricalAnalysis] Creating new historical analysis container');
      analysisContainer = document.createElement('div');
      analysisContainer.id = 'ml-historical-analysis';
      analysisContainer.className = 'ml-historical-container';
      
      // Find the best place to insert the container
      const momentumIndicators = document.querySelector('.momentum-indicators');
      if (momentumIndicators) {
        momentumIndicators.appendChild(analysisContainer);
      } else {
        console.error('[MLHistoricalAnalysis] Could not find momentum-indicators container');
        return;
      }
    }
    
    // Store reference to the container
    this.container = analysisContainer;

    analysisContainer.innerHTML = `
      <div class="historical-analysis-header">
        <h3>📊 Historical Analysis & Convergence Engine</h3>
        <button class="analysis-toggle-button" id="toggleHistoricalAnalysis">🔍 Analyze</button>
      </div>

      <div class="historical-analysis-content" id="historicalAnalysisContent">
        <div class="light-selection-panel">
          <h4>💡 Signal Light Selection</h4>
          <div class="selection-instructions">
            Click signal lights to select them for convergence analysis. Selected lights will have golden glowing trim.
          </div>
          <div class="selected-lights-display" id="selectedLightsDisplay">
            <span class="no-selection">No lights selected</span>
          </div>
          <div class="selection-controls">
            <button class="clear-selection-btn" id="clearSelection">Clear Selection</button>
            <button class="analyze-convergence-btn" id="analyzeConvergence">🔍 Analyze Convergence</button>
          </div>
        </div>

        <div class="convergence-results-panel">
          <h4>🎯 Convergence Analysis Results</h4>
          <div class="convergence-stats" id="convergenceStats">
            <div class="stat-item">
              <span class="stat-label">Total Convergences:</span>
              <span class="stat-value" id="totalConvergences">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Green Convergences:</span>
              <span class="stat-value green" id="greenConvergences">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Red Convergences:</span>
              <span class="stat-value red" id="redConvergences">0</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">Success Rate:</span>
              <span class="stat-value" id="successRate">0%</span>
            </div>
          </div>
          <div class="convergence-timeline" id="convergenceTimeline">
            <div class="timeline-placeholder">Select lights and analyze to see convergence timeline</div>
          </div>
        </div>

        <div class="fibonacci-panel">
          <h4>📐 Fibonacci Analysis</h4>
          <div class="fibonacci-controls">
            <label class="fibonacci-toggle">
              <input type="checkbox" id="fibonacciEnabled" checked>
              <span class="checkbox-custom"></span>
              Enable Fibonacci Analysis
            </label>
            <button class="calculate-fibonacci-btn" id="calculateFibonacci">📊 Calculate Levels</button>
          </div>
          <div class="fibonacci-levels" id="fibonacciLevels">
            <div class="fibonacci-placeholder">Enable Fibonacci analysis to see retracement levels</div>
          </div>
        </div>

        <div class="time-predictions-panel">
          <h4>⏰ Time-Based Predictions</h4>
          <div class="prediction-dropdown">
            <select id="timePredictionSelect">
              <option value="today">Best Prediction Today</option>
              <option value="yesterday">Best Prediction Yesterday</option>
              <option value="lastWeek">Best Prediction Last Week</option>
              <option value="lastMonth">Best Prediction Last Month</option>
              <option value="all">Best Prediction All Time</option>
            </select>
            <button class="load-prediction-btn" id="loadTimePrediction">📈 Load Prediction</button>
          </div>
          <div class="prediction-display" id="timePredictionDisplay">
            <div class="prediction-placeholder">Select a time period to see the best prediction</div>
          </div>
        </div>

        <div class="advanced-options-panel">
          <h4>⚙️ Advanced Options</h4>
          <div class="option-row">
            <label>Search Depth:</label>
            <input type="range" id="searchDepthSlider" min="50" max="500" step="50" value="${this.analysisOptions.searchDepth}">
            <span class="option-value">${this.analysisOptions.searchDepth} points</span>
          </div>
          <div class="option-row">
            <label>Convergence Threshold:</label>
            <input type="range" id="convergenceThresholdSlider" min="0.5" max="1.0" step="0.1" value="${this.analysisOptions.convergenceThreshold}">
            <span class="option-value">${(this.analysisOptions.convergenceThreshold * 100).toFixed(0)}%</span>
          </div>
          <div class="option-row">
            <label class="option-toggle">
              <input type="checkbox" id="autoAnalysisToggle" ${this.analysisOptions.autoAnalysis ? 'checked' : ''}>
              <span class="checkbox-custom"></span>
              Auto Analysis
            </label>
          </div>
        </div>
      </div>
    `;

    this.setupHistoricalAnalysisEvents();
    this.applyHistoricalAnalysisStyles();
  }

  setupHistoricalAnalysisEvents() {
    // Toggle analysis panel
    const toggleBtn = document.getElementById('toggleHistoricalAnalysis');
    const content = document.getElementById('historicalAnalysisContent');

    if (toggleBtn && content) {
      toggleBtn.addEventListener('click', () => {
        content.classList.toggle('collapsed');
        toggleBtn.textContent = content.classList.contains('collapsed') ? '🔍 Analyze' : '❌ Close';
      });
    }

    // Clear selection
    const clearBtn = document.getElementById('clearSelection');
    if (clearBtn) {
      clearBtn.addEventListener('click', () => {
        this.clearLightSelection();
      });
    }

    // Analyze convergence
    const analyzeBtn = document.getElementById('analyzeConvergence');
    if (analyzeBtn) {
      analyzeBtn.addEventListener('click', () => {
        this.analyzeSelectedLights();
      });
    }

    // Fibonacci controls
    const fibToggle = document.getElementById('fibonacciEnabled');
    if (fibToggle) {
      fibToggle.addEventListener('change', (e) => {
        this.analysisOptions.fibonacciEnabled = e.target.checked;
        if (e.target.checked) {
          this.calculateFibonacciLevels();
        }
      });
    }

    const fibBtn = document.getElementById('calculateFibonacci');
    if (fibBtn) {
      fibBtn.addEventListener('click', () => {
        this.calculateFibonacciLevels();
      });
    }

    // Time prediction controls
    const timePredSelect = document.getElementById('timePredictionSelect');
    const loadPredBtn = document.getElementById('loadTimePrediction');

    if (loadPredBtn) {
      loadPredBtn.addEventListener('click', () => {
        const period = timePredSelect?.value || 'today';
        this.loadTimePrediction(period);
      });
    }

    // Advanced options
    const searchDepthSlider = document.getElementById('searchDepthSlider');
    if (searchDepthSlider) {
      searchDepthSlider.addEventListener('input', (e) => {
        this.analysisOptions.searchDepth = parseInt(e.target.value);
        const display = e.target.nextElementSibling;
        if (display) {
          display.textContent = `${this.analysisOptions.searchDepth} points`;
        }
      });
    }

    const convergenceSlider = document.getElementById('convergenceThresholdSlider');
    if (convergenceSlider) {
      convergenceSlider.addEventListener('input', (e) => {
        this.analysisOptions.convergenceThreshold = parseFloat(e.target.value);
        const display = e.target.nextElementSibling;
        if (display) {
          display.textContent = `${(this.analysisOptions.convergenceThreshold * 100).toFixed(0)}%`;
        }
      });
    }

    const autoToggle = document.getElementById('autoAnalysisToggle');
    if (autoToggle) {
      autoToggle.addEventListener('change', (e) => {
        this.analysisOptions.autoAnalysis = e.target.checked;
      });
    }
  }

  setupLightSelectionSystem() {
    console.log('[MLHistoricalAnalysis] Setting up light selection system...');

    // Add click handler to document to catch all clicks and filter for signal circles
    document.addEventListener('click', (e) => {
      // Try to find the closest signal circle that was clicked
      const signalCircle = e.target.closest('.signal-circle');
      
      // If we clicked on a signal circle, handle the selection
      if (signalCircle) {
        console.log('[MLHistoricalAnalysis] Signal circle clicked:', signalCircle);
        this.toggleLightSelection(signalCircle);
        e.stopPropagation(); // Prevent event bubbling to parent elements
      }
    });

    // Also set up a mutation observer to handle dynamically added signal circles
    if (!this.observer) {
      this.observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.addedNodes.length) {
            console.log('[MLHistoricalAnalysis] New nodes added to DOM, checking for signal circles...');
          }
        });
      });

      // Start observing the document with the configured parameters
      this.observer.observe(document.body, {
        childList: true,
        subtree: true
      });
    }
  }

  toggleLightSelection(lightElement) {
    try {
      console.log('[MLHistoricalAnalysis] Toggling light selection for element:', lightElement);
      
      // Get the light data with improved error handling
      let lightData;
      try {
        lightData = this.getLightData(lightElement);
        if (!lightData) {
          throw new Error('No light data returned');
        }
        console.log('[MLHistoricalAnalysis] Light data:', lightData);
      } catch (error) {
        console.error('[MLHistoricalAnalysis] Error getting light data:', error, lightElement);
        return;
      }

      // Create a consistent ID for the light
      const lightId = `${lightData.indicator}-${lightData.timeframe}`.toLowerCase();
      console.log(`[MLHistoricalAnalysis] Toggling light: ${lightId}`);
      
      // Toggle selection
      if (this.selectedLights.has(lightId)) {
        // Deselect
        this.selectedLights.delete(lightId);
        lightElement.classList.remove('golden-selected');
        console.log(`[MLHistoricalAnalysis] Deselected light: ${lightId}`);
      } else {
        // Select (limit to max selections if needed)
        const maxSelections = 5; // Optional: Limit number of selections
        if (this.selectedLights.size >= maxSelections) {
          console.warn(`[MLHistoricalAnalysis] Maximum of ${maxSelections} lights can be selected`);
          return;
        }
        
        this.selectedLights.add(lightId);
        lightElement.classList.add('golden-selected');
        console.log(`[MLHistoricalAnalysis] Selected light: ${lightId}`);
      }

      // Update the UI to show selected lights
      this.updateSelectedLightsDisplay();
      
      // If auto-analysis is enabled, analyze the selected lights
      if (this.analysisOptions.autoAnalysis && this.selectedLights.size > 0) {
        this.analyzeSelectedLights();
      }
      
    } catch (error) {
      console.error('[MLHistoricalAnalysis] Error in toggleLightSelection:', error);
    }
  }

  getLightData(lightElement) {
    try {
      if (!lightElement) {
        console.error('[MLHistoricalAnalysis] No light element provided');
        return null;
      }
      
      // Try to get indicator from various possible locations
      let indicator = lightElement.dataset.indicator;
      let timeframe = lightElement.dataset.timeframe;
      
      // If not found in direct data attributes, try parent elements
      if (!indicator || !timeframe) {
        // Try to find the row (which contains the indicator name)
        let row = lightElement.closest('tr') || lightElement.closest('.signal-row');
        
        // If we found a row, try to get the indicator from it
        if (row) {
          // Try dataset first, then text content as fallback
          indicator = indicator || row.dataset.indicator || 
                     (row.querySelector('.signal-label')?.textContent || '').trim().toLowerCase();
          
          // Try to find the cell (which might contain timeframe)
          const cell = lightElement.closest('td') || lightElement.closest('.signal-cell');
          if (cell) {
            timeframe = timeframe || cell.dataset.timeframe;
          }
        }
      }
      
      // If we still don't have values, try to parse from ID or class
      if (!indicator || !timeframe) {
        const id = lightElement.id || '';
        const parts = id.split('-');
        if (parts.length >= 3) {
          indicator = indicator || parts[1];
          timeframe = timeframe || parts[2];
        }
      }
      
      // Final fallbacks
      indicator = indicator || 'unknown';
      timeframe = timeframe || 'unknown';
      
      // Clean up the values
      indicator = indicator.toString().toLowerCase().trim();
      timeframe = timeframe.toString().toLowerCase().trim();
      
      // Get current signal state
      const signalState = this.getSignalState(lightElement);
      
      console.log(`[MLHistoricalAnalysis] Light data - Indicator: ${indicator}, Timeframe: ${timeframe}, State: ${signalState}`);
      
      return {
        indicator,
        timeframe,
        signal: signalState,
        element: lightElement,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[MLHistoricalAnalysis] Error getting light data:', error, lightElement);
      return null;
    }
  }
  
  getSignalState(lightElement) {
    // Check for signal state based on classes
    if (lightElement.classList.contains('strong-buy')) return 'strong-buy';
    if (lightElement.classList.contains('mild-buy')) return 'mild-buy';
    if (lightElement.classList.contains('neutral')) return 'neutral';
    if (lightElement.classList.contains('mild-sell')) return 'mild-sell';
    if (lightElement.classList.contains('strong-sell')) return 'strong-sell';
    return 'unknown';
  }

  clearLightSelection() {
    console.log('[MLHistoricalAnalysis] Clearing light selection...');

    // Remove golden highlights from all lights
    document.querySelectorAll('.golden-selected').forEach(light => {
      light.classList.remove('golden-selected');
    });

    this.selectedLights.clear();
    this.updateSelectedLightsDisplay();
  }

  updateSelectedLightsDisplay() {
    const display = document.getElementById('selectedLightsDisplay');
    if (!display) return;

    if (this.selectedLights.size === 0) {
      display.innerHTML = '<span class="no-selection">No lights selected</span>';
    } else {
      const lightsList = Array.from(this.selectedLights).map(lightId => {
        const [indicator, timeframe] = lightId.split('-');
        // Get the current signal state for this indicator and timeframe
        const signalElement = document.querySelector(`.signal-circle[data-indicator="${indicator}"][data-timeframe="${timeframe}"]`);
        const signalState = signalElement ? this.getSignalState(signalElement) : 'unknown';
        
        // Get threshold settings for this indicator if available
        const thresholdSettings = this.getIndicatorThresholds(indicator);
        const thresholdText = thresholdSettings 
          ? `(Thresholds: ${thresholdSettings.buyThreshold}/${thresholdSettings.sellThreshold})`
          : '';
        
        return `
          <div class="selected-light-item" data-indicator="${indicator}" data-timeframe="${timeframe}">
            <span class="signal-indicator ${signalState}"></span>
            <span class="light-label">${indicator.toUpperCase()} (${timeframe})</span>
            <span class="threshold-settings" title="Current threshold settings for this indicator">${thresholdText}</span>
            <button class="remove-light" title="Remove this signal">×</button>
          </div>`;
      }).join('');

      display.innerHTML = `
        <div class="selected-count">${this.selectedLights.size} signals selected:</div>
        <div class="selected-lights-list">${lightsList}</div>
        <div class="selection-actions">
          <button id="analyzeSelected" class="action-btn primary">Analyze Selected</button>
          <button id="clearSelection" class="action-btn secondary">Clear All</button>
        </div>
      `;
      
      // Add event listeners to remove buttons
      display.querySelectorAll('.remove-light').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.stopPropagation();
          const item = e.target.closest('.selected-light-item');
          if (item) {
            const indicator = item.dataset.indicator;
            const timeframe = item.dataset.timeframe;
            this.deselectLight(`${indicator}-${timeframe}`);
          }
        });
      });
    }
  }

  analyzeSelectedLights() {
    if (this.selectedLights.size === 0) {
      console.warn('[MLHistoricalAnalysis] No lights selected for analysis');
      return;
    }

    console.log(`[MLHistoricalAnalysis] Analyzing convergence for ${this.selectedLights.size} selected lights...`);

    // Simulate convergence analysis
    const convergenceData = this.performConvergenceAnalysis();
    this.displayConvergenceResults(convergenceData);
  }

  performConvergenceAnalysis() {
    console.log('[MLHistoricalAnalysis] Performing convergence analysis...');

    // Simulate historical convergence analysis
    const totalPoints = this.analysisOptions.searchDepth;
    const convergences = [];

    // Generate simulated convergence events
    for (let i = 0; i < totalPoints; i++) {
      const timestamp = Date.now() - (i * 60000); // 1 minute intervals
      const isConvergence = Math.random() > (1 - this.analysisOptions.convergenceThreshold);

      if (isConvergence) {
        const isGreen = Math.random() > 0.5;
        const wasSuccessful = Math.random() > 0.3; // 70% success rate

        convergences.push({
          timestamp,
          type: isGreen ? 'green' : 'red',
          successful: wasSuccessful,
          strength: Math.random() * 0.5 + 0.5, // 0.5 to 1.0
          priceChange: (Math.random() - 0.5) * 0.1 // -5% to +5%
        });
      }
    }

    const greenConvergences = convergences.filter(c => c.type === 'green').length;
    const redConvergences = convergences.filter(c => c.type === 'red').length;
    const successfulConvergences = convergences.filter(c => c.successful).length;
    const successRate = convergences.length > 0 ? (successfulConvergences / convergences.length) * 100 : 0;

    return {
      total: convergences.length,
      green: greenConvergences,
      red: redConvergences,
      successRate,
      events: convergences.slice(0, 10) // Show last 10 events
    };
  }

  getIndicatorThresholds(indicator) {
    // Try to get threshold settings for the indicator
    try {
      if (window.indicatorSettings && window.indicatorSettings[indicator]) {
        return {
          buyThreshold: window.indicatorSettings[indicator].buyThreshold || 'N/A',
          sellThreshold: window.indicatorSettings[indicator].sellThreshold || 'N/A'
        };
      }
      return null;
    } catch (error) {
      console.error('[MLHistoricalAnalysis] Error getting indicator thresholds:', error);
      return null;
    }
  }

  deselectLight(lightId) {
    if (this.selectedLights.has(lightId)) {
      this.selectedLights.delete(lightId);
      // Remove golden highlight
      const [indicator, timeframe] = lightId.split('-');
      const signalElement = document.querySelector(`.signal-circle[data-indicator="${indicator}"][data-timeframe="${timeframe}"]`);
      if (signalElement) {
        signalElement.classList.remove('golden-selected');
      }
      this.updateSelectedLightsDisplay();
      console.log(`[MLHistoricalAnalysis] Deselected light: ${lightId}`);
    }
  }

  displayConvergenceResults(data) {
    console.log('[MLHistoricalAnalysis] Displaying convergence results:', data);

    // Update statistics
    const statsContainer = document.getElementById('convergenceStats');
    if (statsContainer) {
      statsContainer.innerHTML = `
        <div class="stat-row header">
          <div class="stat-item" title="Total number of convergence events found">Total</div>
          <div class="stat-item" title="Number of bullish convergence events">Bullish</div>
          <div class="stat-item" title="Number of bearish convergence events">Bearish</div>
          <div class="stat-item" title="Success rate of convergence signals">Success Rate</div>
        </div>
        <div class="stat-row values">
          <div class="stat-value">${data.total}</div>
          <div class="stat-value green">${data.green}</div>
          <div class="stat-value red">${data.red}</div>
          <div class="stat-value ${data.successRate > 70 ? 'high' : data.successRate > 50 ? 'medium' : 'low'}">
            ${data.successRate.toFixed(1)}%
          </div>
        </div>
      `;
    }

    // Update timeline with detailed results
    const timeline = document.getElementById('convergenceTimeline');
    if (timeline) {
      if (data.events.length > 0) {
        timeline.innerHTML = `
          <div class="timeline-header">
            <span class="header-time" title="Time of the signal">Time</span>
            <span class="header-type" title="Type of convergence (bullish/bearish)">Signal</span>
            <span class="header-strength" title="Signal strength (0-100%)">Strength</span>
            <span class="header-result" title="Whether the signal was successful">Result</span>
            <span class="header-change" title="Price change after signal">Change</span>
          </div>
          <div class="timeline-events">
            ${data.events.map((event, index) => `
              <div class="timeline-event ${event.type} ${event.successful ? 'successful' : 'failed'}" 
                   data-event-index="${index}" 
                   title="${event.type.toUpperCase()} signal at ${new Date(event.timestamp).toLocaleString()}
Strength: ${(event.strength * 100).toFixed(0)}%
Result: ${event.successful ? 'Successful' : 'Failed'}
Price Change: ${event.priceChange > 0 ? '+' : ''}${(event.priceChange * 100).toFixed(2)}%">>
                <span class="event-time" title="${new Date(event.timestamp).toLocaleString()}">
                  ${new Date(event.timestamp).toLocaleTimeString()}
                </span>
                <span class="event-type" title="${event.type === 'green' ? 'Bullish' : 'Bearish'} convergence">
                  ${event.type === 'green' ? '🟢' : '🔴'} ${event.type.toUpperCase()}
                </span>
                <span class="event-strength" title="Signal strength: ${(event.strength * 100).toFixed(0)}%">
                  ${(event.strength * 100).toFixed(0)}%
                </span>
                <span class="event-result" title="${event.successful ? 'Signal was accurate' : 'Signal was inaccurate'}">
                  ${event.successful ? '✅' : '❌'}
                </span>
                <span class="event-change ${event.priceChange >= 0 ? 'positive' : 'negative'}" 
                      title="Price change after signal: ${event.priceChange > 0 ? '+' : ''}${(event.priceChange * 100).toFixed(2)}%">
                  ${event.priceChange > 0 ? '↑' : '↓'} ${Math.abs(event.priceChange * 100).toFixed(2)}%
                </span>
              </div>
            `).join('')}
          </div>
        `;
      } else {
        timeline.innerHTML = `
          <div class="no-events">
            No convergence events found in the selected time period.
          </div>
        `;
      }
    }

    // Update advanced options with current settings
    this.updateAdvancedOptions();
  }

  calculateFibonacciLevels() {
    if (!this.analysisOptions.fibonacciEnabled) return;

    console.log('[MLHistoricalAnalysis] Calculating Fibonacci levels...');

    // Get current price data
    const currentPrice = this.getCurrentPrice();
    if (!currentPrice) return;

    // Calculate recent high and low
    const recentData = this.getRecentPriceData(50);
    if (recentData.length < 2) return;

    const high = Math.max(...recentData);
    const low = Math.min(...recentData);
    const range = high - low;

    // Standard Fibonacci retracement levels
    const fibLevels = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1.0];

    this.fibonacciLevels = fibLevels.map(level => ({
      level: level,
      price: high - (range * level),
      percentage: level * 100,
      type: level === 0 ? 'resistance' : level === 1 ? 'support' : 'retracement'
    }));

    this.displayFibonacciLevels();
  }

  displayFibonacciLevels() {
    const container = document.getElementById('fibonacciLevels');
    if (!container || this.fibonacciLevels.length === 0) return;

    container.innerHTML = `
      <div class="fibonacci-header">Fibonacci Retracement Levels:</div>
      ${this.fibonacciLevels.map(fib => `
        <div class="fibonacci-level ${fib.type}">
          <span class="fib-percentage">${fib.percentage.toFixed(1)}%</span>
          <span class="fib-price">$${fib.price.toFixed(8)}</span>
          <span class="fib-type">${fib.type}</span>
        </div>
      `).join('')}
    `;
  }

  loadTimePrediction(period) {
    console.log(`[MLHistoricalAnalysis] Loading time prediction for: ${period}`);

    // Simulate time-based prediction loading
    const prediction = this.generateTimePrediction(period);
    this.displayTimePrediction(prediction);
  }

  generateTimePrediction(period) {
    const currentPrice = this.getCurrentPrice() || 50000;

    // Generate different predictions based on time period
    const predictions = {
      today: {
        direction: 'bullish',
        confidence: 78,
        targetPrice: currentPrice * 1.025,
        reasoning: 'Strong momentum indicators and volume surge detected',
        timeframe: '4-6 hours'
      },
      yesterday: {
        direction: 'bearish',
        confidence: 65,
        targetPrice: currentPrice * 0.985,
        reasoning: 'Resistance level rejection and profit-taking signals',
        timeframe: 'End of day'
      },
      lastWeek: {
        direction: 'bullish',
        confidence: 82,
        targetPrice: currentPrice * 1.045,
        reasoning: 'Weekly trend reversal and institutional accumulation',
        timeframe: '3-5 days'
      },
      lastMonth: {
        direction: 'neutral',
        confidence: 55,
        targetPrice: currentPrice * 1.008,
        reasoning: 'Consolidation phase with mixed signals',
        timeframe: '1-2 weeks'
      },
      all: {
        direction: 'bullish',
        confidence: 89,
        targetPrice: currentPrice * 1.125,
        reasoning: 'Historical pattern analysis shows strong upward potential',
        timeframe: '2-4 weeks'
      }
    };

    return predictions[period] || predictions.today;
  }

  displayTimePrediction(prediction) {
    const display = document.getElementById('timePredictionDisplay');
    if (!display) return;

    const directionIcon = prediction.direction === 'bullish' ? '📈' : prediction.direction === 'bearish' ? '📉' : '➡️';
    const confidenceClass = prediction.confidence >= 80 ? 'high' : prediction.confidence >= 60 ? 'medium' : 'low';

    display.innerHTML = `
      <div class="time-prediction-card">
        <div class="prediction-header">
          <span class="prediction-direction ${prediction.direction}">${directionIcon} ${prediction.direction.toUpperCase()}</span>
          <span class="prediction-confidence ${confidenceClass}">${prediction.confidence}%</span>
        </div>
        <div class="prediction-target">
          <span class="target-label">Target Price:</span>
          <span class="target-price">$${prediction.targetPrice.toFixed(8)}</span>
        </div>
        <div class="prediction-timeframe">
          <span class="timeframe-label">Timeframe:</span>
          <span class="timeframe-value">${prediction.timeframe}</span>
        </div>
        <div class="prediction-reasoning">
          <strong>Analysis:</strong> ${prediction.reasoning}
        </div>
      </div>
    `;
  }

  getCurrentPrice() {
    // Try to get current price from various sources
    if (window.currentPrice) return window.currentPrice;
    if (window.indicatorsData) {
      const timeframes = Object.keys(window.indicatorsData);
      if (timeframes.length > 0) {
        return window.indicatorsData[timeframes[0]]?.currentPrice;
      }
    }
    return null;
  }

  getRecentPriceData(count = 50) {
    // Simulate recent price data
    const currentPrice = this.getCurrentPrice() || 50000;
    const prices = [];

    for (let i = 0; i < count; i++) {
      const variation = (Math.random() - 0.5) * 0.1; // ±5% variation
      prices.push(currentPrice * (1 + variation));
    }

    return prices;
  }

  initializeTimeBasedPredictions() {
    console.log('[MLHistoricalAnalysis] Initializing time-based predictions...');

    // Generate initial predictions for all time periods
    Object.keys(this.timeBasedPredictions).forEach(period => {
      this.timeBasedPredictions[period] = this.generateTimePrediction(period);
    });
  }

  startHistoricalAnalysis() {
    console.log('[MLHistoricalAnalysis] Starting historical analysis engine...');

    // Auto-analysis interval
    if (this.analysisOptions.autoAnalysis) {
      setInterval(() => {
        if (this.selectedLights.size > 0) {
          this.analyzeSelectedLights();
        }

        if (this.analysisOptions.fibonacciEnabled) {
          this.calculateFibonacciLevels();
        }
      }, 30000); // Every 30 seconds
    }
  }

  applyHistoricalAnalysisStyles() {
    const style = document.createElement('style');
    style.textContent = `
      /* ML Historical Analysis Styling */
      .ml-historical-container {
        background: rgba(0, 10, 20, 0.9);
        border: 1px solid rgba(255, 215, 0, 0.3);
        border-radius: 8px;
        margin: 10px 0;
        padding: 15px;
        font-family: 'Courier New', monospace;
      }

      .historical-analysis-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        border-bottom: 1px solid rgba(255, 215, 0, 0.2);
        padding-bottom: 10px;
      }

      .historical-analysis-header h3 {
        color: #ffd700;
        margin: 0;
        font-size: 16px;
      }

      .analysis-toggle-button {
        background: linear-gradient(135deg, #ffd700, #ffaa00);
        border: none;
        color: #000;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        font-weight: bold;
      }

      .historical-analysis-content {
        transition: all 0.3s ease;
        overflow: hidden;
      }

      .historical-analysis-content.collapsed {
        max-height: 0;
        opacity: 0;
        padding: 0;
      }

      /* Golden Selection Effects */
      .signal-light.golden-selected,
      .circle.golden-selected {
        box-shadow: 0 0 20px #ffd700, 0 0 40px #ffd700, 0 0 60px #ffd700;
        border: 2px solid #ffd700;
        animation: goldenPulse 2s infinite;
      }

      @keyframes goldenPulse {
        0%, 100% {
          box-shadow: 0 0 20px #ffd700, 0 0 40px #ffd700, 0 0 60px #ffd700;
          transform: scale(1);
        }
        50% {
          box-shadow: 0 0 30px #ffd700, 0 0 60px #ffd700, 0 0 90px #ffd700;
          transform: scale(1.05);
        }
      }

      /* Panel Styling */
      .light-selection-panel,
      .convergence-results-panel,
      .fibonacci-panel,
      .time-predictions-panel,
      .advanced-options-panel {
        background: rgba(0, 20, 40, 0.5);
        border: 1px solid rgba(255, 215, 0, 0.2);
        border-radius: 6px;
        padding: 12px;
        margin: 10px 0;
      }

      .light-selection-panel h4,
      .convergence-results-panel h4,
      .fibonacci-panel h4,
      .time-predictions-panel h4,
      .advanced-options-panel h4 {
        color: #ffd700;
        margin: 0 0 10px 0;
        font-size: 14px;
      }

      .selection-instructions {
        color: #cccccc;
        font-size: 12px;
        margin-bottom: 10px;
        font-style: italic;
      }

      .selected-lights-display {
        background: rgba(0, 0, 0, 0.3);
        padding: 8px;
        border-radius: 4px;
        margin: 10px 0;
        min-height: 30px;
      }

      .no-selection {
        color: #888888;
        font-style: italic;
      }

      .selected-count {
        color: #ffd700;
        font-weight: bold;
        margin-bottom: 5px;
      }

      .selected-lights-list {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
      }

      .selected-light-tag {
        background: linear-gradient(135deg, #ffd700, #ffaa00);
        color: #000;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
      }

      .selection-controls {
        display: flex;
        gap: 10px;
        margin-top: 10px;
      }

      .clear-selection-btn,
      .analyze-convergence-btn,
      .calculate-fibonacci-btn,
      .load-prediction-btn {
        background: rgba(255, 215, 0, 0.2);
        border: 1px solid #ffd700;
        color: #ffd700;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 11px;
        transition: all 0.3s ease;
      }

      .clear-selection-btn:hover,
      .analyze-convergence-btn:hover,
      .calculate-fibonacci-btn:hover,
      .load-prediction-btn:hover {
        background: rgba(255, 215, 0, 0.4);
        transform: translateY(-1px);
      }

      /* Convergence Results */
      .convergence-stats {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
        margin-bottom: 15px;
      }

      .stat-item {
        display: flex;
        justify-content: space-between;
        padding: 5px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
      }

      .stat-label {
        color: #cccccc;
        font-size: 11px;
      }

      .stat-value {
        color: #ffffff;
        font-weight: bold;
        font-size: 11px;
      }

      .stat-value.green { color: #00ff00; }
      .stat-value.red { color: #ff0000; }

      .convergence-timeline {
        max-height: 200px;
        overflow-y: auto;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        padding: 8px;
      }

      .timeline-header {
        color: #ffd700;
        font-weight: bold;
        margin-bottom: 8px;
        font-size: 12px;
      }

      .timeline-event {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 8px;
        margin: 2px 0;
        border-radius: 3px;
        font-size: 10px;
      }

      .timeline-event.green { background: rgba(0, 255, 0, 0.1); border-left: 3px solid #00ff00; }
      .timeline-event.red { background: rgba(255, 0, 0, 0.1); border-left: 3px solid #ff0000; }
      .timeline-event.successful { opacity: 1; }
      .timeline-event.failed { opacity: 0.6; }

      /* Fibonacci Styling */
      .fibonacci-controls {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 10px;
      }

      .fibonacci-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #ffffff;
        cursor: pointer;
      }

      .checkbox-custom {
        width: 16px;
        height: 16px;
        border: 2px solid #ffd700;
        border-radius: 3px;
        position: relative;
      }

      .fibonacci-toggle input[type="checkbox"]:checked + .checkbox-custom::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 1px;
        color: #ffd700;
        font-weight: bold;
        font-size: 12px;
      }

      .fibonacci-levels {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 4px;
        padding: 8px;
      }

      .fibonacci-header {
        color: #ffd700;
        font-weight: bold;
        margin-bottom: 8px;
        font-size: 12px;
      }

      .fibonacci-level {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 3px 6px;
        margin: 2px 0;
        border-radius: 3px;
        font-size: 11px;
      }

      .fibonacci-level.resistance { background: rgba(255, 0, 0, 0.1); color: #ff6666; }
      .fibonacci-level.support { background: rgba(0, 255, 0, 0.1); color: #66ff66; }
      .fibonacci-level.retracement { background: rgba(255, 215, 0, 0.1); color: #ffd700; }

      /* Time Predictions */
      .prediction-dropdown {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
      }

      .prediction-dropdown select {
        background: rgba(0, 20, 40, 0.8);
        border: 1px solid rgba(255, 215, 0, 0.3);
        color: #ffffff;
        padding: 5px 10px;
        border-radius: 4px;
        flex: 1;
      }

      .time-prediction-card {
        background: rgba(0, 0, 0, 0.3);
        border-radius: 6px;
        padding: 12px;
      }

      .prediction-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
      }

      .prediction-direction {
        font-weight: bold;
        font-size: 14px;
      }

      .prediction-direction.bullish { color: #00ff00; }
      .prediction-direction.bearish { color: #ff0000; }
      .prediction-direction.neutral { color: #ffaa00; }

      .prediction-confidence {
        font-weight: bold;
        font-size: 12px;
      }

      .prediction-confidence.high { color: #00ff00; }
      .prediction-confidence.medium { color: #ffaa00; }
      .prediction-confidence.low { color: #ff6600; }

      .prediction-target,
      .prediction-timeframe {
        display: flex;
        justify-content: space-between;
        margin: 5px 0;
        font-size: 12px;
      }

      .target-price {
        color: #00ff88;
        font-weight: bold;
      }

      .prediction-reasoning {
        background: rgba(0, 0, 0, 0.2);
        padding: 8px;
        border-radius: 4px;
        font-size: 11px;
        color: #cccccc;
        line-height: 1.4;
        margin-top: 10px;
      }

      /* Advanced Options */
      .option-row {
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 8px 0;
      }

      .option-row label {
        color: #ffffff;
        font-size: 12px;
        min-width: 120px;
      }

      .option-row input[type="range"] {
        flex: 1;
        margin: 0 10px;
      }

      .option-value {
        color: #ffd700;
        font-weight: bold;
        min-width: 60px;
        font-size: 11px;
      }

      .option-toggle {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #ffffff;
        cursor: pointer;
      }

      .option-toggle input[type="checkbox"]:checked + .checkbox-custom::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 1px;
        color: #ffd700;
        font-weight: bold;
        font-size: 12px;
      }
    `;
    document.head.appendChild(style);
  }

  setupFibonacciAnalysis() {
    console.log('[MLHistoricalAnalysis] Setting up Fibonacci analysis...');

    // Create Fibonacci analysis interface
    const fibonacciContainer = document.createElement('div');
    fibonacciContainer.className = 'fibonacci-analysis-container';
    fibonacciContainer.innerHTML = `
      <div class="fibonacci-header">
        <h4>📐 Fibonacci Analysis</h4>
        <p>Advanced mathematical pattern recognition using Fibonacci sequences</p>
      </div>

      <div class="fibonacci-controls">
        <div class="fibonacci-settings">
          <label>
            <span>Fibonacci Levels:</span>
            <select id="fibonacciLevels">
              <option value="standard">Standard (23.6%, 38.2%, 50%, 61.8%, 78.6%)</option>
              <option value="extended">Extended (0%, 100%, 161.8%, 261.8%)</option>
              <option value="custom">Custom Levels</option>
            </select>
          </label>

          <label>
            <span>Time Period:</span>
            <select id="fibonacciPeriod">
              <option value="1h">1 Hour</option>
              <option value="4h">4 Hours</option>
              <option value="1d" selected>1 Day</option>
              <option value="1w">1 Week</option>
            </select>
          </label>
        </div>

        <div class="fibonacci-actions">
          <button class="fibonacci-btn" id="calculateFibBtn">
            <span class="btn-icon">🧮</span>
            Calculate Levels
          </button>
          <button class="fibonacci-btn" id="fibonacciPredictBtn">
            <span class="btn-icon">🎯</span>
            Predict Targets
          </button>
        </div>
      </div>

      <div class="fibonacci-results">
        <div class="fibonacci-levels-display">
          <h5>Current Fibonacci Levels:</h5>
          <div class="fib-levels-list" id="fibLevelsList">
            <div class="fib-level">
              <span class="fib-percentage">23.6%</span>
              <span class="fib-price">--</span>
              <span class="fib-status">--</span>
            </div>
          </div>
        </div>

        <div class="fibonacci-signals">
          <h5>Fibonacci Signals:</h5>
          <div class="fib-signals-list" id="fibSignalsList">
            <div class="fib-signal">
              <span class="signal-type">Support</span>
              <span class="signal-level">61.8%</span>
              <span class="signal-strength">Strong</span>
            </div>
          </div>
        </div>
      </div>
    `;

    // Add to historical analysis panel
    const panel = document.querySelector('.ml-historical-analysis');
    if (panel) {
      panel.appendChild(fibonacciContainer);
    }

    // Bind Fibonacci events
    this.bindFibonacciEvents();
  }

  bindFibonacciEvents() {
    console.log('[MLHistoricalAnalysis] Binding Fibonacci events...');

    const calculateBtn = document.getElementById('calculateFibBtn');
    const predictBtn = document.getElementById('fibonacciPredictBtn');

    if (calculateBtn) {
      calculateBtn.addEventListener('click', () => {
        this.calculateFibonacciLevels();
      });
    }

    if (predictBtn) {
      predictBtn.addEventListener('click', () => {
        this.predictFibonacciTargets();
      });
    }
  }

  calculateFibonacciLevels() {
    console.log('[MLHistoricalAnalysis] Calculating Fibonacci levels...');

    // Mock calculation for now
    const levels = [
      { percentage: '0%', price: '$45,000', status: 'Support' },
      { percentage: '23.6%', price: '$47,360', status: 'Resistance' },
      { percentage: '38.2%', price: '$49,180', status: 'Neutral' },
      { percentage: '50%', price: '$50,000', status: 'Key Level' },
      { percentage: '61.8%', price: '$50,820', status: 'Strong Resistance' },
      { percentage: '78.6%', price: '$51,720', status: 'Major Resistance' },
      { percentage: '100%', price: '$52,000', status: 'Target' }
    ];

    const levelsList = document.getElementById('fibLevelsList');
    if (levelsList) {
      levelsList.innerHTML = levels.map(level => `
        <div class="fib-level">
          <span class="fib-percentage">${level.percentage}</span>
          <span class="fib-price">${level.price}</span>
          <span class="fib-status">${level.status}</span>
        </div>
      `).join('');
    }
  }

  predictFibonacciTargets() {
    console.log('[MLHistoricalAnalysis] Predicting Fibonacci targets...');

    // Mock prediction for now
    const signals = [
      { type: 'Support', level: '38.2%', strength: 'Strong' },
      { type: 'Resistance', level: '61.8%', strength: 'Moderate' },
      { type: 'Target', level: '100%', strength: 'High Probability' }
    ];

    const signalsList = document.getElementById('fibSignalsList');
    if (signalsList) {
      signalsList.innerHTML = signals.map(signal => `
        <div class="fib-signal">
          <span class="signal-type">${signal.type}</span>
          <span class="signal-level">${signal.level}</span>
          <span class="signal-strength">${signal.strength}</span>
        </div>
      `).join('');
    }
  }
}

// Initialize ML historical analysis
let mlHistoricalAnalysisInitialized = false;

function initializeMLHistoricalAnalysis() {
  if (mlHistoricalAnalysisInitialized) return;
  
  try {
    const container = document.querySelector('#ml-historical-analysis');
    if (!container) {
      console.log('[MLHistoricalAnalysis] Container not found, waiting...');
      setTimeout(initializeMLHistoricalAnalysis, 1000);
      return;
    }
    
    window.mlHistoricalAnalysis = new MLHistoricalAnalysis();
    mlHistoricalAnalysisInitialized = true;
    console.log('[MLHistoricalAnalysis] Initialization complete');
  } catch (error) {
    console.error('[MLHistoricalAnalysis] Error during initialization:', error);
    
    // Retry after delay if initialization fails
    setTimeout(initializeMLHistoricalAnalysis, 1000);
  }
}

// Initialize based on document ready state
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeMLHistoricalAnalysis);
} else {
  // DOM already loaded, initialize immediately
  setTimeout(initializeMLHistoricalAnalysis, 100);
}

// Fallback in case elements load later
setTimeout(initializeMLHistoricalAnalysis, 2000);

// Also initialize when the window loads completely
window.addEventListener('load', initializeMLHistoricalAnalysis);

// Export for manual initialization
window.initializeMLHistoricalAnalysis = initializeMLHistoricalAnalysis;

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = MLHistoricalAnalysis;
}