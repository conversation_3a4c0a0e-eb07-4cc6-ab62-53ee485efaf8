/**
 * Enhanced ML Chart System with Multiple Data Points and Controls
 */

class EnhancedMLChartSystem {
  constructor() {
    this.chart = null;
    this.currentTimeframe = '1h';
    this.availableDatasets = {
      // Original ML chart lines that were requested to be restored
      'price_prediction': { label: 'Price Prediction', color: '#00ffff', enabled: true },
      'probability_score': { label: 'Probability Score', color: '#ff6b6b', enabled: true },
      'volatility_index': { label: 'Volatility Index', color: '#4ecdc4', enabled: true },
      'pattern_confidence': { label: 'Pattern Confidence', color: '#45b7d1', enabled: true },
      'training_accuracy': { label: 'Training Accuracy', color: '#96ceb4', enabled: true },

      // Additional technical indicators
      'rsi': { label: 'RSI', color: '#ff9999', enabled: false },
      'macd': { label: 'MACD', color: '#99ccff', enabled: false },
      'volume': { label: 'Volume', color: '#99ff99', enabled: false },
      'price': { label: 'Current Price', color: '#ffff99', enabled: false },
      'bollinger_upper': { label: 'Bollinger Upper', color: '#ffeaa7', enabled: false },
      'bollinger_lower': { label: 'Bollinger Lower', color: '#fab1a0', enabled: false },
      'sma_20': { label: 'SMA 20', color: '#fd79a8', enabled: false },
      'ema_12': { label: 'EMA 12', color: '#fdcb6e', enabled: false },
      'stoch_rsi': { label: 'Stoch RSI', color: '#e17055', enabled: false },
      'atr': { label: 'ATR', color: '#81ecec', enabled: false },
      'momentum': { label: 'Momentum', color: '#a29bfe', enabled: false },
      'williams_r': { label: 'Williams %R', color: '#fd79a8', enabled: false },
      'support_resistance': { label: 'Support/Resistance', color: '#ff8c42', enabled: false },
      'trend_strength': { label: 'Trend Strength', color: '#6a4c93', enabled: false },
      'market_sentiment': { label: 'Market Sentiment', color: '#f72585', enabled: false },
      'fibonacci_levels': { label: 'Fibonacci Levels', color: '#4cc9f0', enabled: false }
    };
    this.timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w'];
    this.zoomLevels = [24, 48, 96, 168, 336, 720]; // Hours of data to show
    this.currentZoomLevel = 24;
    this.init();
  }

  init() {
    console.log('[EnhancedMLChartSystem] 🚀 Initializing enhanced ML chart system...');
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setup());
    } else {
      this.setup();
    }
  }

  setup() {
    this.createChartControls();
    this.initializeChart();
    this.setupEventListeners();
    console.log('[EnhancedMLChartSystem] ✅ Enhanced ML chart system initialized');
  }

  createChartControls() {
    // Try multiple selectors to find the ML chart container
    const chartContainer = document.querySelector('#ml-visualization-container, .ml-visualization-container, .ml-analysis-container, #momentum-indicators .ml-analysis-container');
    if (!chartContainer) {
      console.warn('[EnhancedMLChartSystem] Chart container not found, trying to create one...');
      this.createMLContainer();
      return;
    }

    // Create controls container
    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'ml-chart-controls';
    controlsContainer.innerHTML = `
      <div class="chart-controls-header">
        <h4>📊 ML Chart Controls</h4>
      </div>
      <div class="chart-controls-row">
        <div class="control-group">
          <label>Data Points:</label>
          <select id="mlChartDataSelector" multiple class="data-selector">
            ${Object.entries(this.availableDatasets).map(([key, dataset]) => 
              `<option value="${key}" ${dataset.enabled ? 'selected' : ''}>${dataset.label}</option>`
            ).join('')}
          </select>
        </div>
        <div class="control-group">
          <label>Timeframe:</label>
          <select id="mlChartTimeframe" class="timeframe-selector">
            ${this.timeframes.map(tf => 
              `<option value="${tf}" ${tf === this.currentTimeframe ? 'selected' : ''}>${tf}</option>`
            ).join('')}
          </select>
        </div>
        <div class="control-group">
          <label>Zoom:</label>
          <select id="mlChartZoom" class="zoom-selector">
            ${this.zoomLevels.map(level => 
              `<option value="${level}" ${level === this.currentZoomLevel ? 'selected' : ''}>${level}h</option>`
            ).join('')}
          </select>
        </div>
        <div class="control-group">
          <button id="mlChartRefresh" class="refresh-button">🔄 Refresh</button>
          <button id="mlChartReset" class="reset-button">↺ Reset</button>
        </div>
      </div>
    `;

    // Add controls above the chart
    const chartCanvas = chartContainer.querySelector('#mlAnalysisChart');
    if (chartCanvas) {
      chartContainer.insertBefore(controlsContainer, chartCanvas);
    } else {
      chartContainer.appendChild(controlsContainer);
    }

    // Add CSS for controls
    this.addControlsCSS();
  }

  addControlsCSS() {
    const style = document.createElement('style');
    style.id = 'ml-chart-controls-css';
    style.textContent = `
      .ml-chart-controls {
        background: linear-gradient(135deg, rgba(0, 20, 40, 0.9), rgba(0, 40, 80, 0.7));
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        color: #ffffff;
        font-family: 'Orbitron', sans-serif;
      }

      .chart-controls-header h4 {
        margin: 0 0 10px 0;
        color: #00ffff;
        text-align: center;
      }

      .chart-controls-row {
        display: flex;
        gap: 15px;
        align-items: end;
        flex-wrap: wrap;
      }

      .control-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
        min-width: 120px;
      }

      .control-group label {
        font-size: 0.9rem;
        color: #00ccff;
        font-weight: bold;
      }

      .data-selector, .timeframe-selector, .zoom-selector {
        background: rgba(0, 20, 40, 0.8);
        border: 1px solid rgba(0, 255, 255, 0.4);
        border-radius: 4px;
        color: #ffffff;
        padding: 8px;
        font-family: 'Orbitron', sans-serif;
        font-size: 0.9rem;
      }

      .data-selector {
        height: 80px;
        min-width: 150px;
      }

      .data-selector option {
        background: rgba(0, 20, 40, 0.9);
        color: #ffffff;
        padding: 4px;
      }

      .data-selector option:checked {
        background: rgba(0, 255, 255, 0.3);
      }

      .refresh-button, .reset-button {
        background: linear-gradient(135deg, rgba(0, 100, 200, 0.8), rgba(0, 60, 120, 0.9));
        border: 1px solid rgba(0, 255, 255, 0.4);
        border-radius: 4px;
        color: #ffffff;
        padding: 8px 12px;
        cursor: pointer;
        font-family: 'Orbitron', sans-serif;
        font-size: 0.9rem;
        transition: all 0.3s ease;
      }

      .refresh-button:hover, .reset-button:hover {
        background: linear-gradient(135deg, rgba(0, 150, 255, 0.8), rgba(0, 100, 180, 0.9));
        box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      }

      .reset-button {
        background: linear-gradient(135deg, rgba(200, 100, 0, 0.8), rgba(120, 60, 0, 0.9));
      }

      .reset-button:hover {
        background: linear-gradient(135deg, rgba(255, 150, 0, 0.8), rgba(180, 100, 0, 0.9));
      }
    `;

    // Remove existing style if present
    const existingStyle = document.getElementById('ml-chart-controls-css');
    if (existingStyle) existingStyle.remove();

    document.head.appendChild(style);
  }

  createMLContainer() {
    console.log('[EnhancedMLChartSystem] Creating ML container...');

    // Find momentum-indicators container
    const momentumContainer = document.querySelector('#momentum-indicators, .momentum-indicators');
    if (!momentumContainer) {
      console.error('[EnhancedMLChartSystem] Momentum indicators container not found');
      return;
    }

    // Create ML analysis container
    const mlContainer = document.createElement('div');
    mlContainer.className = 'ml-analysis-container';
    mlContainer.id = 'ml-visualization-container';
    mlContainer.style.cssText = `
      margin-top: 20px;
      padding: 15px;
      background: rgba(0, 20, 40, 0.8);
      border: 1px solid rgba(0, 255, 255, 0.3);
      border-radius: 8px;
    `;

    mlContainer.innerHTML = `
      <div class="ml-chart-header" style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
        <h3 style="color: #00FFFF; font-size: 1rem; margin: 0; text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);">
          🤖 AI Technical Analysis & Probability Engine
        </h3>
        <div class="ml-status" style="color: #00FF00; font-size: 0.8rem;">
          ● Live Analysis Active
        </div>
      </div>
      <div class="chart-container" style="position: relative; height: 300px; width: 100%;">
        <canvas id="mlAnalysisChart" style="width: 100%; height: 100%;"></canvas>
      </div>
    `;

    // Append to momentum container
    momentumContainer.appendChild(mlContainer);

    // Now try to create controls
    this.createChartControls();
  }

  setupEventListeners() {
    // Data selector change
    const dataSelector = document.getElementById('mlChartDataSelector');
    if (dataSelector) {
      dataSelector.addEventListener('change', () => {
        this.updateEnabledDatasets();
        this.updateChart();
      });
    }

    // Timeframe change
    const timeframeSelector = document.getElementById('mlChartTimeframe');
    if (timeframeSelector) {
      timeframeSelector.addEventListener('change', (e) => {
        this.currentTimeframe = e.target.value;
        this.updateChart();
      });
    }

    // Zoom change
    const zoomSelector = document.getElementById('mlChartZoom');
    if (zoomSelector) {
      zoomSelector.addEventListener('change', (e) => {
        this.currentZoomLevel = parseInt(e.target.value);
        this.updateChart();
      });
    }

    // Refresh button
    const refreshButton = document.getElementById('mlChartRefresh');
    if (refreshButton) {
      refreshButton.addEventListener('click', () => {
        this.refreshData();
        this.updateChart();
      });
    }

    // Reset button
    const resetButton = document.getElementById('mlChartReset');
    if (resetButton) {
      resetButton.addEventListener('click', () => {
        this.resetToDefaults();
      });
    }
  }

  updateEnabledDatasets() {
    const dataSelector = document.getElementById('mlChartDataSelector');
    if (!dataSelector) return;

    const selectedOptions = Array.from(dataSelector.selectedOptions).map(option => option.value);
    
    // Update enabled status
    Object.keys(this.availableDatasets).forEach(key => {
      this.availableDatasets[key].enabled = selectedOptions.includes(key);
    });
  }

  initializeChart() {
    const canvas = document.querySelector('#mlAnalysisChart');
    if (!canvas || !window.Chart) {
      console.warn('[EnhancedMLChartSystem] Canvas or Chart.js not available');
      return;
    }

    // Destroy existing chart
    if (this.chart) {
      this.chart.destroy();
    }

    // Also destroy any existing Chart.js instance on this canvas
    const existingChart = Chart.getChart(canvas);
    if (existingChart) {
      existingChart.destroy();
    }

    const ctx = canvas.getContext('2d');
    
    this.chart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: this.generateTimeLabels(),
        datasets: this.generateDatasets()
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
          mode: 'index',
          intersect: false,
        },
        scales: {
          y: {
            type: 'linear',
            display: true,
            position: 'left',
            grid: {
              color: 'rgba(0, 255, 255, 0.2)'
            },
            ticks: {
              color: '#00ffff'
            }
          },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            grid: {
              drawOnChartArea: false,
            },
            ticks: {
              color: '#00ffff'
            }
          },
          x: {
            grid: {
              color: 'rgba(0, 255, 255, 0.2)'
            },
            ticks: {
              color: '#00ffff'
            }
          }
        },
        plugins: {
          legend: {
            labels: {
              color: '#00ffff',
              usePointStyle: true
            }
          },
          tooltip: {
            backgroundColor: 'rgba(0, 20, 40, 0.9)',
            titleColor: '#00ffff',
            bodyColor: '#ffffff',
            borderColor: '#00ffff',
            borderWidth: 1
          }
        }
      }
    });

    console.log('[EnhancedMLChartSystem] ✅ Chart initialized with multiple datasets');
  }

  generateTimeLabels() {
    const labels = [];
    const now = new Date();
    const intervalMinutes = this.getIntervalMinutes();
    const totalPoints = this.currentZoomLevel * (60 / intervalMinutes);

    for (let i = totalPoints - 1; i >= 0; i--) {
      const time = new Date(now.getTime() - i * intervalMinutes * 60 * 1000);
      labels.push(time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
    }
    return labels;
  }

  getIntervalMinutes() {
    const timeframeMinutes = {
      '1m': 1, '5m': 5, '15m': 15, '30m': 30,
      '1h': 60, '4h': 240, '1d': 1440, '1w': 10080
    };
    return timeframeMinutes[this.currentTimeframe] || 60;
  }

  generateDatasets() {
    const datasets = [];
    const dataLength = this.currentZoomLevel * (60 / this.getIntervalMinutes());

    Object.entries(this.availableDatasets).forEach(([key, dataset]) => {
      if (dataset.enabled) {
        datasets.push({
          label: dataset.label,
          data: this.generateDataForIndicator(key, dataLength),
          borderColor: dataset.color,
          backgroundColor: dataset.color + '20',
          borderWidth: 2,
          fill: false,
          yAxisID: this.getYAxisForIndicator(key),
          pointRadius: 1,
          pointHoverRadius: 4
        });
      }
    });

    return datasets;
  }

  getYAxisForIndicator(indicator) {
    // Use right axis for percentage-based indicators
    const rightAxisIndicators = ['rsi', 'stoch_rsi', 'williams_r', 'ml_probability'];
    return rightAxisIndicators.includes(indicator) ? 'y1' : 'y';
  }

  generateDataForIndicator(indicator, length) {
    // Get real data from global state if available
    const indicatorData = this.getRealIndicatorData(indicator);
    if (indicatorData && indicatorData.length >= length) {
      return indicatorData.slice(-length);
    }

    // Generate realistic sample data based on indicator type
    return this.generateSampleData(indicator, length);
  }

  getRealIndicatorData(indicator) {
    // Try to get real data from global state
    const timeframeData = window.indicatorsData?.[this.currentTimeframe];
    if (!timeframeData) return null;

    switch (indicator) {
      case 'rsi':
        return timeframeData.rsi?.history || null;
      case 'macd':
        return timeframeData.macd?.history || null;
      case 'volume':
        return timeframeData.volume?.history || null;
      case 'price':
        return window.priceHistory || null;
      default:
        return null;
    }
  }

  generateSampleData(indicator, length) {
    const data = [];
    let baseValue = this.getBaseValueForIndicator(indicator);
    
    for (let i = 0; i < length; i++) {
      const variation = this.getVariationForIndicator(indicator);
      baseValue += (Math.random() - 0.5) * variation;
      baseValue = Math.max(0, Math.min(100, baseValue)); // Clamp for percentage indicators
      data.push(parseFloat(baseValue.toFixed(2)));
    }
    
    return data;
  }

  getBaseValueForIndicator(indicator) {
    const baseValues = {
      'ml_probability': 50,
      'rsi': 50,
      'macd': 0,
      'volume': 1000000,
      'price': 100000,
      'stoch_rsi': 50,
      'williams_r': -50,
      'atr': 1000,
      'momentum': 0
    };
    return baseValues[indicator] || 50;
  }

  getVariationForIndicator(indicator) {
    const variations = {
      'ml_probability': 5,
      'rsi': 3,
      'macd': 2,
      'volume': 100000,
      'price': 1000,
      'stoch_rsi': 4,
      'williams_r': 3,
      'atr': 50,
      'momentum': 1
    };
    return variations[indicator] || 2;
  }

  updateChart() {
    if (!this.chart) return;

    this.chart.data.labels = this.generateTimeLabels();
    this.chart.data.datasets = this.generateDatasets();
    this.chart.update('none'); // No animation for better performance
  }

  refreshData() {
    console.log('[EnhancedMLChartSystem] 🔄 Refreshing chart data...');
    // Force refresh of real data if available
    if (typeof window.requestAllIndicators === 'function') {
      window.requestAllIndicators();
    }
  }

  resetToDefaults() {
    console.log('[EnhancedMLChartSystem] ↺ Resetting to defaults...');
    
    // Reset enabled datasets
    Object.keys(this.availableDatasets).forEach(key => {
      this.availableDatasets[key].enabled = ['ml_probability', 'rsi', 'macd', 'volume', 'price'].includes(key);
    });

    // Reset selectors
    const dataSelector = document.getElementById('mlChartDataSelector');
    if (dataSelector) {
      Array.from(dataSelector.options).forEach(option => {
        option.selected = this.availableDatasets[option.value]?.enabled || false;
      });
    }

    const timeframeSelector = document.getElementById('mlChartTimeframe');
    if (timeframeSelector) {
      timeframeSelector.value = '1h';
      this.currentTimeframe = '1h';
    }

    const zoomSelector = document.getElementById('mlChartZoom');
    if (zoomSelector) {
      zoomSelector.value = '24';
      this.currentZoomLevel = 24;
    }

    this.updateChart();
  }
}

// Initialize the enhanced ML chart system
document.addEventListener('DOMContentLoaded', () => {
  window.enhancedMLChartSystem = new EnhancedMLChartSystem();
});

// Also initialize if DOM is already loaded
if (document.readyState !== 'loading') {
  window.enhancedMLChartSystem = new EnhancedMLChartSystem();
}
