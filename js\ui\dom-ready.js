/**
 * DOM Ready and Safe Element Access Utilities
 * Ensures DOM elements are available before accessing them
 */

// Track if DOM is ready
let isDOMReady = false
const readyCallbacks = []

// Check if DOM is already loaded
if (document.readyState === 'complete' ||
    (document.readyState !== 'loading' && !document.documentElement.doScroll)) {
  // Handle it asynchronously to allow other scripts to process first
  setTimeout(domReady, 0)
} else {
  document.addEventListener('DOMContentLoaded', domReady)
  // Fallback for older browsers
  window.addEventListener('load', domReady)
}

// Main DOM ready handler
function domReady() {
  if (isDOMReady) return
  isDOMReady = true

  // Execute all queued callbacks
  while (readyCallbacks.length) {
    try {
      readyCallbacks.shift()()
    } catch (e) {
      console.error('Error in DOM ready callback:', e)
    }
  }
}

/**
 * Execute a function when the DOM is ready
 * @param {Function} fn - Function to execute when DOM is ready
 */
function onDOMReady(fn) {
  if (isDOMReady) {
    setTimeout(fn, 0)
  } else {
    readyCallbacks.push(fn)
  }
}

/**
 * Safe element selector with retry logic
 * @param {string} selector - CSS selector
 * @param {number} [timeout=5000] - Maximum time to wait in ms
 * @returns {Promise<Element>}
 */
function safeQuerySelector(selector, timeout = 5000) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()

    function checkElement() {
      const element = document.querySelector(selector)

      if (element) {
        resolve(element)
        return
      }

      if (Date.now() - startTime >= timeout) {
        reject(new Error(`Element ${selector} not found within ${timeout}ms`))
        return
      }

      requestAnimationFrame(checkElement)
    }

    checkElement()
  })
}

// Export public API
window.DOMReady = {
  ready: onDOMReady,
  query: safeQuerySelector,
  isReady: () => isDOMReady,
}

// Initialize critical components when DOM is ready
onDOMReady(() => {
  console.log('DOM fully loaded and parsed')

  // Initialize any components that need to run after DOM is ready
  initializeCriticalComponents().catch(console.error)
})

async function initializeCriticalComponents() {
  try {
    // Example: Wait for critical elements before initializing
    await Promise.all([
      safeQuerySelector('#signal-matrix').catch(() => {
        console.warn('Signal matrix not found, some features may be limited')
        return null
      }),
      safeQuerySelector('.momentum-table').catch(() => {
        console.warn('Momentum table not found, some features may be limited')
        return null
      }),
    ])

    // Initialize other critical components here
    initializeSignalHandlers()
  } catch (error) {
    console.error('Error initializing critical components:', error)
  }
}

function initializeSignalHandlers() {
  // Initialize signal handlers here
  console.log('Initializing signal handlers')

  // Example: Safe initialization of signal handlers
  const signalMatrix = document.querySelector('#signal-matrix')
  if (signalMatrix) {
    // Initialize signal matrix handlers
    console.log('Signal matrix found, initializing handlers')
  }

  // Add other safe initializations here
}

// Make sure we clean up event listeners when the page unloads
window.addEventListener('beforeunload', () => {
  // Clean up any resources or intervals here
  console.log('Cleaning up resources before unload')
})
