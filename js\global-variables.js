// StarCrypt Global Variables
// Single source of truth for all global variables

// Timeframes and timing - Default 7 timeframes as requested
window.TIMEFRAMES = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
window.LOW_TIMEFRAMES = ['1s', '5s', '15s', '1m', '4m', '24m', '168m']
window.TIMEFRAME_SECONDS = {
  '1s': 1, '5s': 5, '15s': 15, '1m': 60, '4m': 240, '24m': 1440, '168m': 10080,
  '5m': 300, '15m': 900, '30m': 1800, '1h': 3600, '4h': 14400, '1d': 86400, '3d': 259200, '1w': 604800,
}

// Current state - use let instead of const to allow reassignment
window.currentTf = '1h'
window.currentPair = 'xbtusdt'

// Load strategy from localStorage or default to 'admiral_toa'
window.currentStrategy = localStorage.getItem('lastSelectedStrategy') || 'admiral_toa'

// Wait for document to load, then ensure initial strategy is set properly
document.addEventListener('DOMContentLoaded', () => {
  // Set the initial strategy selector value from our global
  const mainStrategySelector = document.getElementById('strategySelector') ||
                              document.getElementById('mainStrategySelector')
  if (mainStrategySelector) {
    mainStrategySelector.value = window.currentStrategy
    // Trigger strategy change to ensure UI updates
    if (typeof window.handleStrategyChange === 'function') {
      window.handleStrategyChange({ target: mainStrategySelector })
    }
  }
})

// Indicators
window.INDICATORS = {
  momentum: ['rsi', 'stochRsi', 'williamsR', 'ultimateOscillator', 'mfi'],
  trend: ['macd', 'bollingerBands', 'adx', 'atr', 'vwap', 'fractal'],
  volume: ['volume'],
  ml: ['ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
}

// Flat list of all indicators for the signal matrix
window.MATRIX_INDICATORS = [
  'rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR',
  'ultimateOscillator', 'mfi', 'adx', 'vwap', 'fractal', 'volume',
  'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly',
]

// Indicator color schemes for consistent visual representation
window.INDICATOR_COLORS = {
  // Momentum indicators
  rsi: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  stochRsi: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  williamsR: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  ultimateOscillator: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  mfi: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },

  // Trend indicators
  macd: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  bollingerBands: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  adx: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  atr: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  vwap: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  fractal: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },

  // Volume indicator
  volume: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },

  // ML indicators
  ml: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  sentiment: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  entropy: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  correlation: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
  time_anomaly: { bullish: '#00FF00', bearish: '#FF0000', neutral: '#808080' },
}

window.INDICATOR_DISPLAY_NAMES = {
  rsi: 'RSI',
  stochRsi: 'Stochastic RSI',
  macd: 'MACD',
  bollingerBands: 'Bollinger Bands',
  adx: 'ADX (Direction)',
  williamsR: 'Williams %R',
  ultimateOscillator: 'Ultimate Oscillator',
  mfi: 'Money Flow Index',
  volume: 'Volume Analysis',
  vwap: 'VWAP Guardian',
  atr: 'ATR (Volatility)',
  fractal: 'Fractal Pattern',
  ml: 'Machine Learning',
  sentiment: 'Sentiment Analysis',
  entropy: 'Market Entropy',
  correlation: 'Market Correlation',
  time_anomaly: 'TIME ANOMALY',
}

window.helperOrder = ['rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx']
window.logMessages = window.logMessages || []
window.enabledIndicators = [
  'rsi', 'stochRsi', 'macd', 'bollingerBands', 'atr', 'adx', 'williamsR',
  'ultimateOscillator', 'mfi', 'vwap', 'fractal', 'volume', 'ml', 'sentiment',
  'entropy', 'correlation', 'time_anomaly',
]

// This is the one true definition of TRADING_STRATEGIES
// All other files should reference this, not redefine it
window.TRADING_STRATEGIES = {
  admiral_toa: {
    name: 'Admiral T.O.A. Convergence',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'atr', 'macd', 'williamsR', 'ultimateOscillator', 'mfi', 'adx', 'vwap', 'fractal', 'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    description: 'The flagship strategy that combines momentum indicators (RSI, Stoch RSI) with trend confirmation (MACD), price position (Bollinger Bands), trend strength (ADX), volume analysis, and ML confirmation for high-confidence signals.',
    helperText: `<p><strong>Step 1: Check Momentum (<span class="indicator">RSI14</span> & <span class="indicator">Stoch RSI</span>)</strong></p>
<p>- Look for <span class="indicator">RSI14</span> > <span class="condition-sell">70 (overbought)</span> or < <span class="condition-buy">30 (oversold)</span>. Confidence: ~65% for reversals.</p>
<p>- Confirm with <span class="indicator">Stoch RSI</span> > <span class="condition-sell">80 (overbought)</span> or < <span class="condition-buy">20 (oversold)</span>. Combined Confidence: ~70%.</p>
<p><strong>Step 2: Confirm with <span class="indicator">Bollinger Bands</span> & <span class="indicator">ATR</span></strong></p>
<p>- Price near the upper band (overbought) or lower band (oversold) with high <span class="indicator">ATR</span> (>1% of price) increases reversal likelihood. Confidence: ~75% when combined with RSI/Stoch RSI.</p>
<p><strong>Step 3: Look for <span class="indicator">MACD</span> Convergence (Shield Logic)</strong></p>
<p>- <span class="indicator">MACD</span> crossing above signal line (bullish) or below (bearish) with RSI/Stoch RSI confirmation. (Shield) blocks trades against trend. Confidence: ~80% for strong signals.</p>
<p><strong>Step 4: Additional Confirmation (<span class="indicator">Williams %R</span>, <span class="indicator">UltOscillator</span>, <span class="indicator">MFI</span>)</strong></p>
<p>- <span class="indicator">Williams %R</span>, <span class="indicator">UltOscillator</span>, and <span class="indicator">MFI</span> aligning with RSI/Stoch RSI (e.g., all overbought) boosts confidence. Confidence: ~85% with 4+ indicators.</p>
<p><strong>Step 5: Check Trend Strength (<span class="indicator">ADX</span>)</strong></p>
<p>- <span class="indicator">ADX</span> > 25 confirms a strong trend, increasing trade reliability. Final convergence confidence: ~90% with all steps aligned.</p>
<p><strong>Step 6: Advanced Trend Confirmation (<span class="indicator">VWAP</span> & <span class="indicator">Fractal</span>)</strong></p>
<p>- <span class="indicator">VWAP</span> position relative to price and <span class="indicator">Fractal</span> patterns provide additional trend confirmation. Confidence: ~90% when aligned with other indicators.</p>
<p><strong>Step 7: Volume & <span class="indicator">ML</span> Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> spike (>1.5x 14-period avg) and <span class="indicator">ML</span> prediction alignment (confidence >70%) add final confirmation. Confidence: ~92% with volume and ML support.</p>
<p><strong>Step 8: Advanced ML Analysis (<span class="indicator">Sentiment</span>, <span class="indicator">Entropy</span>, <span class="indicator">Correlation</span>, <span class="indicator">Time Anomaly</span>)</strong></p>
<p>- Advanced ML indicators provide deep market insights beyond traditional technical analysis. When all ML indicators align, confidence reaches ~95%.</p>
<p><strong>Tip:</strong> Wait for at least 4 indicators to align (e.g., RSI, Stoch RSI, Bollinger Bands, MACD) for a high-probability trade. The Admiral T.O.A. chat will alert you to strong convergences with confidence percentages!</p>`,
    color: '#00FFFF',
  },
  neural_network_navigator: {
    name: 'AI Neural Network Navigator',
    indicators: ['rsi', 'macd', 'bollingerBands', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly'],
    description: 'Advanced AI strategy that uses neural networks to identify complex market patterns and predict price movements with high accuracy by combining traditional indicators with machine learning signals.',
    helperText: `<p><strong>Step 1: AI Pattern Recognition (<span class="indicator">ML</span>)</strong></p>
<p>- <span class="indicator">ML</span> model analyzes historical price patterns to identify high-probability setups with <span class="condition-trend">confidence score >75%</span>. Base confidence: ~80%.</p>
<p><strong>Step 2: Sentiment Analysis (<span class="indicator">Sentiment</span>)</strong></p>
<p>- <span class="indicator">Sentiment</span> indicator measures market mood from social media and news sources. <span class="condition-buy">Positive sentiment >65%</span> or <span class="condition-sell">negative sentiment >65%</span> confirms direction. Confidence: ~85% when aligned with ML.</p>
<p><strong>Step 3: Technical Confirmation (<span class="indicator">RSI</span> & <span class="indicator">MACD</span>)</strong></p>
<p>- <span class="indicator">RSI</span> and <span class="indicator">MACD</span> must align with AI predictions to filter false signals. Confidence: ~88% with technical confirmation.</p>
<p><strong>Step 4: Volatility Assessment (<span class="indicator">Bollinger Bands</span>)</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> width indicates market volatility. Narrow bands before expansion signal potential breakouts. Confidence: ~90% with volatility confirmation.</p>
<p><strong>Step 5: Advanced AI Metrics (<span class="indicator">Entropy</span> & <span class="indicator">Correlation</span>)</strong></p>
<p>- <span class="indicator">Entropy</span> measures market randomness while <span class="indicator">Correlation</span> tracks relationships with other assets. Low entropy and diverging correlations signal high-confidence setups. Confidence: ~92%.</p>
<p><strong>Step 6: Time Series Analysis (<span class="indicator">Time Anomaly</span>)</strong></p>
<p>- <span class="indicator">Time Anomaly</span> detector identifies unusual market behavior across multiple timeframes. When anomalies align with predictions, confidence reaches ~95%.</p>
<p><strong>Tip:</strong> Neural Network Navigator works best when at least 3 AI indicators (ML, Sentiment, Entropy, Correlation, Time Anomaly) align with traditional technical indicators. The AI confidence score in the helper panel shows overall signal strength!</p>`,
    color: '#FF00FF',
  },
  momentum_blast: {
    name: 'Momentum Blast',
    indicators: ['rsi', 'stochRsi', 'macd', 'mfi', 'volume'],
    description: 'A more aggressive strategy focusing on momentum indicators with looser thresholds for more frequent trading signals.',
    helperText: `<p><strong>Step 1: Quick Momentum Check (<span class="indicator">RSI</span> & <span class="indicator">Stoch RSI</span>)</strong></p>
<p>- <span class="indicator">RSI</span> > <span class="condition-sell">60 (momentum up)</span> or < <span class="condition-buy">40 (momentum down)</span>. Looser thresholds for faster signals. Confidence: ~60%.</p>
<p>- <span class="indicator">Stoch RSI</span> crossing above/below 50 line indicates momentum shift. Confidence: ~65% when aligned with RSI.</p>
<p><strong>Step 2: MACD Momentum Confirmation</strong></p>
<p>- <span class="indicator">MACD</span> line crossing signal line (any direction) confirms momentum. Fast signals with moderate confidence: ~70%.</p>
<p><strong>Step 3: Money Flow Analysis (<span class="indicator">MFI</span>)</strong></p>
<p>- <span class="indicator">MFI</span> > 60 (buying pressure) or < 40 (selling pressure) adds volume-weighted momentum confirmation. Confidence: ~75%.</p>
<p><strong>Step 4: Volume Spike Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> > 1.2x average confirms momentum with institutional participation. Final confidence: ~80% with volume support.</p>
<p><strong>Tip:</strong> Momentum Blast is designed for quick entries/exits. Take profits faster and use tighter stops. Best for volatile markets and short-term trades!</p>`,
    color: '#FF0000',
  },
  tight_convergence: {
    name: 'Tight Convergence',
    indicators: ['bollingerBands', 'atr', 'adx', 'vwap', 'macd', 'volume'],
    description: 'Focuses on price consolidation patterns and breakouts from tight ranges using volatility and trend indicators for high-probability setups.',
    helperText: `<p><strong>Step 1: Identify Consolidation (<span class="indicator">Bollinger Bands</span> & <span class="indicator">ATR</span>)</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> width < 2% of price indicates tight consolidation. <span class="indicator">ATR</span> should be declining. Confidence: ~70% for upcoming breakout.</p>
<p><strong>Step 2: Trend Strength Assessment (<span class="indicator">ADX</span>)</strong></p>
<p>- <span class="indicator">ADX</span> < 25 confirms sideways market. Rising ADX from low levels signals potential breakout direction. Confidence: ~75%.</p>
<p><strong>Step 3: Value Zone Analysis (<span class="indicator">VWAP</span>)</strong></p>
<p>- Price oscillating around <span class="indicator">VWAP</span> indicates fair value. Breakout above/below VWAP with volume confirms direction. Confidence: ~80%.</p>
<p><strong>Step 4: Momentum Confirmation (<span class="indicator">MACD</span>)</strong></p>
<p>- <span class="indicator">MACD</span> histogram flattening then expanding confirms breakout momentum. Confidence: ~85% with MACD alignment.</p>
<p><strong>Step 5: Volume Breakout Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> spike >1.5x average on breakout confirms institutional participation. Final confidence: ~90% with volume confirmation.</p>
<p><strong>Tip:</strong> Wait for the squeeze to release! Best entries are on the initial breakout with volume. Set stops just inside the consolidation range.</p>`,
    color: '#FFFF00',
  },
  scalping_sniper: {
    name: 'Scalping Sniper',
    indicators: ['rsi', 'macd', 'volume', 'bollingerBands', 'atr'],
    description: 'A rapid-fire strategy designed for capturing quick profits from short-term price movements using momentum and volatility indicators.',
    helperText: `<p><strong>Step 1: Quick RSI Scalp Setup</strong></p>
<p>- <span class="indicator">RSI</span> bouncing off 50 line or extreme levels (30/70). Fast momentum shifts for quick entries. Confidence: ~65%.</p>
<p><strong>Step 2: MACD Micro-Trends</strong></p>
<p>- <span class="indicator">MACD</span> histogram changing color (green to red or vice versa) signals micro-trend shifts. Confidence: ~70%.</p>
<p><strong>Step 3: Volume Spike Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> > 1.3x average confirms institutional interest in the move. Confidence: ~75%.</p>
<p><strong>Step 4: Bollinger Band Bounces</strong></p>
<p>- Price touching <span class="indicator">Bollinger Bands</span> edges provides clear entry/exit levels. Confidence: ~80% for mean reversion.</p>
<p><strong>Step 5: ATR-Based Targets</strong></p>
<p>- Use <span class="indicator">ATR</span> to set profit targets (0.3-0.5x ATR) and stops (0.2x ATR). Risk management is key!</p>
<p><strong>Tip:</strong> Scalping requires speed and discipline. Take profits quickly, cut losses faster. Best on 1m-5m timeframes during high volatility periods!</p>`,
    color: '#4B0082',
  },
  top_bottom_feeder: {
    name: 'Top/Bottom Feeder',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'mfi', 'volume', 'williamsR', 'ultimateOscillator'],
    description: 'Specialized for catching market extremes, focusing on deeply oversold or overbought conditions across multiple indicators.',
    helperText: `<p><strong>Step 1: Extreme RSI Conditions</strong></p>
<p>- <span class="indicator">RSI</span> < <span class="condition-buy">20 (deeply oversold)</span> or > <span class="condition-sell">85 (deeply overbought)</span>. Extreme levels for reversal hunting. Confidence: ~70%.</p>
<p><strong>Step 2: Stochastic Confirmation</strong></p>
<p>- <span class="indicator">Stoch RSI</span> < <span class="condition-buy">10 (extreme oversold)</span> or > <span class="condition-sell">90 (extreme overbought)</span>. Double confirmation of extremes. Confidence: ~75%.</p>
<p><strong>Step 3: Bollinger Band Extremes</strong></p>
<p>- Price touching or exceeding <span class="indicator">Bollinger Bands</span> outer edges with band width >3% indicates extreme conditions. Confidence: ~80%.</p>
<p><strong>Step 4: Money Flow Confirmation</strong></p>
<p>- <span class="indicator">MFI</span> < <span class="condition-buy">15 (extreme selling)</span> or > <span class="condition-sell">85 (extreme buying)</span> confirms volume-weighted extremes. Confidence: ~85%.</p>
<p><strong>Step 5: Williams %R & Ultimate Oscillator</strong></p>
<p>- <span class="indicator">Williams %R</span> and <span class="indicator">Ultimate Oscillator</span> at extreme levels provide additional confirmation. All oscillators aligned: ~90% confidence.</p>
<p><strong>Step 6: Volume Spike Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> spike >2x average during extreme readings indicates capitulation or euphoria. Final confidence: ~92%.</p>
<p><strong>Tip:</strong> Wait for multiple oscillators to reach extreme levels simultaneously. Best for swing trading and catching major reversals!</p>`,
    color: '#FFA500',
  },
  trend_rider: {
    name: 'Trend Rider',
    indicators: ['adx', 'macd', 'rsi', 'williamsR', 'volume', 'vwap', 'atr'],
    description: 'Focuses on riding established trends by combining ADX for trend strength with momentum indicators for entry timing.',
    helperText: `<p><strong>Step 1: Confirm Strong Trend (<span class="indicator">ADX</span>)</strong></p>
<p>- <span class="indicator">ADX</span> > <span class="condition-trend">30 (strong trend)</span> or > <span class="condition-trend">40 (very strong trend)</span>. Higher ADX = better trend riding opportunities. Confidence: ~75%.</p>
<p><strong>Step 2: MACD Trend Alignment</strong></p>
<p>- <span class="indicator">MACD</span> line above signal line (uptrend) or below (downtrend) with histogram expanding. Trend momentum confirmation. Confidence: ~80%.</p>
<p><strong>Step 3: RSI Trend Position</strong></p>
<p>- In uptrends: <span class="indicator">RSI</span> staying above 40-50 range. In downtrends: RSI staying below 50-60 range. Trend strength gauge. Confidence: ~85%.</p>
<p><strong>Step 4: VWAP Trend Reference</strong></p>
<p>- Price consistently above <span class="indicator">VWAP</span> (uptrend) or below (downtrend) with increasing distance. Institutional trend confirmation. Confidence: ~88%.</p>
<p><strong>Step 5: Williams %R Entry Timing</strong></p>
<p>- <span class="indicator">Williams %R</span> pullbacks to -20 to -40 range in uptrends or -60 to -80 in downtrends for entry timing. Confidence: ~90%.</p>
<p><strong>Step 6: Volume & ATR Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> above average and <span class="indicator">ATR</span> expanding confirm trend strength and momentum. Final confidence: ~92%.</p>
<p><strong>Tip:</strong> Only trade in the direction of the established trend. Use pullbacks for entries, not reversals. Best for trending markets!</p>`,
    color: '#00FF00',
  },
  random_walk: {
    name: 'Random Walk Prototype',
    indicators: ['rsi', 'macd', 'bollingerBands', 'adx', 'volume', 'williamsR'],
    description: 'An experimental strategy that combines technical analysis with random elements to identify potential market inefficiencies and sentiment shifts.',
    color: '#C0C0C0',
  },
  time_warp_scalper: {
    name: 'Time Warp Scalper',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'macd', 'williamsR', 'time_anomaly', 'volume'],
    description: 'A multi-timeframe scalping strategy that identifies temporary time-based anomalies for short-term trading opportunities.',
    color: '#800080',
  },
  ml_predictor: {
    name: 'ML Predictor',
    indicators: ['rsi', 'macd', 'ml', 'sentiment', 'correlation'],
    description: 'Uses machine learning algorithms to predict short-term price movements based on historical patterns and current market conditions.',
    color: '#1E90FF',
  },
  deep_learning_diver: {
    name: 'AI Deep Learning Diver',
    indicators: ['rsi', 'stochRsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation', 'entropy'],
    description: 'Advanced AI strategy using deep learning neural networks to analyze market patterns across multiple timeframes and data sources.',
    color: '#4169E1',
  },
  ai_pattern_prophet: {
    name: 'AI Pattern Prophet',
    indicators: ['bollingerBands', 'macd', 'adx', 'ml', 'entropy', 'time_anomaly', 'fractal', 'volume'],
    description: 'Identifies recurring chart patterns and forecasts their completion probability using AI pattern recognition algorithms.',
    color: '#8A2BE2',
  },
  machine_learning_momentum: {
    name: 'AI ML Momentum Master',
    indicators: ['rsi', 'macd', 'williamsR', 'ml', 'volume', 'sentiment', 'correlation'],
    description: 'Enhanced momentum analysis powered by machine learning to identify the most statistically significant momentum signals.',
    color: '#9932CC',
  },
  sentiment_analysis_surfer: {
    name: 'AI Sentiment Analysis Surfer',
    indicators: ['rsi', 'macd', 'ml', 'sentiment', 'volume', 'correlation', 'entropy'],
    description: 'Combines social media sentiment analysis with technical indicators to identify potential market mood shifts.',
    color: '#BA55D3',
  },
  fractal_surge: {
    name: 'Fractal Surge',
    indicators: ['atr', 'macd', 'volume', 'fractal', 'bollingerBands', 'adx', 'rsi', 'stochRsi'],
    description: 'Focuses on fractal patterns and breakouts from established ranges for explosive moves.',
    helperText: `<p><strong>Step 1: Identify Fractal Patterns (<span class="indicator">Fractal</span>)</strong></p>
<p>- <span class="indicator">Fractal</span> highs and lows form support/resistance levels. Look for fractal breaks with momentum. Confidence: ~70%.</p>
<p><strong>Step 2: Volatility Expansion (<span class="indicator">ATR</span>)</strong></p>
<p>- <span class="indicator">ATR</span> expanding from low levels (>1.5x recent average) signals potential explosive moves. Confidence: ~75%.</p>
<p><strong>Step 3: MACD Momentum Surge</strong></p>
<p>- <span class="indicator">MACD</span> histogram rapidly expanding with line crossing signal confirms momentum surge. Confidence: ~80%.</p>
<p><strong>Step 4: Bollinger Band Breakout</strong></p>
<p>- Price breaking <span class="indicator">Bollinger Bands</span> with band expansion indicates range breakout. Confidence: ~85%.</p>
<p><strong>Step 5: Trend Strength Confirmation (<span class="indicator">ADX</span>)</strong></p>
<p>- <span class="indicator">ADX</span> rising from below 25 to above 30 confirms emerging trend strength. Confidence: ~88%.</p>
<p><strong>Step 6: RSI & Stoch RSI Momentum</strong></p>
<p>- <span class="indicator">RSI</span> and <span class="indicator">Stoch RSI</span> breaking above 60 (bullish) or below 40 (bearish) with momentum. Confidence: ~90%.</p>
<p><strong>Step 7: Volume Surge Confirmation</strong></p>
<p>- <span class="indicator">Volume</span> spike >2x average confirms institutional participation in the surge. Final confidence: ~93%.</p>
<p><strong>Tip:</strong> Wait for fractal breaks with multiple confirmations. Best for catching explosive breakout moves from consolidation!</p>`,
    color: '#FFFF00',
  },
  sentiment_blaster: {
    name: 'Sentiment Blaster',
    indicators: ['rsi', 'macd', 'volume', 'mfi', 'williamsR'],
    description: 'Analyzes market sentiment to identify extreme conditions for contrarian trading opportunities.',
    color: '#800080',
  },
  x_sentiment_blaster: {
    name: 'X Sentiment Blaster',
    indicators: ['rsi', 'macd', 'volume', 'mfi', 'williamsR', 'sentiment', 'ultimateOscillator'],
    description: 'AI-powered strategy that analyzes market sentiment through volume and momentum indicators, with confirmation from oscillators for sentiment-driven trades.',
    color: '#FF9800',
  },
  vwap_guardian: {
    name: 'VWAP Guardian',
    indicators: ['vwap', 'rsi', 'macd', 'volume', 'bollingerBands'],
    description: 'Uses Volume Weighted Average Price (VWAP) as a key reference point for identifying value zones and price deviations.',
    color: '#4682B4',
  },
  correlation_hunter: {
    name: 'Correlation Hunter',
    indicators: ['correlation', 'rsi', 'macd', 'volume', 'bollingerBands'],
    description: 'Analyzes correlations between different assets to identify divergences and potential reversals.',
    color: '#FF6347',
  },
  quantum_entropy: {
    name: 'Quantum Entropy',
    indicators: ['bollingerBands', 'atr', 'adx', 'williamsR', 'macd', 'volume'],
    description: 'Measures market chaos and volatility to identify potential breakouts from equilibrium states.',
    helperText: `<p><strong>Step 1: Measure Market Chaos (<span class="indicator">Bollinger Bands</span> & <span class="indicator">ATR</span>)</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> width <2% indicates low entropy (order). <span class="indicator">ATR</span> at multi-period lows confirms equilibrium state. Confidence: ~70%.</p>
<p><strong>Step 2: Trend Strength Assessment (<span class="indicator">ADX</span>)</strong></p>
<p>- <span class="indicator">ADX</span> <20 indicates low directional movement (high entropy). Rising ADX signals entropy reduction and trend emergence. Confidence: ~75%.</p>
<p><strong>Step 3: Oscillator Equilibrium (<span class="indicator">Williams %R</span>)</strong></p>
<p>- <span class="indicator">Williams %R</span> oscillating between -40 to -60 range indicates balanced market forces. Breakout signals entropy shift. Confidence: ~80%.</p>
<p><strong>Step 4: MACD Entropy Detection</strong></p>
<p>- <span class="indicator">MACD</span> histogram near zero with lines converging indicates momentum equilibrium. Divergence signals entropy change. Confidence: ~85%.</p>
<p><strong>Step 5: Volume Entropy Analysis</strong></p>
<p>- <span class="indicator">Volume</span> below average with low variance indicates market equilibrium. Volume spike signals entropy breakout. Confidence: ~88%.</p>
<p><strong>Step 6: Quantum State Transition</strong></p>
<p>- When all indicators simultaneously break equilibrium (bands expand, ADX rises, volume spikes), quantum state transition occurs. Final confidence: ~92%.</p>
<p><strong>Tip:</strong> Trade the transition from order (low entropy) to chaos (high entropy). Best for catching major market shifts from consolidation!</p>`,
    color: '#800000',
  },
  cross_asset_nebula: {
    name: 'Cross-Asset Nebula',
    indicators: ['rsi', 'macd', 'bollingerBands', 'adx', 'atr'],
    description: 'Analyzes correlations between crypto and traditional markets to identify unique opportunities.',
    helperText: `<p><strong>Step 1: RSI Divergence Analysis</strong></p>
<p>- <span class="indicator">RSI</span> divergence between crypto and traditional markets (stocks/bonds) signals unique opportunities. Confidence: ~70%.</p>
<p><strong>Step 2: MACD Cross-Asset Momentum</strong></p>
<p>- <span class="indicator">MACD</span> momentum shifts in crypto while traditional markets remain stable indicate independent moves. Confidence: ~75%.</p>
<p><strong>Step 3: Bollinger Band Relative Position</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> position relative to traditional asset bands shows relative value opportunities. Confidence: ~80%.</p>
<p><strong>Step 4: Trend Strength Comparison (<span class="indicator">ADX</span>)</strong></p>
<p>- <span class="indicator">ADX</span> showing strong crypto trends while traditional markets are sideways indicates crypto-specific catalysts. Confidence: ~85%.</p>
<p><strong>Step 5: Volatility Regime Analysis (<span class="indicator">ATR</span>)</strong></p>
<p>- <span class="indicator">ATR</span> expansion in crypto during traditional market calm signals unique volatility opportunities. Confidence: ~88%.</p>
<p><strong>Step 6: Cross-Asset Convergence</strong></p>
<p>- When all indicators align for crypto-specific moves independent of traditional markets, confidence reaches ~90%.</p>
<p><strong>Tip:</strong> Best during market decoupling events. Look for crypto-specific catalysts when traditional markets are stable!</p>`,
    color: '#0000CD',
  },
  time_anomaly_detector: {
    name: 'Time Anomaly Detector',
    indicators: ['rsi', 'macd', 'time_anomaly', 'volume', 'bollingerBands'],
    description: 'Identifies statistical anomalies in price returns across different time periods for unique trading opportunities.',
    helperText: `<p><strong>Step 1: Time Anomaly Detection (<span class="indicator">Time Anomaly</span>)</strong></p>
<p>- <span class="indicator">Time Anomaly</span> indicator identifies unusual price patterns across multiple timeframes. Anomaly score >75% signals unique opportunities. Confidence: ~75%.</p>
<p><strong>Step 2: RSI Anomaly Confirmation</strong></p>
<p>- <span class="indicator">RSI</span> showing unusual readings relative to historical patterns confirms time-based anomalies. Confidence: ~80%.</p>
<p><strong>Step 3: MACD Pattern Recognition</strong></p>
<p>- <span class="indicator">MACD</span> displaying atypical convergence/divergence patterns supports anomaly detection. Confidence: ~85%.</p>
<p><strong>Step 4: Volume Anomaly Analysis</strong></p>
<p>- <span class="indicator">Volume</span> spikes or unusual patterns during anomaly periods confirm institutional awareness. Confidence: ~88%.</p>
<p><strong>Step 5: Bollinger Band Anomaly</strong></p>
<p>- <span class="indicator">Bollinger Bands</span> showing unusual expansion/contraction patterns during anomaly periods. Final confidence: ~90%.</p>
<p><strong>Tip:</strong> Best for catching unique market inefficiencies. Trade anomalies that persist across multiple timeframes!</p>`,
    color: '#FF1493',
  },
  volatility_breakout_hunter: {
    name: 'Volatility Breakout Hunter',
    indicators: ['atr', 'bollingerBands', 'adx', 'volume', 'rsi', 'macd'],
    description: 'Specializes in detecting and trading volatility breakouts from periods of low volatility consolidation.',
    color: '#32CD32',
  },
  mean_reversion_master: {
    name: 'Mean Reversion Master',
    indicators: ['rsi', 'stochRsi', 'bollingerBands', 'williamsR', 'mfi', 'vwap'],
    description: 'Focuses on identifying extreme price deviations from mean values for high-probability reversal trades.',
    color: '#FF4500',
  },
  momentum_divergence_spotter: {
    name: 'Momentum Divergence Spotter',
    indicators: ['rsi', 'macd', 'stochRsi', 'mfi', 'volume', 'ultimateOscillator'],
    description: 'Identifies divergences between price action and momentum indicators to predict potential reversals.',
    color: '#8B008B',
  },
  volume_profile_analyzer: {
    name: 'Volume Profile Analyzer',
    indicators: ['volume', 'vwap', 'rsi', 'macd', 'bollingerBands', 'atr'],
    description: 'Uses volume analysis and VWAP to identify key support/resistance levels and institutional activity.',
    color: '#20B2AA',
  },
  multi_timeframe_confluence: {
    name: 'Multi-Timeframe Confluence',
    indicators: ['rsi', 'macd', 'adx', 'bollingerBands', 'volume', 'atr', 'vwap'],
    description: 'Analyzes multiple timeframes simultaneously to identify high-confluence trading setups with maximum probability.',
    color: '#B22222',
  },
  ichimoku_cloud_master: {
    name: 'Ichimoku Cloud Master',
    indicators: ['ichimoku', 'rsi', 'volume', 'atr', 'parabolicSar'],
    description: 'Comprehensive Ichimoku cloud analysis with trend confirmation using multiple technical indicators.',
    color: '#FF6B6B',
  },
  volume_flow_hunter: {
    name: 'Volume Flow Hunter',
    indicators: ['obv', 'chaikinMoneyFlow', 'mfi', 'volume', 'vwap'],
    description: 'Advanced volume flow and money flow analysis to track institutional movements and smart money.',
    color: '#4ECDC4',
  },
  momentum_oscillator_pro: {
    name: 'Momentum Oscillator Pro',
    indicators: ['cci', 'rsi', 'stochRsi', 'williamsR', 'ultimateOscillator'],
    description: 'Multi-oscillator momentum convergence analysis for precise entry and exit timing.',
    color: '#45B7D1',
  },
  trend_strength_analyzer: {
    name: 'Trend Strength Analyzer',
    indicators: ['aroon', 'adx', 'parabolicSar', 'trix', 'macd'],
    description: 'Comprehensive trend strength and direction analysis using advanced trend indicators.',
    color: '#F7DC6F',
  },
  institutional_flow_tracker: {
    name: 'Institutional Flow Tracker',
    indicators: ['vwap', 'obv', 'chaikinMoneyFlow', 'volume', 'correlation'],
    description: 'Track institutional money flow and smart money movements for high-probability trades.',
    color: '#BB8FCE',
  },
}

// Define a function to ensure strategy switching updates all necessary states
window.switchStrategy = function (strategy) {
  if (!window.TRADING_STRATEGIES[strategy]) {
    console.error(`Invalid strategy: ${strategy}`)
    return false
  }

  window.currentStrategy = strategy
  console.log(`Strategy switched to: ${window.currentStrategy}`)

  // Update all strategy selectors in the UI
  const strategySelectors = document.querySelectorAll('#mainStrategySelector, .strategy-selector')
  strategySelectors.forEach(selector => {
    if (selector && selector.value !== strategy) {
      selector.value = strategy
    }
  })

  // Update UI and data as needed
  if (typeof updateSignalMatrix === 'function') {
    updateSignalMatrix()
  }

  // If we have updateStrategy function from WebSocket handler, use it
  if (typeof window.updateStrategy === 'function') {
    window.updateStrategy(strategy)
  } else {
    // Fallback if the WebSocket handler isn't loaded yet
    if (typeof ws !== 'undefined' && ws && ws.readyState === 1) {
      ws.send(JSON.stringify({
        type: 'strategy_change',
        strategy: window.currentStrategy,
      }))
    }
  }

  return true
}

console.log('Global variables initialized successfully')
