/**
 * Mini Charts Fix - Proper Height and Multi-line Indicators
 */

class MiniChartsFix {
  constructor() {
    this.targetHeight = 40;
    this.multiLineIndicators = ['macd', 'bollingerBands', 'stochRsi', 'ichimoku', 'aroon'];
    this.oscillatorIndicators = ['rsi', 'stochRsi', 'mfi', 'williamsR', 'ultimateOscillator', 'cci'];
    this.init();
  }

  init() {
    console.log('[MiniChartsFix] 🔧 Fixing mini charts rendering...');
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.fixAllMiniCharts());
    } else {
      this.fixAllMiniCharts();
    }
  }

  fixAllMiniCharts() {
    // Fix existing mini charts
    this.fixExistingCharts();
    
    // Override chart creation functions
    this.overrideChartCreation();
    
    // Add CSS for proper styling
    this.addMiniChartCSS();
    
    console.log('[MiniChartsFix] ✅ Mini charts fixed');
  }

  fixExistingCharts() {
    // Find all mini chart containers
    const chartContainers = document.querySelectorAll('.mini-chart, .indicator-chart, [id*="Chart"], canvas[id*="chart"]');
    
    chartContainers.forEach(container => {
      this.fixSingleChart(container);
    });
  }

  fixSingleChart(container) {
    // Set proper height
    container.style.height = `${this.targetHeight}px`;
    
    if (container.tagName === 'CANVAS') {
      container.height = this.targetHeight;
      container.style.height = `${this.targetHeight}px`;
    }

    // Get indicator name from container
    const indicatorName = this.getIndicatorName(container);
    
    if (indicatorName) {
      // Apply specific styling based on indicator type
      this.applyIndicatorSpecificStyling(container, indicatorName);
      
      // Recreate chart if it's a multi-line indicator
      if (this.multiLineIndicators.includes(indicatorName)) {
        this.recreateMultiLineChart(container, indicatorName);
      }
    }
  }

  getIndicatorName(container) {
    // Try various methods to get indicator name
    const id = container.id || '';
    const className = container.className || '';
    const parentId = container.parentElement?.id || '';
    
    // Extract from ID
    if (id.includes('Chart')) {
      return id.replace('Chart', '').toLowerCase();
    }
    
    // Extract from class
    const classMatch = className.match(/(\w+)-chart/);
    if (classMatch) {
      return classMatch[1].toLowerCase();
    }
    
    // Extract from parent
    const parentMatch = parentId.match(/(\w+)-row/);
    if (parentMatch) {
      return parentMatch[1].toLowerCase();
    }
    
    // Look for data attributes
    const dataIndicator = container.getAttribute('data-indicator') || 
                         container.parentElement?.getAttribute('data-indicator');
    if (dataIndicator) {
      return dataIndicator.toLowerCase();
    }
    
    return null;
  }

  applyIndicatorSpecificStyling(container, indicatorName) {
    // Add indicator-specific class
    container.classList.add(`${indicatorName}-mini-chart`);
    
    // Set specific styling based on indicator type
    if (this.oscillatorIndicators.includes(indicatorName)) {
      container.classList.add('oscillator-chart');
    }
    
    if (this.multiLineIndicators.includes(indicatorName)) {
      container.classList.add('multi-line-chart');
    }
  }

  recreateMultiLineChart(container, indicatorName) {
    if (!window.Chart || container.tagName !== 'CANVAS') return;
    
    // Destroy existing chart
    const existingChart = Chart.getChart(container);
    if (existingChart) {
      existingChart.destroy();
    }
    
    // Create new chart with proper configuration
    const ctx = container.getContext('2d');
    const chartConfig = this.getChartConfig(indicatorName);
    
    new Chart(ctx, chartConfig);
  }

  getChartConfig(indicatorName) {
    const baseConfig = {
      type: 'line',
      data: {
        labels: this.generateTimeLabels(),
        datasets: this.getDatasets(indicatorName)
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            enabled: true,
            mode: 'index',
            intersect: false
          }
        },
        scales: {
          x: {
            display: false
          },
          y: {
            display: false,
            beginAtZero: this.shouldBeginAtZero(indicatorName)
          }
        },
        elements: {
          point: {
            radius: 0,
            hoverRadius: 2
          },
          line: {
            borderWidth: 1
          }
        },
        interaction: {
          intersect: false,
          mode: 'index'
        }
      }
    };

    return baseConfig;
  }

  getDatasets(indicatorName) {
    switch (indicatorName) {
      case 'macd':
        return [
          {
            label: 'MACD',
            data: this.generateSampleData(20),
            borderColor: '#00ffff',
            backgroundColor: 'transparent',
            borderWidth: 1
          },
          {
            label: 'Signal',
            data: this.generateSampleData(20),
            borderColor: '#ff6b6b',
            backgroundColor: 'transparent',
            borderWidth: 1
          },
          {
            label: 'Histogram',
            data: this.generateSampleData(20),
            borderColor: '#4ecdc4',
            backgroundColor: 'rgba(78, 205, 196, 0.3)',
            borderWidth: 1,
            type: 'bar'
          }
        ];
      
      case 'bollingerBands':
        return [
          {
            label: 'Upper',
            data: this.generateSampleData(20, 110),
            borderColor: '#ffeaa7',
            backgroundColor: 'transparent',
            borderWidth: 1
          },
          {
            label: 'Middle',
            data: this.generateSampleData(20, 100),
            borderColor: '#74b9ff',
            backgroundColor: 'transparent',
            borderWidth: 1
          },
          {
            label: 'Lower',
            data: this.generateSampleData(20, 90),
            borderColor: '#fab1a0',
            backgroundColor: 'transparent',
            borderWidth: 1
          }
        ];
      
      case 'stochRsi':
        return [
          {
            label: '%K',
            data: this.generateSampleData(20, 50, 0, 100),
            borderColor: '#a29bfe',
            backgroundColor: 'transparent',
            borderWidth: 1
          },
          {
            label: '%D',
            data: this.generateSampleData(20, 50, 0, 100),
            borderColor: '#fd79a8',
            backgroundColor: 'transparent',
            borderWidth: 1
          }
        ];
      
      case 'ichimoku':
        return [
          {
            label: 'Tenkan',
            data: this.generateSampleData(20),
            borderColor: '#ff7675',
            backgroundColor: 'transparent',
            borderWidth: 1
          },
          {
            label: 'Kijun',
            data: this.generateSampleData(20),
            borderColor: '#74b9ff',
            backgroundColor: 'transparent',
            borderWidth: 1
          },
          {
            label: 'Senkou A',
            data: this.generateSampleData(20),
            borderColor: '#00b894',
            backgroundColor: 'rgba(0, 184, 148, 0.2)',
            borderWidth: 1,
            fill: '+1'
          },
          {
            label: 'Senkou B',
            data: this.generateSampleData(20),
            borderColor: '#e17055',
            backgroundColor: 'rgba(225, 112, 85, 0.2)',
            borderWidth: 1
          }
        ];
      
      case 'aroon':
        return [
          {
            label: 'Aroon Up',
            data: this.generateSampleData(20, 50, 0, 100),
            borderColor: '#00b894',
            backgroundColor: 'transparent',
            borderWidth: 1
          },
          {
            label: 'Aroon Down',
            data: this.generateSampleData(20, 50, 0, 100),
            borderColor: '#e17055',
            backgroundColor: 'transparent',
            borderWidth: 1
          }
        ];
      
      default:
        return [
          {
            label: indicatorName.toUpperCase(),
            data: this.generateSampleData(20),
            borderColor: '#00ffff',
            backgroundColor: 'transparent',
            borderWidth: 1
          }
        ];
    }
  }

  generateTimeLabels() {
    const labels = [];
    for (let i = 19; i >= 0; i--) {
      labels.push(`-${i}`);
    }
    return labels;
  }

  generateSampleData(length, base = 50, min = 0, max = 100) {
    const data = [];
    let current = base;
    
    for (let i = 0; i < length; i++) {
      current += (Math.random() - 0.5) * 10;
      current = Math.max(min, Math.min(max, current));
      data.push(parseFloat(current.toFixed(2)));
    }
    
    return data;
  }

  shouldBeginAtZero(indicatorName) {
    const zeroBasedIndicators = ['volume', 'atr', 'adx'];
    return zeroBasedIndicators.includes(indicatorName);
  }

  overrideChartCreation() {
    // Override any existing chart creation functions
    const originalCreateChart = window.createMiniChart;
    if (originalCreateChart) {
      window.createMiniChart = (container, indicatorName, data) => {
        // Set proper height first
        container.style.height = `${this.targetHeight}px`;
        if (container.tagName === 'CANVAS') {
          container.height = this.targetHeight;
        }
        
        // Apply indicator-specific styling
        this.applyIndicatorSpecificStyling(container, indicatorName);
        
        // Call original function
        const result = originalCreateChart(container, indicatorName, data);
        
        // Fix the chart after creation
        setTimeout(() => this.fixSingleChart(container), 100);
        
        return result;
      };
    }
  }

  addMiniChartCSS() {
    const style = document.createElement('style');
    style.id = 'mini-charts-fix-css';
    style.textContent = `
      /* Mini Chart Fixed Styling */
      .mini-chart, .indicator-chart, [id*="Chart"], canvas[id*="chart"] {
        height: ${this.targetHeight}px !important;
        max-height: ${this.targetHeight}px !important;
        min-height: ${this.targetHeight}px !important;
      }

      /* Ensure row alignment */
      .indicator-row, .momentum-row {
        display: flex;
        align-items: center;
        height: ${this.targetHeight + 10}px;
      }

      .indicator-name {
        display: flex;
        align-items: center;
        height: ${this.targetHeight}px;
      }

      /* Multi-line chart styling */
      .multi-line-chart {
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 3px;
      }

      /* Oscillator chart styling */
      .oscillator-chart {
        background: linear-gradient(to bottom, 
          rgba(255, 0, 0, 0.1) 0%, 
          rgba(255, 165, 0, 0.1) 25%, 
          rgba(128, 128, 128, 0.1) 50%, 
          rgba(0, 0, 255, 0.1) 75%, 
          rgba(0, 255, 0, 0.1) 100%);
      }

      /* Specific indicator styling */
      .macd-mini-chart {
        background: linear-gradient(to right, rgba(0, 255, 255, 0.05), rgba(255, 107, 107, 0.05));
      }

      .rsi-mini-chart {
        background: linear-gradient(to bottom, 
          rgba(255, 0, 0, 0.1) 0%, 
          rgba(255, 0, 0, 0.05) 30%, 
          rgba(128, 128, 128, 0.05) 50%, 
          rgba(0, 255, 0, 0.05) 70%, 
          rgba(0, 255, 0, 0.1) 100%);
      }

      .stochrsi-mini-chart {
        background: linear-gradient(to bottom, 
          rgba(162, 155, 254, 0.1) 0%, 
          rgba(253, 121, 168, 0.1) 100%);
      }

      .volume-mini-chart {
        background: linear-gradient(to top, rgba(69, 183, 209, 0.1), transparent);
      }
    `;

    // Remove existing style if present
    const existingStyle = document.getElementById('mini-charts-fix-css');
    if (existingStyle) existingStyle.remove();

    document.head.appendChild(style);
  }
}

// Initialize the mini charts fix
document.addEventListener('DOMContentLoaded', () => {
  window.miniChartsFix = new MiniChartsFix();
});

// Also initialize if DOM is already loaded
if (document.readyState !== 'loading') {
  window.miniChartsFix = new MiniChartsFix();
}
