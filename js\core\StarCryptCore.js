/**
 * StarCrypt Core - Unified Application Core System
 * Orchestrates all core modules and provides a single entry point
 */

class StarCryptCore {
  constructor(options = {}) {
    // Core modules
    this.modules = {
      config: null,
      websocket: null,
      strategy: null,
      signal: null
    };

    // Initialization state
    this.isInitialized = false;
    this.isInitializing = false;
    this.initializationPromise = null;

    // Event system
    this.eventListeners = new Map();

    // Configuration
    this.options = {
      autoInit: options.autoInit !== false,
      debug: options.debug || false,
      ...options
    };

    // Initialize if auto-init is enabled
    if (this.options.autoInit) {
      this.init();
    }
  }

  /**
   * Initialize all core modules
   */
  async init() {
    if (this.isInitialized) {
      this.log('StarCrypt Core already initialized');
      return this.initializationPromise;
    }

    if (this.isInitializing) {
      this.log('StarCrypt Core initialization in progress');
      return this.initializationPromise;
    }

    this.isInitializing = true;
    this.log('Initializing StarCrypt Core...');

    this.initializationPromise = this.performInitialization();
    
    try {
      await this.initializationPromise;
      this.isInitialized = true;
      this.isInitializing = false;
      this.log('StarCrypt Core initialized successfully');
      this.emit('initialized');
    } catch (error) {
      this.isInitializing = false;
      this.error('StarCrypt Core initialization failed:', error);
      this.emit('initializationFailed', { error });
      throw error;
    }

    return this.initializationPromise;
  }

  /**
   * Perform the actual initialization
   */
  async performInitialization() {
    try {
      // Step 1: Initialize Configuration Core
      this.log('Step 1: Initializing Configuration Core...');
      await this.initializeConfigCore();

      // Step 2: Initialize WebSocket Core
      this.log('Step 2: Initializing WebSocket Core...');
      await this.initializeWebSocketCore();

      // Step 3: Initialize Strategy Core
      this.log('Step 3: Initializing Strategy Core...');
      await this.initializeStrategyCore();

      // Step 4: Initialize Signal Core
      this.log('Step 4: Initializing Signal Core...');
      await this.initializeSignalCore();

      // Step 5: Set up inter-module communication
      this.log('Step 5: Setting up inter-module communication...');
      this.setupInterModuleCommunication();

      // Step 6: Initialize UI components
      this.log('Step 6: Initializing UI components...');
      await this.initializeUIComponents();

      // Step 7: Final setup
      this.log('Step 7: Final setup...');
      this.finalSetup();

      this.log('All initialization steps completed successfully');

    } catch (error) {
      this.error('Initialization step failed:', error);
      throw error;
    }
  }

  /**
   * Initialize Configuration Core
   */
  async initializeConfigCore() {
    if (typeof window !== 'undefined' && window.getConfigCore) {
      this.modules.config = window.getConfigCore(this.options.config);
    } else {
      const { getConfigCore } = await import('./ConfigCore.js');
      this.modules.config = getConfigCore(this.options.config);
    }

    // Wait for configuration to be ready
    await new Promise(resolve => {
      if (this.modules.config.get('app.name')) {
        resolve();
      } else {
        this.modules.config.on('configChanged', resolve);
      }
    });

    this.log('Configuration Core initialized');
  }

  /**
   * Initialize WebSocket Core
   */
  async initializeWebSocketCore() {
    const wsConfig = this.modules.config.getSection('websocket');
    
    if (typeof window !== 'undefined' && window.getWebSocketCore) {
      this.modules.websocket = window.getWebSocketCore(wsConfig);
    } else {
      const { getWebSocketCore } = await import('./WebSocketCore.js');
      this.modules.websocket = getWebSocketCore(wsConfig);
    }

    // Wait for WebSocket to be ready (don't require connection)
    await new Promise(resolve => {
      // WebSocket core is ready immediately, connection is async
      resolve();
    });

    this.log('WebSocket Core initialized');
  }

  /**
   * Initialize Strategy Core
   */
  async initializeStrategyCore() {
    const strategyConfig = this.modules.config.getSection('strategy');
    
    if (typeof window !== 'undefined' && window.getStrategyCore) {
      this.modules.strategy = window.getStrategyCore(strategyConfig);
    } else {
      const { getStrategyCore } = await import('./StrategyCore.js');
      this.modules.strategy = getStrategyCore(strategyConfig);
    }

    // Initialize strategy core
    this.modules.strategy.init();

    this.log('Strategy Core initialized');
  }

  /**
   * Initialize Signal Core
   */
  async initializeSignalCore() {
    const signalConfig = this.modules.config.getSection('signals');
    
    if (typeof window !== 'undefined' && window.getSignalCore) {
      this.modules.signal = window.getSignalCore(signalConfig);
    } else {
      const { getSignalCore } = await import('./SignalCore.js');
      this.modules.signal = getSignalCore(signalConfig);
    }

    // Signal core initializes automatically
    this.log('Signal Core initialized');
  }

  /**
   * Set up communication between modules
   */
  setupInterModuleCommunication() {
    // WebSocket -> Signal communication
    this.modules.websocket.on('message:signals', (data) => {
      this.modules.signal.handleSignalMessage(data);
    });

    this.modules.websocket.on('message:indicators', (data) => {
      this.modules.signal.handleIndicatorMessage(data);
    });

    // Strategy -> Signal communication
    this.modules.strategy.on('strategyChanged', (data) => {
      this.modules.signal.handleStrategyChange(data);
    });

    // Configuration -> All modules communication
    this.modules.config.on('configChanged', (event) => {
      this.handleConfigChange(event);
    });

    // Signal -> WebSocket communication (for strategy updates)
    this.modules.signal.on('strategyUpdateNeeded', (data) => {
      this.modules.websocket.send({
        type: 'strategy_update',
        ...data
      });
    });

    this.log('Inter-module communication set up');
  }

  /**
   * Handle configuration changes
   */
  handleConfigChange(event) {
    const { path, value } = event;

    // Update WebSocket configuration
    if (path.startsWith('websocket.')) {
      const wsConfig = this.modules.config.getSection('websocket');
      this.modules.websocket.updateConfig(wsConfig);
    }

    // Update Strategy configuration
    if (path.startsWith('strategy.')) {
      const strategyConfig = this.modules.config.getSection('strategy');
      this.modules.strategy.updateConfig(strategyConfig);
    }

    // Update Signal configuration
    if (path.startsWith('signals.')) {
      const signalConfig = this.modules.config.getSection('signals');
      this.modules.signal.updateConfig(signalConfig);
    }

    // Handle debug mode changes
    if (path === 'app.debug') {
      this.setDebugMode(value);
    }
  }

  /**
   * Initialize UI components
   */
  async initializeUIComponents() {
    // Initialize theme
    const theme = this.modules.config.get('ui.theme');
    if (typeof window !== 'undefined' && window.ThemeManager) {
      window.ThemeManager.init(theme);
    }

    // Initialize other UI components
    const uiComponents = [
      'SignalLights',
      'TimeframeUI', 
      'VolumeIndicatorUpdater',
      'SignalMatrix'
    ];

    for (const component of uiComponents) {
      if (typeof window !== 'undefined' && window[component]) {
        try {
          if (typeof window[component].init === 'function') {
            window[component].init();
          }
        } catch (error) {
          this.warn(`Failed to initialize UI component ${component}:`, error);
        }
      }
    }

    this.log('UI components initialized');
  }

  /**
   * Final setup tasks
   */
  finalSetup() {
    // Set up global error handling
    this.setupGlobalErrorHandling();

    // Set up performance monitoring
    this.setupPerformanceMonitoring();

    // Set up cleanup on page unload
    this.setupCleanup();

    // Expose core to global scope for debugging
    if (typeof window !== 'undefined') {
      window.starCryptCore = this;
    }

    this.log('Final setup completed');
  }

  /**
   * Set up global error handling
   */
  setupGlobalErrorHandling() {
    if (typeof window !== 'undefined') {
      window.addEventListener('error', (event) => {
        this.error('Global error:', event.error);
        this.emit('globalError', { error: event.error, event });
      });

      window.addEventListener('unhandledrejection', (event) => {
        this.error('Unhandled promise rejection:', event.reason);
        this.emit('unhandledRejection', { reason: event.reason, event });
      });
    }
  }

  /**
   * Set up performance monitoring
   */
  setupPerformanceMonitoring() {
    const maxMemory = this.modules.config.get('performance.maxMemoryUsage', 512);
    
    setInterval(() => {
      if (typeof window !== 'undefined' && window.performance && window.performance.memory) {
        const memoryUsage = window.performance.memory.usedJSHeapSize / 1024 / 1024; // MB
        
        if (memoryUsage > maxMemory) {
          this.warn(`Memory usage high: ${memoryUsage.toFixed(2)}MB (max: ${maxMemory}MB)`);
          this.emit('highMemoryUsage', { usage: memoryUsage, max: maxMemory });
        }
      }
    }, 30000); // Check every 30 seconds
  }

  /**
   * Set up cleanup on page unload
   */
  setupCleanup() {
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.cleanup();
      });
    }
  }

  /**
   * Set debug mode for all modules
   */
  setDebugMode(enabled) {
    this.options.debug = enabled;
    
    // Update all modules
    for (const module of Object.values(this.modules)) {
      if (module && typeof module.updateConfig === 'function') {
        module.updateConfig({ debug: enabled });
      }
    }

    this.log(`Debug mode ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get module by name
   */
  getModule(name) {
    return this.modules[name];
  }

  /**
   * Get all modules
   */
  getModules() {
    return { ...this.modules };
  }

  /**
   * Check if core is ready
   */
  isReady() {
    return this.isInitialized && !this.isInitializing;
  }

  /**
   * Wait for core to be ready
   */
  async waitForReady() {
    if (this.isReady()) {
      return;
    }

    if (this.initializationPromise) {
      await this.initializationPromise;
    } else {
      await this.init();
    }
  }

  /**
   * Get core statistics
   */
  getStats() {
    const stats = {
      isInitialized: this.isInitialized,
      isInitializing: this.isInitializing,
      modules: {}
    };

    // Get stats from each module
    for (const [name, module] of Object.entries(this.modules)) {
      if (module && typeof module.getStats === 'function') {
        stats.modules[name] = module.getStats();
      } else {
        stats.modules[name] = { available: !!module };
      }
    }

    return stats;
  }

  /**
   * Event system methods
   */
  on(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(listener);
  }

  off(event, listener) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      for (const listener of listeners) {
        try {
          listener(data);
        } catch (error) {
          this.error(`Event listener error for ${event}:`, error);
        }
      }
    }

    // Also dispatch DOM event
    if (typeof document !== 'undefined') {
      try {
        const customEvent = new CustomEvent(`starcrypt:core:${event}`, { detail: data });
        document.dispatchEvent(customEvent);
      } catch (error) {
        this.error(`Error dispatching DOM event ${event}:`, error);
      }
    }
  }

  /**
   * Cleanup resources
   */
  cleanup() {
    this.log('Cleaning up StarCrypt Core...');

    // Cleanup all modules
    for (const module of Object.values(this.modules)) {
      if (module && typeof module.destroy === 'function') {
        try {
          module.destroy();
        } catch (error) {
          this.error('Error during module cleanup:', error);
        }
      }
    }

    // Clear event listeners
    this.eventListeners.clear();

    this.log('StarCrypt Core cleanup completed');
  }

  /**
   * Utility methods
   */
  log(...args) {
    if (this.options.debug) {
      console.log('[StarCrypt Core]', ...args);
    }
  }

  warn(...args) {
    console.warn('[StarCrypt Core]', ...args);
  }

  error(...args) {
    console.error('[StarCrypt Core]', ...args);
  }
}

// Create singleton instance
let starCryptCoreInstance = null;

/**
 * Get or create StarCrypt core singleton
 */
function getStarCryptCore(options = {}) {
  if (!starCryptCoreInstance) {
    starCryptCoreInstance = new StarCryptCore(options);
  }
  return starCryptCoreInstance;
}

// Export for both CommonJS and ES6
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { StarCryptCore, getStarCryptCore };
} else if (typeof window !== 'undefined') {
  window.StarCryptCore = StarCryptCore;
  window.getStarCryptCore = getStarCryptCore;
  
  // Initialize global instance
  window.starCryptCore = getStarCryptCore();
}
