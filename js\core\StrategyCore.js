/**
 * StarCrypt Strategy Core - Unified Strategy Management System
 * Consolidates all strategy functionality into a single, robust system
 */

class StarCryptStrategyCore {
  constructor(options = {}) {
    // Configuration
    this.config = {
      defaultStrategy: options.defaultStrategy || 'admiral_toa',
      autoSave: options.autoSave !== false,
      debug: options.debug || false,
      ...options
    };

    // State management
    this.currentStrategy = null;
    this.previousStrategy = null;
    this.strategies = new Map();
    this.isInitialized = false;
    this.isChanging = false;

    // Event system
    this.eventListeners = new Map();
    this.changeCallbacks = new Set();

    // Strategy definitions
    this.initializeStrategies();
  }

  /**
   * Initialize strategy definitions
   */
  initializeStrategies() {
    // Define all available strategies
    const strategyDefinitions = {
      admiral_toa: {
        id: 'admiral_toa',
        name: 'Admiral T.O.A.',
        description: 'AI-powered trading signals with advanced market analysis',
        category: 'ai',
        indicators: ['rsi', 'macd', 'bb', 'ema', 'volume', 'sentiment'],
        thresholds: {
          rsi: { green: 70, blue: 60, orange: 40, red: 30 },
          macd: { green: 0.5, blue: 0.2, orange: -0.2, red: -0.5 },
          bb: { green: 0.8, blue: 0.6, orange: 0.4, red: 0.2 },
          ema: { green: 2, blue: 1, orange: -1, red: -2 },
          volume: { green: 1.5, blue: 1.2, orange: 0.8, red: 0.5 },
          sentiment: { green: 0.6, blue: 0.3, orange: -0.3, red: -0.6 }
        },
        settings: {
          riskLevel: 'medium',
          timeframe: 'multi',
          signalStrength: 'high'
        }
      },
      momentum_blast: {
        id: 'momentum_blast',
        name: 'Momentum Blast',
        description: 'High-momentum trading with rapid signal detection',
        category: 'momentum',
        indicators: ['rsi', 'macd', 'volume', 'momentum'],
        thresholds: {
          rsi: { green: 80, blue: 70, orange: 30, red: 20 },
          macd: { green: 1, blue: 0.5, orange: -0.5, red: -1 },
          volume: { green: 2, blue: 1.5, orange: 0.7, red: 0.3 },
          momentum: { green: 5, blue: 3, orange: -3, red: -5 }
        },
        settings: {
          riskLevel: 'high',
          timeframe: 'short',
          signalStrength: 'very_high'
        }
      },
      tight_convergence: {
        id: 'tight_convergence',
        name: 'Tight Convergence',
        description: 'Precise entry points with tight convergence analysis',
        category: 'precision',
        indicators: ['rsi', 'macd', 'bb', 'ema', 'stoch'],
        thresholds: {
          rsi: { green: 65, blue: 55, orange: 45, red: 35 },
          macd: { green: 0.3, blue: 0.1, orange: -0.1, red: -0.3 },
          bb: { green: 0.9, blue: 0.7, orange: 0.3, red: 0.1 },
          ema: { green: 1, blue: 0.5, orange: -0.5, red: -1 },
          stoch: { green: 80, blue: 70, orange: 30, red: 20 }
        },
        settings: {
          riskLevel: 'low',
          timeframe: 'medium',
          signalStrength: 'medium'
        }
      },
      top_bottom_feeder: {
        id: 'top_bottom_feeder',
        name: 'Top Bottom Feeder',
        description: 'Reversal detection at market extremes',
        category: 'reversal',
        indicators: ['rsi', 'bb', 'stoch', 'williams_r', 'cci'],
        thresholds: {
          rsi: { green: 30, blue: 40, orange: 60, red: 70 },
          bb: { green: 0.1, blue: 0.3, orange: 0.7, red: 0.9 },
          stoch: { green: 20, blue: 30, orange: 70, red: 80 },
          williams_r: { green: -80, blue: -70, orange: -30, red: -20 },
          cci: { green: -100, blue: -50, orange: 50, red: 100 }
        },
        settings: {
          riskLevel: 'medium',
          timeframe: 'long',
          signalStrength: 'high'
        }
      },
      scalping_sniper: {
        id: 'scalping_sniper',
        name: 'Scalping Sniper',
        description: 'Ultra-fast scalping with precision timing',
        category: 'scalping',
        indicators: ['ema', 'volume', 'price_action', 'tick_volume'],
        thresholds: {
          ema: { green: 0.5, blue: 0.2, orange: -0.2, red: -0.5 },
          volume: { green: 3, blue: 2, orange: 1, red: 0.5 },
          price_action: { green: 2, blue: 1, orange: -1, red: -2 },
          tick_volume: { green: 1.5, blue: 1.2, orange: 0.8, red: 0.5 }
        },
        settings: {
          riskLevel: 'very_high',
          timeframe: 'very_short',
          signalStrength: 'extreme'
        }
      },
      trend_rider: {
        id: 'trend_rider',
        name: 'Trend Rider',
        description: 'Long-term trend following strategy',
        category: 'trend',
        indicators: ['ema', 'sma', 'adx', 'parabolic_sar', 'ichimoku'],
        thresholds: {
          ema: { green: 3, blue: 2, orange: -2, red: -3 },
          sma: { green: 2, blue: 1, orange: -1, red: -2 },
          adx: { green: 40, blue: 30, orange: 20, red: 15 },
          parabolic_sar: { green: 2, blue: 1, orange: -1, red: -2 },
          ichimoku: { green: 1, blue: 0.5, orange: -0.5, red: -1 }
        },
        settings: {
          riskLevel: 'low',
          timeframe: 'long',
          signalStrength: 'medium'
        }
      },
      fractal_surge: {
        id: 'fractal_surge',
        name: 'Fractal Surge',
        description: 'Fractal-based momentum detection',
        category: 'advanced',
        indicators: ['fractals', 'momentum', 'volume', 'rsi'],
        thresholds: {
          fractals: { green: 3, blue: 2, orange: -2, red: -3 },
          momentum: { green: 4, blue: 2, orange: -2, red: -4 },
          volume: { green: 1.8, blue: 1.3, orange: 0.7, red: 0.4 },
          rsi: { green: 75, blue: 65, orange: 35, red: 25 }
        },
        settings: {
          riskLevel: 'high',
          timeframe: 'medium',
          signalStrength: 'high'
        }
      },
      x_sentiment_blaster: {
        id: 'x_sentiment_blaster',
        name: 'X Sentiment Blaster',
        description: 'Social sentiment-driven trading signals',
        category: 'sentiment',
        indicators: ['sentiment', 'social_volume', 'news_impact', 'fear_greed'],
        thresholds: {
          sentiment: { green: 0.8, blue: 0.4, orange: -0.4, red: -0.8 },
          social_volume: { green: 2, blue: 1.5, orange: 0.8, red: 0.3 },
          news_impact: { green: 0.7, blue: 0.3, orange: -0.3, red: -0.7 },
          fear_greed: { green: 80, blue: 60, orange: 40, red: 20 }
        },
        settings: {
          riskLevel: 'medium',
          timeframe: 'short',
          signalStrength: 'high'
        }
      },
      quantum_entropy: {
        id: 'quantum_entropy',
        name: 'Quantum Entropy',
        description: 'Advanced mathematical modeling for market prediction',
        category: 'advanced',
        indicators: ['entropy', 'chaos', 'complexity', 'correlation'],
        thresholds: {
          entropy: { green: 0.8, blue: 0.6, orange: 0.4, red: 0.2 },
          chaos: { green: 0.3, blue: 0.5, orange: 0.7, red: 0.9 },
          complexity: { green: 0.7, blue: 0.5, orange: 0.3, red: 0.1 },
          correlation: { green: 0.8, blue: 0.6, orange: 0.4, red: 0.2 }
        },
        settings: {
          riskLevel: 'very_high',
          timeframe: 'variable',
          signalStrength: 'extreme'
        }
      },
      cross_asset_nebula: {
        id: 'cross_asset_nebula',
        name: 'Cross-Asset Nebula',
        description: 'Multi-asset correlation and arbitrage detection',
        category: 'arbitrage',
        indicators: ['correlation', 'spread', 'volatility', 'beta'],
        thresholds: {
          correlation: { green: 0.9, blue: 0.7, orange: 0.3, red: 0.1 },
          spread: { green: 2, blue: 1, orange: -1, red: -2 },
          volatility: { green: 0.3, blue: 0.2, orange: 0.1, red: 0.05 },
          beta: { green: 1.5, blue: 1.2, orange: 0.8, red: 0.5 }
        },
        settings: {
          riskLevel: 'medium',
          timeframe: 'multi',
          signalStrength: 'medium'
        }
      },
      time_warp_scalper: {
        id: 'time_warp_scalper',
        name: 'Time Warp Scalper',
        description: 'Time-based pattern recognition for ultra-fast trades',
        category: 'scalping',
        indicators: ['time_patterns', 'micro_trends', 'tick_analysis', 'order_flow'],
        thresholds: {
          time_patterns: { green: 0.8, blue: 0.6, orange: 0.4, red: 0.2 },
          micro_trends: { green: 3, blue: 2, orange: -2, red: -3 },
          tick_analysis: { green: 2, blue: 1, orange: -1, red: -2 },
          order_flow: { green: 1.5, blue: 1, orange: -1, red: -1.5 }
        },
        settings: {
          riskLevel: 'extreme',
          timeframe: 'micro',
          signalStrength: 'extreme'
        }
      }
    };

    // Load strategies into map
    for (const [id, strategy] of Object.entries(strategyDefinitions)) {
      this.strategies.set(id, strategy);
    }

    this.log('Strategies initialized:', this.strategies.size);
  }

  /**
   * Initialize the strategy core
   */
  init() {
    if (this.isInitialized) {
      this.log('Strategy core already initialized');
      return;
    }

    this.log('Initializing Strategy Core...');

    // Load saved strategy or use default
    const savedStrategy = this.config.autoSave ? 
      localStorage.getItem('starcrypt_current_strategy') : null;
    
    const initialStrategy = savedStrategy || this.config.defaultStrategy;

    // Validate strategy exists
    if (!this.strategies.has(initialStrategy)) {
      this.warn(`Strategy ${initialStrategy} not found, using default`);
      this.currentStrategy = this.config.defaultStrategy;
    } else {
      this.currentStrategy = initialStrategy;
    }

    // Set up event listeners
    this.setupEventListeners();

    // Apply initial strategy
    this.applyStrategy(this.currentStrategy, { source: 'init', silent: true });

    this.isInitialized = true;
    this.log('Strategy Core initialized with strategy:', this.currentStrategy);

    // Emit initialization event
    this.emit('initialized', { strategy: this.currentStrategy });
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Listen for strategy change events from UI
    if (typeof document !== 'undefined') {
      document.addEventListener('starcrypt:strategy:change', (event) => {
        const { strategyId, source } = event.detail;
        this.changeStrategy(strategyId, { source: source || 'ui' });
      });

      document.addEventListener('starcrypt:strategy:reload', () => {
        this.reloadCurrentStrategy();
      });
    }
  }

  /**
   * Get all available strategies
   */
  getStrategies() {
    return Array.from(this.strategies.values());
  }

  /**
   * Get strategy by ID
   */
  getStrategy(strategyId) {
    return this.strategies.get(strategyId);
  }

  /**
   * Get current strategy
   */
  getCurrentStrategy() {
    return this.getStrategy(this.currentStrategy);
  }

  /**
   * Change strategy
   */
  changeStrategy(strategyId, options = {}) {
    if (!strategyId || !this.strategies.has(strategyId)) {
      this.error(`Invalid strategy: ${strategyId}`);
      return false;
    }

    if (strategyId === this.currentStrategy && !options.force) {
      this.log(`Strategy ${strategyId} is already active`);
      return false;
    }

    if (this.isChanging && !options.force) {
      this.warn('Strategy change already in progress');
      return false;
    }

    return this.applyStrategy(strategyId, options);
  }

  /**
   * Apply a strategy
   */
  applyStrategy(strategyId, options = {}) {
    if (this.isChanging && !options.force) {
      this.warn('Strategy change already in progress');
      return false;
    }

    this.isChanging = true;

    try {
      this.log(`Applying strategy: ${strategyId}`, options);

      const strategy = this.strategies.get(strategyId);
      if (!strategy) {
        throw new Error(`Strategy not found: ${strategyId}`);
      }

      // Save previous strategy
      this.previousStrategy = this.currentStrategy;
      this.currentStrategy = strategyId;

      // Save to localStorage if auto-save enabled
      if (this.config.autoSave) {
        localStorage.setItem('starcrypt_current_strategy', strategyId);
      }

      // Emit strategy change events
      if (!options.silent) {
        this.emit('strategyChanging', {
          from: this.previousStrategy,
          to: strategyId,
          strategy,
          options
        });
      }

      // Update UI components
      this.updateUI(strategy, options);

      // Update indicators and thresholds
      this.updateIndicators(strategy, options);

      // Notify change callbacks
      for (const callback of this.changeCallbacks) {
        try {
          callback(strategy, this.previousStrategy, options);
        } catch (error) {
          this.error('Strategy change callback error:', error);
        }
      }

      // Emit strategy changed event
      if (!options.silent) {
        this.emit('strategyChanged', {
          from: this.previousStrategy,
          to: strategyId,
          strategy,
          options
        });
      }

      this.log(`Strategy applied successfully: ${strategyId}`);
      return true;

    } catch (error) {
      this.error('Error applying strategy:', error);
      return false;
    } finally {
      this.isChanging = false;
    }
  }

  /**
   * Update UI components for strategy change
   */
  updateUI(strategy, options = {}) {
    try {
      // Update strategy selector dropdown
      this.updateStrategySelector(strategy);

      // Update strategy description
      this.updateStrategyDescription(strategy);

      // Update strategy helper text
      this.updateStrategyHelper(strategy);

      // Update visual indicators
      this.updateVisualIndicators(strategy);

    } catch (error) {
      this.error('Error updating UI:', error);
    }
  }

  /**
   * Update strategy selector dropdown
   */
  updateStrategySelector(strategy) {
    const selector = document.getElementById('strategy-selector');
    if (selector) {
      selector.value = strategy.id;

      // Update selected option styling
      const options = selector.querySelectorAll('option');
      options.forEach(option => {
        option.classList.toggle('selected', option.value === strategy.id);
      });
    }
  }

  /**
   * Update strategy description
   */
  updateStrategyDescription(strategy) {
    const descElement = document.getElementById('strategy-description');
    if (descElement) {
      descElement.textContent = strategy.description;
      descElement.setAttribute('data-strategy', strategy.id);
    }
  }

  /**
   * Update strategy helper text
   */
  updateStrategyHelper(strategy) {
    const helperElement = document.getElementById('strategy-helper');
    if (helperElement) {
      const helperText = this.generateHelperText(strategy);
      helperElement.innerHTML = helperText;
    }
  }

  /**
   * Generate helper text for strategy
   */
  generateHelperText(strategy) {
    const { settings } = strategy;
    return `
      <div class="strategy-helper-content">
        <div class="helper-item">
          <span class="helper-label">Risk Level:</span>
          <span class="helper-value risk-${settings.riskLevel}">${settings.riskLevel.toUpperCase()}</span>
        </div>
        <div class="helper-item">
          <span class="helper-label">Timeframe:</span>
          <span class="helper-value">${settings.timeframe.toUpperCase()}</span>
        </div>
        <div class="helper-item">
          <span class="helper-label">Signal Strength:</span>
          <span class="helper-value">${settings.signalStrength.replace('_', ' ').toUpperCase()}</span>
        </div>
        <div class="helper-item">
          <span class="helper-label">Category:</span>
          <span class="helper-value">${strategy.category.toUpperCase()}</span>
        </div>
      </div>
    `;
  }

  /**
   * Update visual indicators
   */
  updateVisualIndicators(strategy) {
    // Update strategy indicator in header
    const indicator = document.querySelector('.current-strategy-indicator');
    if (indicator) {
      indicator.textContent = strategy.name;
      indicator.className = `current-strategy-indicator strategy-${strategy.category}`;
    }

    // Update body class for strategy-specific styling
    if (typeof document !== 'undefined') {
      document.body.className = document.body.className
        .replace(/strategy-\w+/g, '')
        .concat(` strategy-${strategy.id}`);
    }
  }

  /**
   * Update indicators and thresholds for strategy
   */
  updateIndicators(strategy, options = {}) {
    try {
      // Update threshold sliders
      this.updateThresholdSliders(strategy);

      // Update indicator visibility
      this.updateIndicatorVisibility(strategy);

      // Update signal processing
      this.updateSignalProcessing(strategy);

      // Notify indicator update
      this.emit('indicatorsUpdated', { strategy, options });

    } catch (error) {
      this.error('Error updating indicators:', error);
    }
  }

  /**
   * Update threshold sliders
   */
  updateThresholdSliders(strategy) {
    const { thresholds } = strategy;

    for (const [indicator, values] of Object.entries(thresholds)) {
      const slider = document.getElementById(`threshold-${indicator}`);
      if (slider) {
        // Update slider values
        this.updateSliderThresholds(slider, values);
      }
    }

    // Hide sliders for indicators not in this strategy
    this.hideUnusedSliders(strategy);
  }

  /**
   * Update slider thresholds
   */
  updateSliderThresholds(slider, thresholds) {
    const { green, blue, orange, red } = thresholds;

    // Update slider thumb positions
    const thumbs = slider.querySelectorAll('.threshold-thumb');
    if (thumbs.length >= 4) {
      thumbs[0].style.left = `${this.valueToPercent(red, slider)}%`;
      thumbs[1].style.left = `${this.valueToPercent(orange, slider)}%`;
      thumbs[2].style.left = `${this.valueToPercent(blue, slider)}%`;
      thumbs[3].style.left = `${this.valueToPercent(green, slider)}%`;
    }

    // Update color segments
    this.updateSliderColors(slider, thresholds);
  }

  /**
   * Convert value to percentage for slider
   */
  valueToPercent(value, slider) {
    const min = parseFloat(slider.getAttribute('data-min') || '0');
    const max = parseFloat(slider.getAttribute('data-max') || '100');
    return ((value - min) / (max - min)) * 100;
  }

  /**
   * Update slider color segments
   */
  updateSliderColors(slider, thresholds) {
    const track = slider.querySelector('.threshold-track');
    if (track) {
      const { red, orange, blue, green } = thresholds;
      const redPercent = this.valueToPercent(red, slider);
      const orangePercent = this.valueToPercent(orange, slider);
      const bluePercent = this.valueToPercent(blue, slider);
      const greenPercent = this.valueToPercent(green, slider);

      track.style.background = `linear-gradient(to right,
        #ff4444 0%,
        #ff4444 ${redPercent}%,
        #ff8800 ${redPercent}%,
        #ff8800 ${orangePercent}%,
        #888888 ${orangePercent}%,
        #888888 ${bluePercent}%,
        #4488ff ${bluePercent}%,
        #4488ff ${greenPercent}%,
        #44ff44 ${greenPercent}%,
        #44ff44 100%
      )`;
    }
  }

  /**
   * Hide sliders for unused indicators
   */
  hideUnusedSliders(strategy) {
    const allSliders = document.querySelectorAll('[id^="threshold-"]');
    allSliders.forEach(slider => {
      const indicator = slider.id.replace('threshold-', '');
      const isUsed = strategy.indicators.includes(indicator);
      slider.style.display = isUsed ? 'block' : 'none';

      // Also hide the container if it exists
      const container = slider.closest('.threshold-container');
      if (container) {
        container.style.display = isUsed ? 'block' : 'none';
      }
    });
  }

  /**
   * Update indicator visibility
   */
  updateIndicatorVisibility(strategy) {
    const indicatorRows = document.querySelectorAll('.indicator-row');
    indicatorRows.forEach(row => {
      const indicator = row.getAttribute('data-indicator');
      const isVisible = strategy.indicators.includes(indicator);
      row.style.display = isVisible ? 'table-row' : 'none';
    });
  }

  /**
   * Update signal processing
   */
  updateSignalProcessing(strategy) {
    // Notify signal system of strategy change
    if (typeof window !== 'undefined' && window.wsCore) {
      window.wsCore.send({
        type: 'strategy_change',
        strategy: strategy.id,
        indicators: strategy.indicators,
        thresholds: strategy.thresholds
      });
    }

    // Update local signal processing
    if (typeof window !== 'undefined' && window.signalProcessor) {
      window.signalProcessor.updateStrategy(strategy);
    }
  }

  /**
   * Reload current strategy
   */
  reloadCurrentStrategy() {
    if (this.currentStrategy) {
      this.applyStrategy(this.currentStrategy, { force: true, source: 'reload' });
    }
  }

  /**
   * Add strategy change callback
   */
  onStrategyChange(callback) {
    if (typeof callback === 'function') {
      this.changeCallbacks.add(callback);
    }
  }

  /**
   * Remove strategy change callback
   */
  offStrategyChange(callback) {
    this.changeCallbacks.delete(callback);
  }

  /**
   * Event system methods
   */
  on(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(listener);
  }

  off(event, listener) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      for (const listener of listeners) {
        try {
          listener(data);
        } catch (error) {
          this.error(`Event listener error for ${event}:`, error);
        }
      }
    }

    // Also dispatch DOM event
    if (typeof document !== 'undefined') {
      try {
        const customEvent = new CustomEvent(`starcrypt:strategy:${event}`, { detail: data });
        document.dispatchEvent(customEvent);
      } catch (error) {
        this.error(`Error dispatching DOM event ${event}:`, error);
      }
    }
  }

  /**
   * Utility methods
   */
  log(...args) {
    if (this.config.debug) {
      console.log('[StarCrypt Strategy]', ...args);
    }
  }

  warn(...args) {
    console.warn('[StarCrypt Strategy]', ...args);
  }

  error(...args) {
    console.error('[StarCrypt Strategy]', ...args);
  }

  /**
   * Get strategy statistics
   */
  getStats() {
    return {
      currentStrategy: this.currentStrategy,
      previousStrategy: this.previousStrategy,
      totalStrategies: this.strategies.size,
      isInitialized: this.isInitialized,
      isChanging: this.isChanging,
      availableStrategies: Array.from(this.strategies.keys()),
      config: { ...this.config }
    };
  }
}

// Create singleton instance
let strategyCoreInstance = null;

/**
 * Get or create strategy core singleton
 */
function getStrategyCore(options = {}) {
  if (!strategyCoreInstance) {
    strategyCoreInstance = new StarCryptStrategyCore(options);
  }
  return strategyCoreInstance;
}

// Export for both CommonJS and ES6
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { StarCryptStrategyCore, getStrategyCore };
} else if (typeof window !== 'undefined') {
  window.StarCryptStrategyCore = StarCryptStrategyCore;
  window.getStrategyCore = getStrategyCore;

  // Initialize global instance
  window.strategyCore = getStrategyCore();
}
