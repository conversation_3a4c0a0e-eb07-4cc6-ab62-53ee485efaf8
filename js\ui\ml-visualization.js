// ML Visualization Controls
class MLVisualization {
  constructor() {
    this.zoomLevel = 1.0;
    this.isVisible = true;
    this.lastPredictions = {};
    this.isDetailsVisible = false;
    
    // Initialize UI components
    this.initializeUI();
    this.setupEventListeners();
    this.setupPredictionUpdates();
    
    // Initialize control buttons
    this.setupControlButtons();
  }
  
  setupControlButtons() {
    // Control Center button
    const controlCenterBtn = document.createElement('button');
    controlCenterBtn.className = 'ml-control-btn interactive-element';
    controlCenterBtn.style.background = '#1e88e5';
    controlCenterBtn.innerHTML = '<i class="fas fa-cog"></i> Control Center';
    controlCenterBtn.onclick = () => this.toggleControlCenter();
    
    // Analysis button
    const analysisBtn = document.createElement('button');
    analysisBtn.className = 'ml-control-btn interactive-element';
    analysisBtn.style.background = '#ff9800';
    analysisBtn.innerHTML = '<i class="fas fa-chart-line"></i> Analysis';
    analysisBtn.onclick = () => this.toggleAnalysisPanel();
    
    // Add buttons to controls container
    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'ml-control-group';
    controlsContainer.style.marginLeft = 'auto';
    controlsContainer.style.display = 'flex';
    controlsContainer.style.gap = '10px';
    controlsContainer.appendChild(controlCenterBtn);
    controlsContainer.appendChild(analysisBtn);
    
    // Add to header
    const header = document.querySelector('.ml-visualization-header .ml-visualization-controls');
    if (header) {
      header.appendChild(controlsContainer);
    }
  }
  
  toggleControlCenter() {
    // Toggle control center panel
    console.log('Control Center toggled');
    // Implementation for control center toggle
  }
  
  toggleAnalysisPanel() {
    // Toggle analysis panel
    console.log('Analysis panel toggled');
    // Implementation for analysis panel toggle
  }

  initializeUI() {
    // Create ML controls container if it doesn't exist
    if (!document.getElementById('ml-controls')) {
      const controls = document.createElement('div');
      controls.id = 'ml-controls';
      controls.className = 'ml-controls';
      controls.innerHTML = `
        <div class="ml-control-group">
          <button id="ml-zoom-in" class="ml-control-btn" title="Zoom In">+</button>
          <button id="ml-zoom-out" class="ml-control-btn" title="Zoom Out">-</button>
          <button id="ml-reset-zoom" class="ml-control-btn" title="Reset Zoom">⟲</button>
          <button id="ml-toggle-visibility" class="ml-control-btn" title="Toggle Visibility">👁️</button>
        </div>
      `;
      document.querySelector('.cosmic-indicators').appendChild(controls);
    }
  }

  setupEventListeners() {
    // Zoom controls
    document.getElementById('ml-zoom-in')?.addEventListener('click', () => this.adjustZoom(0.1));
    document.getElementById('ml-zoom-out')?.addEventListener('click', () => this.adjustZoom(-0.1));
    document.getElementById('ml-reset-zoom')?.addEventListener('click', () => this.resetZoom());
    
    // Toggle visibility
    document.getElementById('ml-toggle-visibility')?.addEventListener('click', () => this.toggleVisibility());
    
    // Handle ML data updates
    document.addEventListener('mlDataUpdated', (e) => this.handleMLUpdate(e.detail));
  }

  adjustZoom(delta) {
    this.zoomLevel = Math.max(0.5, Math.min(2.0, this.zoomLevel + delta));
    this.applyZoom();
  }

  resetZoom() {
    this.zoomLevel = 1.0;
    this.applyZoom();
  }

  applyZoom() {
    const container = document.querySelector('.ml-visualization-container');
    if (container) {
      container.style.transform = `scale(${this.zoomLevel})`;
      container.style.transformOrigin = 'top left';
    }
  }

  toggleVisibility() {
    this.isVisible = !this.isVisible;
    const container = document.querySelector('.ml-visualization-container');
    if (container) {
      container.style.display = this.isVisible ? 'block' : 'none';
    }
  }

  handleMLUpdate(data) {
    try {
      const { indicator, timeframe, prediction } = data;
      if (!indicator || !timeframe || !prediction) return;

      // Store the latest prediction
      this.lastPredictions[timeframe] = this.lastPredictions[timeframe] || {};
      this.lastPredictions[timeframe][indicator] = prediction;

      // Update the UI
      this.updatePredictionUI(timeframe);
      this.updateSummaryView();
      this.updateDetailsView();
    } catch (error) {
      console.error('Error handling ML update:', error);
    }
  }

  updatePredictionUI(timeframe) {
    const container = document.querySelector(`.ml-timeframe-prediction[data-timeframe="${timeframe}"]`);
    if (!container) return;

    const predictions = this.lastPredictions[timeframe];
    if (!predictions) return;

    // Calculate signal strengths using the 5-color system
    const signalStrengths = {
      'strong-buy': 0,
      'mild-buy': 0,
      'neutral': 0,
      'mild-sell': 0,
      'strong-sell': 0
    };
    
    // Track all predictions for tooltip
    const allPredictions = [];
    
    // Process each indicator's prediction
    Object.entries(predictions).forEach(([indicator, pred]) => {
      const signal = this.mapPredictionToSignal(pred.value, indicator);
      const confidence = pred.confidence || 0.5;
      
      // Store the prediction for tooltip
      allPredictions.push({
        indicator,
        value: pred.value,
        confidence,
        signal
      });
      
      // Update signal strengths
      if (signal in signalStrengths) {
        signalStrengths[signal] += confidence;
      } else {
        signalStrengths.neutral += confidence;
      }
    });
    
    // Determine the dominant signal based on weighted confidence
    let dominantSignal = 'neutral';
    let maxStrength = 0;
    
    // Calculate total strength for normalization
    const totalStrength = Object.values(signalStrengths).reduce((sum, val) => sum + val, 0);
    
    // Find the dominant signal
    for (const [signal, strength] of Object.entries(signalStrengths)) {
      const normalizedStrength = totalStrength > 0 ? (strength / totalStrength) : 0;
      if (normalizedStrength > maxStrength) {
        maxStrength = normalizedStrength;
        dominantSignal = signal;
      }
    }
    
    // Calculate average confidence
    const confidences = allPredictions.map(p => p.confidence || 0);
    const avgConfidence = confidences.length > 0 ? 
      confidences.reduce((a, b) => a + b, 0) / confidences.length : 0;
    
    // Format signal text for display
    const signalDisplayMap = {
      'strong-buy': 'STRONG BUY',
      'mild-buy': 'MILD BUY',
      'neutral': 'NEUTRAL',
      'mild-sell': 'MILD SELL',
      'strong-sell': 'STRONG SELL'
    };
    const displaySignal = signalDisplayMap[dominantSignal] || 'NEUTRAL';
    
    // Build detailed tooltip
    let tooltip = `ML Prediction (${timeframe.toUpperCase()})\n`;
    tooltip += `Overall: ${displaySignal} (${Math.round(avgConfidence * 100)}% confidence)\n\n`;
    tooltip += 'Indicator Predictions:\n';
    
    allPredictions.forEach(pred => {
      const signalName = signalDisplayMap[pred.signal] || pred.signal.toUpperCase();
      tooltip += `• ${pred.indicator.toUpperCase()}: ${signalName} (${Math.round(pred.confidence * 100)}%)\n`;
    });
    
    tooltip += `\nLast Updated: ${new Date().toLocaleTimeString()}`;

    // Update UI
    const valueEl = container.querySelector('.ml-prediction-value');
    const confidenceEl = container.querySelector('.ml-confidence');
    
    if (valueEl && confidenceEl) {
      valueEl.textContent = displaySignal;
      valueEl.style.color = this.getSignalColor(dominantSignal);
      confidenceEl.textContent = `${Math.round(avgConfidence * 100)}% confidence`;
      
      // Update container class for styling
      container.className = 'ml-timeframe-prediction';
      container.classList.add(dominantSignal);
      
      // Update tooltip
      container.title = tooltip;
    }
  }

  updateSummaryView() {
    // Update the main summary view with the most important prediction
    const mainTimeframe = '1h'; // Default to 1h for main view
    const mainPrediction = this.lastPredictions[mainTimeframe];
    
    if (!mainPrediction) return;

    const mainContainer = document.querySelector('.ml-prediction-summary');
    if (mainContainer) {
      mainContainer.style.border = `2px solid ${this.getSignalColor(this.getOverallSignal())}`;
      mainContainer.style.transition = 'border-color 0.5s ease';
    }
  }

  updateDetailsView() {
    const detailsContainer = document.querySelector('.ml-prediction-details');
    if (!detailsContainer) return;
    
    // Group indicators by timeframe for better organization
    const timeframes = {};
    Object.entries(this.lastPredictions).forEach(([timeframe, indicators]) => {
      if (!timeframes[timeframe]) {
        timeframes[timeframe] = [];
      }
      
      Object.entries(indicators).forEach(([indicator, prediction]) => {
        timeframes[timeframe].push({
          indicator,
          prediction,
          signal: this.mapPredictionToSignal(prediction.value, indicator)
        });
      });
    });
    
    let html = `
      <div class="ml-details-container">
        <div class="ml-details-header">
          <div>Indicator</div>
          <div>Prediction</div>
          <div>Confidence</div>
        </div>
        <div class="ml-details-rows">
    `;
    
    // Add each indicator's prediction, grouped by timeframe
    Object.entries(timeframes).forEach(([timeframe, indicators]) => {
      // Add timeframe header
      html += `
        <div class="ml-timeframe-header">
          <strong>${timeframe.toUpperCase()}</strong>
        </div>
      `;
      
      // Add indicators for this timeframe
      indicators.forEach(({indicator, prediction, signal}) => {
        const color = this.getSignalColor(signal);
        const confidence = Math.round((prediction.confidence || 0) * 100);
        
        html += `
          <div class="ml-details-row">
            <div class="indicator-name">${indicator}</div>
            <div class="signal-value" style="color: ${color}">
              ${signal.toUpperCase().replace('-', ' ')}
            </div>
            <div class="confidence-value">
              <div class="confidence-bar" style="width: ${confidence}%; background: ${color};"></div>
              <span>${confidence}%</span>
            </div>
          </div>
        `;
      });
    });
    
    html += `
        </div>
      </div>
    `;
    
    detailsContainer.innerHTML = html;
  }

  getOverallSignal() {
    // Calculate overall signal based on all predictions
    let total = 0;
    let count = 0;
    
    Object.values(this.lastPredictions).forEach(indicators => {
      Object.values(indicators).forEach(prediction => {
        total += prediction.value || 0;
        count++;
      });
    });
    
    const avg = count > 0 ? total / count : 0;
    return this.mapPredictionToSignal(avg);
  }

  mapPredictionToSignal(value, indicator) {
    // Match the signal system's 5-color logic
    const bullishHighIndicators = ['rsi', 'stochRsi', 'williamsR', 'ultimateOscillator', 'mfi'];
    const isBullishHigh = bullishHighIndicators.includes(indicator);
    
    // For bullishHigh indicators (like RSI), higher values are more bullish
    if (isBullishHigh) {
      if (value > 80) return 'strong-buy';
      if (value > 60) return 'mild-buy';
      if (value < 20) return 'strong-sell';
      if (value < 40) return 'mild-sell';
      return 'neutral';
    }
    
    // For regular indicators, use the standard thresholds
    if (value >= 0.8) return 'strong-buy';
    if (value >= 0.2) return 'mild-buy';
    if (value <= -0.8) return 'strong-sell';
    if (value <= -0.2) return 'mild-sell';
    return 'neutral';
  }

  getSignalColor(signal) {
    // Match the signal system's color mapping exactly
    const colors = {
      'strong-buy': '#00FF00',    // Green
      'mild-buy': '#90EE90',      // Light Green
      'neutral': '#808080',       // Gray
      'mild-sell': '#FFA07A',     // Light Red/Orange
      'strong-sell': '#FF0000',   // Red
      'error': '#FF00FF'          // Magenta for errors
    };
    return colors[signal] || '#808080';
  }

  setupPredictionUpdates() {
    // Listen for ML prediction updates
    document.addEventListener('mlPredictionUpdated', (e) => {
      this.handleMLUpdate(e.detail);
    });
  }
}

// Initialize ML Visualization when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  window.mlVisualization = new MLVisualization();
});
