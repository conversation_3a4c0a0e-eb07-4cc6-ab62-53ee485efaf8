/**
 * StarCrypt Signal Core - Unified Signal Processing System
 * Consolidates all signal functionality into a single, robust system
 */

class StarCryptSignalCore {
  constructor(options = {}) {
    // Configuration
    this.config = {
      maxSignalHistory: options.maxSignalHistory || 1000,
      signalTimeout: options.signalTimeout || 30000,
      batchSize: options.batchSize || 10,
      updateInterval: options.updateInterval || 100,
      debug: options.debug || false,
      ...options
    };

    // Signal state management
    this.signals = new Map(); // Current signals by indicator-timeframe key
    this.signalHistory = new Map(); // Historical signals
    this.signalQueue = []; // Pending signal updates
    this.lightStates = new Map(); // Current light states
    this.matrixState = new Map(); // Matrix state for each timeframe

    // Processing state
    this.isProcessing = false;
    this.lastUpdate = 0;
    this.updateCount = 0;

    // Event system
    this.eventListeners = new Map();
    this.changeCallbacks = new Set();

    // Timeframes and indicators
    this.timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
    this.indicators = new Set();

    // Color mapping
    this.colorMap = {
      red: '#ff4444',
      orange: '#ff8800',
      grey: '#888888',
      gray: '#888888', // Alternative spelling
      blue: '#4488ff',
      green: '#44ff44'
    };

    // Timers
    this.updateTimer = null;
    this.cleanupTimer = null;

    // Initialize
    this.init();
  }

  /**
   * Initialize the signal core
   */
  init() {
    this.log('Initializing Signal Core...');

    // Set up update timer
    this.updateTimer = setInterval(() => {
      this.processSignalQueue();
    }, this.config.updateInterval);

    // Set up cleanup timer
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, 60000); // Cleanup every minute

    // Set up event listeners
    this.setupEventListeners();

    // Initialize signal matrix
    this.initializeSignalMatrix();

    this.log('Signal Core initialized');
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    if (typeof document !== 'undefined') {
      // Listen for WebSocket messages
      document.addEventListener('starcrypt:message:signals', (event) => {
        this.handleSignalMessage(event.detail);
      });

      document.addEventListener('starcrypt:message:indicators', (event) => {
        this.handleIndicatorMessage(event.detail);
      });

      // Listen for strategy changes
      document.addEventListener('starcrypt:strategy:strategyChanged', (event) => {
        this.handleStrategyChange(event.detail);
      });

      // Listen for signal light clicks
      document.addEventListener('click', (event) => {
        if (event.target.classList.contains('signal-light')) {
          this.handleSignalLightClick(event);
        }
      });
    }
  }

  /**
   * Initialize signal matrix
   */
  initializeSignalMatrix() {
    const container = document.getElementById('momentum-indicators');
    if (!container) {
      this.warn('Signal matrix container not found');
      return;
    }

    // Clear existing content
    container.innerHTML = '';

    // Create matrix structure
    this.createMatrixStructure(container);

    this.log('Signal matrix initialized');
  }

  /**
   * Create matrix structure
   */
  createMatrixStructure(container) {
    // Create table structure
    const table = document.createElement('table');
    table.className = 'signal-matrix-table';
    table.id = 'signal-matrix';

    // Create header row
    const headerRow = document.createElement('tr');
    headerRow.className = 'matrix-header';

    // Add indicator column header
    const indicatorHeader = document.createElement('th');
    indicatorHeader.textContent = 'Indicator';
    indicatorHeader.className = 'indicator-header';
    headerRow.appendChild(indicatorHeader);

    // Add timeframe headers
    for (const timeframe of this.timeframes) {
      const th = document.createElement('th');
      th.textContent = timeframe;
      th.className = 'timeframe-header';
      th.setAttribute('data-timeframe', timeframe);
      headerRow.appendChild(th);
    }

    table.appendChild(headerRow);

    // Create indicator rows (will be populated when indicators are loaded)
    const tbody = document.createElement('tbody');
    tbody.id = 'signal-matrix-body';
    table.appendChild(tbody);

    container.appendChild(table);
  }

  /**
   * Handle signal message from WebSocket
   */
  handleSignalMessage(data) {
    try {
      const { indicator, timeframe, value, color, timestamp } = data;
      
      if (!indicator || !timeframe) {
        this.warn('Invalid signal data:', data);
        return;
      }

      // Create signal object
      const signal = {
        indicator,
        timeframe,
        value: parseFloat(value) || 0,
        color: color || 'grey',
        timestamp: timestamp || Date.now(),
        id: this.generateSignalId(indicator, timeframe)
      };

      // Queue signal for processing
      this.queueSignal(signal);

    } catch (error) {
      this.error('Error handling signal message:', error);
    }
  }

  /**
   * Handle indicator message from WebSocket
   */
  handleIndicatorMessage(data) {
    try {
      const { indicators } = data;
      
      if (Array.isArray(indicators)) {
        // Update available indicators
        for (const indicator of indicators) {
          this.indicators.add(indicator);
        }
        
        // Rebuild matrix if needed
        this.updateMatrixStructure();
      }

    } catch (error) {
      this.error('Error handling indicator message:', error);
    }
  }

  /**
   * Handle strategy change
   */
  handleStrategyChange(data) {
    try {
      const { strategy } = data;
      
      if (strategy && strategy.indicators) {
        // Update visible indicators
        this.updateIndicatorVisibility(strategy.indicators);
        
        // Update thresholds
        if (strategy.thresholds) {
          this.updateThresholds(strategy.thresholds);
        }
      }

    } catch (error) {
      this.error('Error handling strategy change:', error);
    }
  }

  /**
   * Queue signal for processing
   */
  queueSignal(signal) {
    // Add to queue
    this.signalQueue.push(signal);

    // Limit queue size
    if (this.signalQueue.length > this.config.batchSize * 10) {
      this.signalQueue = this.signalQueue.slice(-this.config.batchSize * 5);
      this.warn('Signal queue trimmed due to size');
    }

    // Process immediately if not already processing
    if (!this.isProcessing) {
      this.processSignalQueue();
    }
  }

  /**
   * Process signal queue
   */
  async processSignalQueue() {
    if (this.isProcessing || this.signalQueue.length === 0) {
      return;
    }

    this.isProcessing = true;

    try {
      // Process batch of signals
      const batchSize = Math.min(this.config.batchSize, this.signalQueue.length);
      const batch = this.signalQueue.splice(0, batchSize);

      for (const signal of batch) {
        await this.processSignal(signal);
      }

      this.updateCount++;
      this.lastUpdate = Date.now();

    } catch (error) {
      this.error('Error processing signal queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process a single signal
   */
  async processSignal(signal) {
    try {
      const { indicator, timeframe, value, color, timestamp, id } = signal;

      // Store current signal
      this.signals.set(id, signal);

      // Update signal history
      this.updateSignalHistory(signal);

      // Update light state
      this.updateLightState(indicator, timeframe, color, value);

      // Update matrix display
      this.updateMatrixDisplay(indicator, timeframe, color, value);

      // Update mini chart if exists
      this.updateMiniChart(indicator, timeframe, value, timestamp);

      // Emit signal update event
      this.emit('signalUpdated', { signal });

    } catch (error) {
      this.error('Error processing signal:', error);
    }
  }

  /**
   * Update signal history
   */
  updateSignalHistory(signal) {
    const { indicator, timeframe } = signal;
    const key = `${indicator}-${timeframe}`;

    if (!this.signalHistory.has(key)) {
      this.signalHistory.set(key, []);
    }

    const history = this.signalHistory.get(key);
    history.push(signal);

    // Limit history size
    if (history.length > this.config.maxSignalHistory) {
      history.splice(0, history.length - this.config.maxSignalHistory);
    }
  }

  /**
   * Update light state
   */
  updateLightState(indicator, timeframe, color, value) {
    const key = `${indicator}-${timeframe}`;
    
    this.lightStates.set(key, {
      color,
      value,
      timestamp: Date.now(),
      indicator,
      timeframe
    });
  }

  /**
   * Update matrix display
   */
  updateMatrixDisplay(indicator, timeframe, color, value) {
    const lightElement = document.getElementById(`light-${indicator}-${timeframe}`);
    if (lightElement) {
      // Update light color
      lightElement.className = `signal-light light-${color}`;
      lightElement.style.backgroundColor = this.colorMap[color] || color;
      
      // Update light value (tooltip)
      lightElement.title = `${indicator} (${timeframe}): ${value.toFixed(4)}`;
      
      // Add pulse animation for new signals
      lightElement.classList.add('pulse');
      setTimeout(() => {
        lightElement.classList.remove('pulse');
      }, 500);
    }
  }

  /**
   * Update mini chart
   */
  updateMiniChart(indicator, timeframe, value, timestamp) {
    const chartElement = document.getElementById(`chart-${indicator}-${timeframe}`);
    if (chartElement && window.MiniChartManager) {
      window.MiniChartManager.updateChart(indicator, timeframe, value, timestamp);
    }
  }

  /**
   * Update matrix structure when indicators change
   */
  updateMatrixStructure() {
    const tbody = document.getElementById('signal-matrix-body');
    if (!tbody) return;

    // Get current strategy indicators
    const currentStrategy = window.strategyCore?.getCurrentStrategy();
    const visibleIndicators = currentStrategy?.indicators || Array.from(this.indicators);

    // Clear existing rows
    tbody.innerHTML = '';

    // Create rows for each indicator
    for (const indicator of visibleIndicators) {
      const row = this.createIndicatorRow(indicator);
      tbody.appendChild(row);
    }
  }

  /**
   * Create indicator row
   */
  createIndicatorRow(indicator) {
    const row = document.createElement('tr');
    row.className = 'indicator-row';
    row.setAttribute('data-indicator', indicator);

    // Create indicator name cell
    const nameCell = document.createElement('td');
    nameCell.className = 'indicator-name';
    nameCell.textContent = indicator.toUpperCase();
    row.appendChild(nameCell);

    // Create signal light cells for each timeframe
    for (const timeframe of this.timeframes) {
      const cell = document.createElement('td');
      cell.className = 'signal-cell';
      cell.setAttribute('data-timeframe', timeframe);

      // Create signal light
      const light = document.createElement('div');
      light.className = 'signal-light light-grey';
      light.id = `light-${indicator}-${timeframe}`;
      light.setAttribute('data-indicator', indicator);
      light.setAttribute('data-timeframe', timeframe);
      light.textContent = timeframe;

      cell.appendChild(light);
      row.appendChild(cell);
    }

    return row;
  }

  /**
   * Update indicator visibility based on strategy
   */
  updateIndicatorVisibility(indicators) {
    const rows = document.querySelectorAll('.indicator-row');
    
    rows.forEach(row => {
      const indicator = row.getAttribute('data-indicator');
      const isVisible = indicators.includes(indicator);
      row.style.display = isVisible ? 'table-row' : 'none';
    });
  }

  /**
   * Update thresholds for signal processing
   */
  updateThresholds(thresholds) {
    this.thresholds = { ...thresholds };
    this.log('Thresholds updated:', this.thresholds);
  }

  /**
   * Handle signal light click
   */
  handleSignalLightClick(event) {
    const light = event.target;
    const indicator = light.getAttribute('data-indicator');
    const timeframe = light.getAttribute('data-timeframe');

    if (indicator && timeframe) {
      // Emit click event
      this.emit('lightClicked', { indicator, timeframe, element: light });

      // Handle Admiral mode vs normal mode
      if (window.isAdmiralMode) {
        this.handleAdmiralModeClick(indicator, timeframe, light);
      } else {
        this.handleNormalModeClick(indicator, timeframe, light);
      }
    }
  }

  /**
   * Handle Admiral mode click (historical analysis)
   */
  handleAdmiralModeClick(indicator, timeframe, light) {
    // Toggle selection
    light.classList.toggle('selected');
    
    // Update selected lights display
    this.updateSelectedLightsDisplay();
    
    // Emit Admiral selection event
    this.emit('admiralSelection', { indicator, timeframe, selected: light.classList.contains('selected') });
  }

  /**
   * Handle normal mode click (TradingView sync)
   */
  handleNormalModeClick(indicator, timeframe, light) {
    // Update TradingView timeframe
    if (window.tradingViewWidget) {
      window.tradingViewWidget.setSymbol(window.currentSymbol, timeframe);
    }
    
    // Highlight column
    this.highlightTimeframeColumn(timeframe);
    
    // Emit timeframe change event
    this.emit('timeframeChanged', { timeframe });
  }

  /**
   * Highlight timeframe column
   */
  highlightTimeframeColumn(timeframe) {
    // Remove existing highlights
    document.querySelectorAll('.signal-cell.highlighted').forEach(cell => {
      cell.classList.remove('highlighted');
    });

    // Add highlight to selected timeframe column
    document.querySelectorAll(`[data-timeframe="${timeframe}"]`).forEach(cell => {
      if (cell.classList.contains('signal-cell')) {
        cell.classList.add('highlighted');
      }
    });
  }

  /**
   * Update selected lights display for Admiral mode
   */
  updateSelectedLightsDisplay() {
    const selectedLights = document.querySelectorAll('.signal-light.selected');
    const display = document.getElementById('selectedLightsDisplay');
    
    if (display) {
      const selections = Array.from(selectedLights).map(light => ({
        indicator: light.getAttribute('data-indicator'),
        timeframe: light.getAttribute('data-timeframe')
      }));
      
      display.innerHTML = selections.map(sel => 
        `<span class="selected-light">${sel.indicator}-${sel.timeframe}</span>`
      ).join(', ');
    }
  }

  /**
   * Generate signal ID
   */
  generateSignalId(indicator, timeframe) {
    return `${indicator}-${timeframe}`;
  }

  /**
   * Get signal by ID
   */
  getSignal(indicator, timeframe) {
    const id = this.generateSignalId(indicator, timeframe);
    return this.signals.get(id);
  }

  /**
   * Get signal history
   */
  getSignalHistory(indicator, timeframe, limit = 100) {
    const key = `${indicator}-${timeframe}`;
    const history = this.signalHistory.get(key) || [];
    return history.slice(-limit);
  }

  /**
   * Get current light states
   */
  getLightStates() {
    return new Map(this.lightStates);
  }

  /**
   * Event system methods
   */
  on(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(listener);
  }

  off(event, listener) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      for (const listener of listeners) {
        try {
          listener(data);
        } catch (error) {
          this.error(`Event listener error for ${event}:`, error);
        }
      }
    }

    // Also dispatch DOM event
    if (typeof document !== 'undefined') {
      try {
        const customEvent = new CustomEvent(`starcrypt:signal:${event}`, { detail: data });
        document.dispatchEvent(customEvent);
      } catch (error) {
        this.error(`Error dispatching DOM event ${event}:`, error);
      }
    }
  }

  /**
   * Cleanup old signals and history
   */
  cleanup() {
    const now = Date.now();
    const cutoff = now - this.config.signalTimeout;

    // Clean old signals
    for (const [id, signal] of this.signals.entries()) {
      if (signal.timestamp < cutoff) {
        this.signals.delete(id);
      }
    }

    // Clean old light states
    for (const [key, state] of this.lightStates.entries()) {
      if (state.timestamp < cutoff) {
        this.lightStates.delete(key);
      }
    }

    this.log('Cleanup completed');
  }

  /**
   * Clear all signals
   */
  clearSignals() {
    this.signals.clear();
    this.lightStates.clear();
    this.signalQueue.length = 0;

    // Reset all lights to grey
    document.querySelectorAll('.signal-light').forEach(light => {
      light.className = 'signal-light light-grey';
      light.style.backgroundColor = this.colorMap.grey;
    });

    this.emit('signalsCleared');
    this.log('All signals cleared');
  }

  /**
   * Force update all signals
   */
  forceUpdate() {
    // Re-process all current signals
    for (const signal of this.signals.values()) {
      this.updateMatrixDisplay(signal.indicator, signal.timeframe, signal.color, signal.value);
    }

    this.emit('forceUpdated');
    this.log('Force update completed');
  }

  /**
   * Get signal statistics
   */
  getStats() {
    return {
      totalSignals: this.signals.size,
      queueLength: this.signalQueue.length,
      historySize: Array.from(this.signalHistory.values()).reduce((sum, arr) => sum + arr.length, 0),
      lightStates: this.lightStates.size,
      indicators: this.indicators.size,
      timeframes: this.timeframes.length,
      updateCount: this.updateCount,
      lastUpdate: this.lastUpdate,
      isProcessing: this.isProcessing,
      config: { ...this.config }
    };
  }

  /**
   * Export signal data
   */
  exportSignals() {
    return {
      signals: Array.from(this.signals.entries()),
      lightStates: Array.from(this.lightStates.entries()),
      signalHistory: Array.from(this.signalHistory.entries()),
      stats: this.getStats(),
      timestamp: Date.now()
    };
  }

  /**
   * Import signal data
   */
  importSignals(data) {
    try {
      if (data.signals) {
        this.signals = new Map(data.signals);
      }
      if (data.lightStates) {
        this.lightStates = new Map(data.lightStates);
      }
      if (data.signalHistory) {
        this.signalHistory = new Map(data.signalHistory);
      }

      // Update display
      this.forceUpdate();

      this.emit('signalsImported', data);
      this.log('Signals imported successfully');
      return true;

    } catch (error) {
      this.error('Error importing signals:', error);
      return false;
    }
  }

  /**
   * Set Admiral mode
   */
  setAdmiralMode(enabled) {
    window.isAdmiralMode = enabled;

    // Update UI for Admiral mode
    document.body.classList.toggle('admiral-mode', enabled);

    // Clear selections if disabling
    if (!enabled) {
      document.querySelectorAll('.signal-light.selected').forEach(light => {
        light.classList.remove('selected');
      });
      this.updateSelectedLightsDisplay();
    }

    this.emit('admiralModeChanged', { enabled });
    this.log('Admiral mode:', enabled ? 'enabled' : 'disabled');
  }

  /**
   * Get selected signals (Admiral mode)
   */
  getSelectedSignals() {
    const selectedLights = document.querySelectorAll('.signal-light.selected');
    return Array.from(selectedLights).map(light => ({
      indicator: light.getAttribute('data-indicator'),
      timeframe: light.getAttribute('data-timeframe'),
      signal: this.getSignal(light.getAttribute('data-indicator'), light.getAttribute('data-timeframe'))
    }));
  }

  /**
   * Clear selected signals (Admiral mode)
   */
  clearSelectedSignals() {
    document.querySelectorAll('.signal-light.selected').forEach(light => {
      light.classList.remove('selected');
    });
    this.updateSelectedLightsDisplay();
    this.emit('selectionsCleared');
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.log('Configuration updated:', newConfig);
  }

  /**
   * Utility methods
   */
  log(...args) {
    if (this.config.debug) {
      console.log('[StarCrypt Signal]', ...args);
    }
  }

  warn(...args) {
    console.warn('[StarCrypt Signal]', ...args);
  }

  error(...args) {
    console.error('[StarCrypt Signal]', ...args);
  }

  /**
   * Destroy signal core
   */
  destroy() {
    // Clear timers
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // Clear data
    this.signals.clear();
    this.signalHistory.clear();
    this.lightStates.clear();
    this.signalQueue.length = 0;

    // Clear event listeners
    this.eventListeners.clear();
    this.changeCallbacks.clear();

    this.log('Signal Core destroyed');
  }
}

// Create singleton instance
let signalCoreInstance = null;

/**
 * Get or create signal core singleton
 */
function getSignalCore(options = {}) {
  if (!signalCoreInstance) {
    signalCoreInstance = new StarCryptSignalCore(options);
  }
  return signalCoreInstance;
}

// Export for both CommonJS and ES6
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { StarCryptSignalCore, getSignalCore };
} else if (typeof window !== 'undefined') {
  window.StarCryptSignalCore = StarCryptSignalCore;
  window.getSignalCore = getSignalCore;

  // Initialize global instance
  window.signalCore = getSignalCore();
}
