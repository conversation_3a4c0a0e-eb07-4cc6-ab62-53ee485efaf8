/**
 * Event Manager for StarCrypt
 * Centralized event listener management to prevent duplicates and memory leaks
 */

class EventManager {
  constructor() {
    this.listeners = new Map(); // Track all event listeners
    this.debounceTimers = new Map(); // Track debounce timers
    this.isInitialized = false;
    
    // Bind methods to preserve context
    this.cleanup = this.cleanup.bind(this);
    this.handleBeforeUnload = this.handleBeforeUnload.bind(this);
    
    // Setup cleanup on page unload
    window.addEventListener('beforeunload', this.handleBeforeUnload);
  }

  /**
   * Add event listener with automatic tracking and duplicate prevention
   * @param {Element|Window|Document} element - Target element
   * @param {string} event - Event type
   * @param {Function} handler - Event handler
   * @param {Object} options - Event options
   * @returns {Function} - Cleanup function
   */
  addEventListener(element, event, handler, options = {}) {
    const key = this.generateKey(element, event, handler);
    
    // Check for duplicates
    if (this.listeners.has(key)) {
      console.warn(`[EventManager] Duplicate listener prevented for ${event} on`, element);
      return this.listeners.get(key).cleanup;
    }

    // Add the listener
    element.addEventListener(event, handler, options);
    
    // Store cleanup function
    const cleanup = () => {
      element.removeEventListener(event, handler, options);
      this.listeners.delete(key);
    };
    
    this.listeners.set(key, {
      element,
      event,
      handler,
      options,
      cleanup,
      timestamp: Date.now()
    });

    return cleanup;
  }

  /**
   * Remove specific event listener
   * @param {Element|Window|Document} element - Target element
   * @param {string} event - Event type
   * @param {Function} handler - Event handler
   */
  removeEventListener(element, event, handler) {
    const key = this.generateKey(element, event, handler);
    const listener = this.listeners.get(key);
    
    if (listener) {
      listener.cleanup();
      console.log(`[EventManager] Removed listener for ${event}`);
    }
  }

  /**
   * Add debounced event listener
   * @param {Element|Window|Document} element - Target element
   * @param {string} event - Event type
   * @param {Function} handler - Event handler
   * @param {number} delay - Debounce delay in ms
   * @param {Object} options - Event options
   * @returns {Function} - Cleanup function
   */
  addDebouncedListener(element, event, handler, delay = 100, options = {}) {
    const debouncedHandler = this.debounce(handler, delay);
    return this.addEventListener(element, event, debouncedHandler, options);
  }

  /**
   * Debounce function with cleanup tracking
   * @param {Function} func - Function to debounce
   * @param {number} wait - Wait time in ms
   * @returns {Function} - Debounced function
   */
  debounce(func, wait) {
    const key = func.toString() + wait;
    
    return (...args) => {
      const existingTimer = this.debounceTimers.get(key);
      if (existingTimer) {
        clearTimeout(existingTimer);
      }
      
      const timer = setTimeout(() => {
        this.debounceTimers.delete(key);
        func.apply(this, args);
      }, wait);
      
      this.debounceTimers.set(key, timer);
    };
  }

  /**
   * Generate unique key for listener tracking
   * @param {Element|Window|Document} element - Target element
   * @param {string} event - Event type
   * @param {Function} handler - Event handler
   * @returns {string} - Unique key
   */
  generateKey(element, event, handler) {
    const elementId = element.id || element.tagName || element.constructor.name;
    const handlerName = handler.name || 'anonymous';
    return `${elementId}_${event}_${handlerName}_${handler.toString().slice(0, 50)}`;
  }

  /**
   * Clean up all event listeners
   */
  cleanup() {
    console.log(`[EventManager] Cleaning up ${this.listeners.size} event listeners`);
    
    // Remove all event listeners
    for (const [key, listener] of this.listeners) {
      try {
        listener.cleanup();
      } catch (error) {
        console.error(`[EventManager] Error cleaning up listener ${key}:`, error);
      }
    }
    
    // Clear debounce timers
    for (const [key, timer] of this.debounceTimers) {
      clearTimeout(timer);
    }
    
    this.listeners.clear();
    this.debounceTimers.clear();
    
    console.log('[EventManager] Cleanup complete');
  }

  /**
   * Handle page unload
   */
  handleBeforeUnload() {
    this.cleanup();
  }

  /**
   * Get listener statistics
   * @returns {Object} - Statistics object
   */
  getStats() {
    const stats = {
      totalListeners: this.listeners.size,
      activeTimers: this.debounceTimers.size,
      listenersByEvent: {},
      listenersByElement: {}
    };

    for (const [key, listener] of this.listeners) {
      // Count by event type
      stats.listenersByEvent[listener.event] = (stats.listenersByEvent[listener.event] || 0) + 1;
      
      // Count by element type
      const elementType = listener.element.tagName || listener.element.constructor.name;
      stats.listenersByElement[elementType] = (stats.listenersByElement[elementType] || 0) + 1;
    }

    return stats;
  }

  /**
   * Log current listener statistics
   */
  logStats() {
    const stats = this.getStats();
    console.log('[EventManager] Current Statistics:', stats);
  }
}

// Create global instance
window.eventManager = new EventManager();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EventManager;
}
