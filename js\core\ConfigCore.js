/**
 * StarCrypt Configuration Core - Unified Configuration Management System
 * Centralizes all configuration across the application
 */

class StarCryptConfigCore {
  constructor(options = {}) {
    // Default configuration
    this.defaultConfig = {
      // Application settings
      app: {
        name: 'StarCrypt Enterprise',
        version: '1.0.0',
        debug: false,
        environment: 'production'
      },

      // WebSocket settings
      websocket: {
        url: this.getWebSocketURL(),
        protocols: [],
        maxReconnectAttempts: 10,
        reconnectInterval: 1000,
        maxReconnectInterval: 30000,
        connectionTimeout: 5000,
        pingInterval: 25000,
        pingTimeout: 10000,
        maxBatchSize: 10,
        maxQueueSize: 1000,
        processDelay: 10,
        maxDepth: 5,
        maxConsecutiveErrors: 10,
        errorResetTime: 60000,
        autoConnect: true
      },

      // Strategy settings
      strategy: {
        defaultStrategy: 'admiral_toa',
        autoSave: true,
        debug: false
      },

      // Signal processing settings
      signals: {
        maxSignalHistory: 1000,
        signalTimeout: 30000,
        batchSize: 10,
        updateInterval: 100,
        debug: false
      },

      // UI settings
      ui: {
        theme: 'dark',
        animations: true,
        notifications: true,
        autoHideMenus: true,
        compactMode: false,
        showTooltips: true
      },

      // Chart settings
      charts: {
        defaultTimeframe: '1h',
        showVolume: true,
        showIndicators: true,
        autoScale: true,
        smoothing: true,
        gradients: true
      },

      // Trading settings
      trading: {
        defaultSymbol: 'BTCUSD',
        riskLevel: 'medium',
        autoTrading: false,
        confirmTrades: true,
        maxPositions: 5
      },

      // AI settings
      ai: {
        enabled: true,
        model: 'grok-3',
        apiKey: '',
        maxTokens: 1000,
        temperature: 0.7,
        timeout: 30000
      },

      // Performance settings
      performance: {
        maxMemoryUsage: 512, // MB
        gcInterval: 300000, // 5 minutes
        cacheSize: 1000,
        batchProcessing: true,
        lazyLoading: true
      },

      // Security settings
      security: {
        enableCSP: true,
        sanitizeInputs: true,
        validateMessages: true,
        encryptStorage: false
      },

      // Logging settings
      logging: {
        level: 'info', // debug, info, warn, error
        console: true,
        file: false,
        remote: false,
        maxLogSize: 10 // MB
      }
    };

    // Current configuration
    this.config = {};

    // Configuration sources
    this.sources = new Map();

    // Event system
    this.eventListeners = new Map();

    // Validation rules
    this.validationRules = new Map();

    // Initialize
    this.init(options);
  }

  /**
   * Get WebSocket URL based on environment
   */
  getWebSocketURL() {
    if (typeof window !== 'undefined') {
      const isLocalhost = window.location.hostname === 'localhost';
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = isLocalhost ? 'localhost:8080' : window.location.host;
      return `${protocol}//${host}`;
    }
    return 'ws://localhost:8080';
  }

  /**
   * Initialize configuration core
   */
  init(options = {}) {
    console.log('[StarCrypt Config] Initializing Configuration Core...');

    // Set up validation rules
    this.setupValidationRules();

    // Load configuration from various sources
    this.loadConfiguration(options);

    // Set up auto-save if enabled
    this.setupAutoSave();

    // Set up environment detection
    this.detectEnvironment();

    console.log('[StarCrypt Config] Configuration Core initialized');
  }

  /**
   * Set up validation rules
   */
  setupValidationRules() {
    // WebSocket validation
    this.validationRules.set('websocket.maxReconnectAttempts', (value) => 
      Number.isInteger(value) && value >= 0 && value <= 100
    );
    this.validationRules.set('websocket.reconnectInterval', (value) => 
      Number.isInteger(value) && value >= 100 && value <= 60000
    );
    this.validationRules.set('websocket.url', (value) => 
      typeof value === 'string' && (value.startsWith('ws://') || value.startsWith('wss://'))
    );

    // Strategy validation
    this.validationRules.set('strategy.defaultStrategy', (value) => 
      typeof value === 'string' && value.length > 0
    );

    // UI validation
    this.validationRules.set('ui.theme', (value) => 
      ['light', 'dark', 'auto'].includes(value)
    );

    // Performance validation
    this.validationRules.set('performance.maxMemoryUsage', (value) => 
      Number.isInteger(value) && value >= 64 && value <= 2048
    );

    // Logging validation
    this.validationRules.set('logging.level', (value) => 
      ['debug', 'info', 'warn', 'error'].includes(value)
    );
  }

  /**
   * Load configuration from various sources
   */
  loadConfiguration(options = {}) {
    // Start with default configuration
    this.config = this.deepClone(this.defaultConfig);

    // Load from localStorage
    this.loadFromStorage();

    // Load from environment variables
    this.loadFromEnvironment();

    // Load from URL parameters
    this.loadFromURL();

    // Apply provided options
    this.mergeConfig(options);

    // Validate final configuration
    this.validateConfiguration();

    console.log('[StarCrypt Config] Configuration loaded');
  }

  /**
   * Load configuration from localStorage
   */
  loadFromStorage() {
    try {
      const stored = localStorage.getItem('starcrypt_config');
      if (stored) {
        const parsedConfig = JSON.parse(stored);
        this.mergeConfig(parsedConfig);
        this.sources.set('localStorage', parsedConfig);
      }
    } catch (error) {
      console.warn('[StarCrypt Config] Error loading from localStorage:', error);
    }
  }

  /**
   * Load configuration from environment variables
   */
  loadFromEnvironment() {
    const envConfig = {};

    // Check for environment-specific settings
    if (typeof window !== 'undefined') {
      // Browser environment
      if (window.location.hostname === 'localhost') {
        envConfig.app = { environment: 'development', debug: true };
        envConfig.logging = { level: 'debug' };
      }
    } else if (typeof process !== 'undefined') {
      // Node.js environment
      if (process.env.NODE_ENV === 'development') {
        envConfig.app = { environment: 'development', debug: true };
        envConfig.logging = { level: 'debug' };
      }
      
      // Load specific environment variables
      if (process.env.WEBSOCKET_URL) {
        envConfig.websocket = { url: process.env.WEBSOCKET_URL };
      }
      if (process.env.AI_API_KEY) {
        envConfig.ai = { apiKey: process.env.AI_API_KEY };
      }
    }

    if (Object.keys(envConfig).length > 0) {
      this.mergeConfig(envConfig);
      this.sources.set('environment', envConfig);
    }
  }

  /**
   * Load configuration from URL parameters
   */
  loadFromURL() {
    if (typeof window === 'undefined') return;

    const urlParams = new URLSearchParams(window.location.search);
    const urlConfig = {};

    // Check for debug parameter
    if (urlParams.has('debug')) {
      urlConfig.app = { debug: urlParams.get('debug') === 'true' };
    }

    // Check for theme parameter
    if (urlParams.has('theme')) {
      urlConfig.ui = { theme: urlParams.get('theme') };
    }

    // Check for strategy parameter
    if (urlParams.has('strategy')) {
      urlConfig.strategy = { defaultStrategy: urlParams.get('strategy') };
    }

    if (Object.keys(urlConfig).length > 0) {
      this.mergeConfig(urlConfig);
      this.sources.set('url', urlConfig);
    }
  }

  /**
   * Merge configuration objects
   */
  mergeConfig(newConfig) {
    this.config = this.deepMerge(this.config, newConfig);
  }

  /**
   * Deep merge two objects
   */
  deepMerge(target, source) {
    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (this.isObject(source[key]) && this.isObject(target[key])) {
          result[key] = this.deepMerge(target[key], source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }

    return result;
  }

  /**
   * Deep clone an object
   */
  deepClone(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => this.deepClone(item));
    if (typeof obj === 'object') {
      const cloned = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          cloned[key] = this.deepClone(obj[key]);
        }
      }
      return cloned;
    }
  }

  /**
   * Check if value is an object
   */
  isObject(value) {
    return value !== null && typeof value === 'object' && !Array.isArray(value);
  }

  /**
   * Validate configuration
   */
  validateConfiguration() {
    const errors = [];

    for (const [path, validator] of this.validationRules.entries()) {
      const value = this.get(path);
      if (value !== undefined && !validator(value)) {
        errors.push(`Invalid value for ${path}: ${value}`);
      }
    }

    if (errors.length > 0) {
      console.warn('[StarCrypt Config] Configuration validation errors:', errors);
    }

    return errors.length === 0;
  }

  /**
   * Get configuration value by path
   */
  get(path, defaultValue = undefined) {
    const keys = path.split('.');
    let current = this.config;

    for (const key of keys) {
      if (current && typeof current === 'object' && key in current) {
        current = current[key];
      } else {
        return defaultValue;
      }
    }

    return current;
  }

  /**
   * Set configuration value by path
   */
  set(path, value, options = {}) {
    const keys = path.split('.');
    const lastKey = keys.pop();
    let current = this.config;

    // Navigate to parent object
    for (const key of keys) {
      if (!current[key] || typeof current[key] !== 'object') {
        current[key] = {};
      }
      current = current[key];
    }

    // Validate value if rule exists
    if (this.validationRules.has(path)) {
      const validator = this.validationRules.get(path);
      if (!validator(value)) {
        throw new Error(`Invalid value for ${path}: ${value}`);
      }
    }

    // Set value
    const oldValue = current[lastKey];
    current[lastKey] = value;

    // Emit change event
    this.emit('configChanged', { path, value, oldValue, options });

    // Auto-save if enabled
    if (options.save !== false && this.get('app.autoSave', true)) {
      this.save();
    }

    console.log(`[StarCrypt Config] Set ${path} = ${value}`);
  }

  /**
   * Save configuration to localStorage
   */
  save() {
    try {
      const configToSave = this.filterSavableConfig();
      localStorage.setItem('starcrypt_config', JSON.stringify(configToSave));
      this.emit('configSaved', { config: configToSave });
      console.log('[StarCrypt Config] Configuration saved');
    } catch (error) {
      console.error('[StarCrypt Config] Error saving configuration:', error);
    }
  }

  /**
   * Filter configuration for saving (exclude sensitive data)
   */
  filterSavableConfig() {
    const filtered = this.deepClone(this.config);
    
    // Remove sensitive data
    if (filtered.ai && filtered.ai.apiKey) {
      delete filtered.ai.apiKey;
    }

    return filtered;
  }

  /**
   * Reset configuration to defaults
   */
  reset(section = null) {
    if (section) {
      this.config[section] = this.deepClone(this.defaultConfig[section]);
      this.emit('configReset', { section });
    } else {
      this.config = this.deepClone(this.defaultConfig);
      this.emit('configReset', { section: 'all' });
    }

    this.save();
    console.log(`[StarCrypt Config] Configuration reset: ${section || 'all'}`);
  }

  /**
   * Set up auto-save functionality
   */
  setupAutoSave() {
    if (typeof window !== 'undefined') {
      // Save configuration before page unload
      window.addEventListener('beforeunload', () => {
        this.save();
      });

      // Save configuration periodically
      setInterval(() => {
        if (this.get('app.autoSave', true)) {
          this.save();
        }
      }, 60000); // Save every minute
    }
  }

  /**
   * Detect environment and adjust configuration
   */
  detectEnvironment() {
    let environment = 'production';
    let debug = false;

    if (typeof window !== 'undefined') {
      // Browser environment detection
      if (window.location.hostname === 'localhost' ||
          window.location.hostname === '127.0.0.1' ||
          window.location.hostname.includes('dev')) {
        environment = 'development';
        debug = true;
      }
    } else if (typeof process !== 'undefined') {
      // Node.js environment detection
      environment = process.env.NODE_ENV || 'production';
      debug = environment === 'development';
    }

    // Update configuration
    this.set('app.environment', environment, { save: false });
    this.set('app.debug', debug, { save: false });

    console.log(`[StarCrypt Config] Environment detected: ${environment}`);
  }

  /**
   * Get all configuration
   */
  getAll() {
    return this.deepClone(this.config);
  }

  /**
   * Get configuration section
   */
  getSection(section) {
    return this.deepClone(this.config[section] || {});
  }

  /**
   * Update entire section
   */
  setSection(section, config, options = {}) {
    if (!this.isObject(config)) {
      throw new Error('Section config must be an object');
    }

    const oldConfig = this.config[section];
    this.config[section] = this.deepClone(config);

    this.emit('sectionChanged', { section, config, oldConfig, options });

    if (options.save !== false && this.get('app.autoSave', true)) {
      this.save();
    }

    console.log(`[StarCrypt Config] Section updated: ${section}`);
  }

  /**
   * Check if configuration has changed
   */
  hasChanged(section = null) {
    const current = section ? this.config[section] : this.config;
    const original = section ? this.defaultConfig[section] : this.defaultConfig;

    return JSON.stringify(current) !== JSON.stringify(original);
  }

  /**
   * Get configuration differences from default
   */
  getDifferences() {
    const differences = {};

    const findDifferences = (current, original, path = '') => {
      for (const key in current) {
        const currentPath = path ? `${path}.${key}` : key;

        if (!(key in original)) {
          differences[currentPath] = { type: 'added', value: current[key] };
        } else if (this.isObject(current[key]) && this.isObject(original[key])) {
          findDifferences(current[key], original[key], currentPath);
        } else if (current[key] !== original[key]) {
          differences[currentPath] = {
            type: 'changed',
            value: current[key],
            original: original[key]
          };
        }
      }
    };

    findDifferences(this.config, this.defaultConfig);
    return differences;
  }

  /**
   * Export configuration
   */
  export(options = {}) {
    const exportData = {
      config: options.includeSensitive ? this.config : this.filterSavableConfig(),
      sources: Array.from(this.sources.entries()),
      timestamp: Date.now(),
      version: this.get('app.version'),
      environment: this.get('app.environment')
    };

    if (options.format === 'json') {
      return JSON.stringify(exportData, null, 2);
    }

    return exportData;
  }

  /**
   * Import configuration
   */
  import(data, options = {}) {
    try {
      let importData;

      if (typeof data === 'string') {
        importData = JSON.parse(data);
      } else {
        importData = data;
      }

      if (importData.config) {
        if (options.merge) {
          this.mergeConfig(importData.config);
        } else {
          this.config = this.deepClone(importData.config);
        }

        this.validateConfiguration();

        if (options.save !== false) {
          this.save();
        }

        this.emit('configImported', { data: importData, options });
        console.log('[StarCrypt Config] Configuration imported');
        return true;
      }

      throw new Error('Invalid import data format');

    } catch (error) {
      console.error('[StarCrypt Config] Error importing configuration:', error);
      return false;
    }
  }

  /**
   * Watch for configuration changes
   */
  watch(path, callback) {
    const watchKey = `watch:${path}`;

    if (!this.eventListeners.has(watchKey)) {
      this.eventListeners.set(watchKey, []);
    }

    this.eventListeners.get(watchKey).push(callback);

    // Also listen to general config changes
    this.on('configChanged', (event) => {
      if (event.path === path || event.path.startsWith(path + '.')) {
        callback(event);
      }
    });

    return () => this.unwatch(path, callback);
  }

  /**
   * Stop watching configuration changes
   */
  unwatch(path, callback) {
    const watchKey = `watch:${path}`;

    if (this.eventListeners.has(watchKey)) {
      const listeners = this.eventListeners.get(watchKey);
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Event system methods
   */
  on(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(listener);
  }

  off(event, listener) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      for (const listener of listeners) {
        try {
          listener(data);
        } catch (error) {
          console.error(`[StarCrypt Config] Event listener error for ${event}:`, error);
        }
      }
    }

    // Also dispatch DOM event
    if (typeof document !== 'undefined') {
      try {
        const customEvent = new CustomEvent(`starcrypt:config:${event}`, { detail: data });
        document.dispatchEvent(customEvent);
      } catch (error) {
        console.error(`[StarCrypt Config] Error dispatching DOM event ${event}:`, error);
      }
    }
  }

  /**
   * Get configuration statistics
   */
  getStats() {
    return {
      totalKeys: this.countKeys(this.config),
      sections: Object.keys(this.config).length,
      sources: Array.from(this.sources.keys()),
      hasChanges: this.hasChanged(),
      environment: this.get('app.environment'),
      debug: this.get('app.debug'),
      lastSaved: localStorage.getItem('starcrypt_config_timestamp'),
      validationRules: this.validationRules.size
    };
  }

  /**
   * Count total configuration keys
   */
  countKeys(obj, count = 0) {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        count++;
        if (this.isObject(obj[key])) {
          count = this.countKeys(obj[key], count);
        }
      }
    }
    return count;
  }

  /**
   * Validate specific value
   */
  validate(path, value) {
    if (this.validationRules.has(path)) {
      const validator = this.validationRules.get(path);
      return validator(value);
    }
    return true; // No validation rule means valid
  }

  /**
   * Add validation rule
   */
  addValidationRule(path, validator) {
    if (typeof validator !== 'function') {
      throw new Error('Validator must be a function');
    }
    this.validationRules.set(path, validator);
  }

  /**
   * Remove validation rule
   */
  removeValidationRule(path) {
    return this.validationRules.delete(path);
  }
}

// Create singleton instance
let configCoreInstance = null;

/**
 * Get or create configuration core singleton
 */
function getConfigCore(options = {}) {
  if (!configCoreInstance) {
    configCoreInstance = new StarCryptConfigCore(options);
  }
  return configCoreInstance;
}

// Export for both CommonJS and ES6
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { StarCryptConfigCore, getConfigCore };
} else if (typeof window !== 'undefined') {
  window.StarCryptConfigCore = StarCryptConfigCore;
  window.getConfigCore = getConfigCore;

  // Initialize global instance
  window.configCore = getConfigCore();
}
