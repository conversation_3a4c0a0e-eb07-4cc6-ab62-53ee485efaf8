/**
 * Indicator Menu Fixes for StarCrypt
 * Adds reset to default button and fixes apply button Oracle Matrix updates
 */

class IndicatorMenuFixes {
  constructor() {
    this.isApplying = false;
    this.resetDebounce = null;
    this.init();
  }

  init() {
    console.log('[IndicatorMenuFixes] Initializing indicator menu fixes...');
    
    try {
      this.addResetButton();
      this.fixApplyButton();
      this.enhanceIndicatorToggles();
      this.addMenuStyles();
      
      console.log('[IndicatorMenuFixes] Indicator menu fixes applied successfully');
    } catch (error) {
      console.error('[IndicatorMenuFixes] Error applying indicator menu fixes:', error);
    }
  }

  addResetButton() {
    // Find all indicator menu containers
    const menuContainers = [
      document.getElementById('indicatorMenu'),
      document.querySelector('.indicator-menu'),
      document.querySelector('.enhanced-indicator-menu')
    ].filter(Boolean);

    menuContainers.forEach(container => {
      this.addResetButtonToContainer(container);
    });

    // Also add to any future indicator menus
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            if (node.id === 'indicatorMenu' || node.classList?.contains('indicator-menu')) {
              this.addResetButtonToContainer(node);
            }
            
            // Check for nested indicator menus
            const nestedMenus = node.querySelectorAll?.('#indicatorMenu, .indicator-menu, .enhanced-indicator-menu');
            nestedMenus?.forEach(menu => this.addResetButtonToContainer(menu));
          }
        });
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });
  }

  addResetButtonToContainer(container) {
    if (!container || container.querySelector('.reset-to-default-btn')) return;

    // Find existing action buttons area
    let actionsArea = container.querySelector('.menu-actions, .indicator-actions, .button-group');
    
    if (!actionsArea) {
      // Create actions area if it doesn't exist
      actionsArea = document.createElement('div');
      actionsArea.className = 'menu-actions';
      
      // Find a good place to insert it
      const applyButton = container.querySelector('#applyIndicatorsButton, #applyChangesButton, .apply-changes');
      if (applyButton) {
        applyButton.parentNode.insertBefore(actionsArea, applyButton.nextSibling);
        actionsArea.appendChild(applyButton);
      } else {
        container.appendChild(actionsArea);
      }
    }

    // Create reset button
    const resetButton = document.createElement('button');
    resetButton.className = 'reset-to-default-btn action-button';
    resetButton.id = 'resetToDefaultButton';
    resetButton.innerHTML = `
      <span class="button-icon">🔄</span>
      <span class="button-text">Reset to Default</span>
    `;
    resetButton.title = 'Reset indicators to current strategy defaults';

    // Add reset button event listener
    resetButton.addEventListener('click', () => this.resetToDefault());

    // Insert reset button before apply button or at the end
    const applyButton = actionsArea.querySelector('#applyIndicatorsButton, #applyChangesButton, .apply-changes');
    if (applyButton) {
      actionsArea.insertBefore(resetButton, applyButton);
    } else {
      actionsArea.appendChild(resetButton);
    }

    console.log('[IndicatorMenuFixes] Added reset button to indicator menu');
  }

  resetToDefault() {
    if (this.resetDebounce) {
      clearTimeout(this.resetDebounce);
    }

    this.resetDebounce = setTimeout(() => {
      try {
        console.log('[IndicatorMenuFixes] Resetting indicators to strategy defaults...');

        // Get current strategy defaults
        const currentStrategy = window.currentStrategy || 'admiral_toa';
        const strategyDetails = window.TRADING_STRATEGIES?.[currentStrategy];
        
        if (!strategyDetails || !strategyDetails.indicators) {
          console.error('[IndicatorMenuFixes] No strategy indicators found for:', currentStrategy);
          this.showNotification('❌ Could not find strategy defaults', 'error');
          return;
        }

        const defaultIndicators = [...strategyDetails.indicators];
        console.log('[IndicatorMenuFixes] Default indicators for', currentStrategy, ':', defaultIndicators);

        // Update global enabled indicators
        if (window.enabledIndicators) {
          window.enabledIndicators.forEach(indicator => {
            indicator.enabled = defaultIndicators.includes(indicator.name);
          });
        }

        // Update enhanced indicator menu if it exists
        if (window.enhancedIndicatorMenu) {
          window.enhancedIndicatorMenu.enabledIndicators = [...defaultIndicators];
          window.enhancedIndicatorMenu.updateCheckboxes();
          window.enhancedIndicatorMenu.updatePreview();
          window.enhancedIndicatorMenu.updateEnabledCount();
        }

        // Update regular indicator menu checkboxes
        const checkboxes = document.querySelectorAll('.indicator-toggle, input[data-indicator]');
        checkboxes.forEach(checkbox => {
          const indicator = checkbox.dataset.indicator || checkbox.dataset.name;
          if (indicator) {
            checkbox.checked = defaultIndicators.includes(indicator);
            
            // Update visual state
            const item = checkbox.closest('.indicator-item, .indicator-row');
            if (item) {
              item.classList.toggle('enabled', checkbox.checked);
            }
          }
        });

        // Save to localStorage
        localStorage.setItem('enabledIndicators', JSON.stringify(defaultIndicators));

        // Apply changes immediately
        this.applyIndicatorChanges();

        this.showNotification(`✅ Reset to ${strategyDetails.name} defaults (${defaultIndicators.length} indicators)`, 'success');
        
      } catch (error) {
        console.error('[IndicatorMenuFixes] Error resetting to defaults:', error);
        this.showNotification('❌ Error resetting indicators', 'error');
      }
    }, 300);
  }

  fixApplyButton() {
    // Find and enhance all apply buttons
    const applyButtons = document.querySelectorAll('#applyIndicatorsButton, #applyChangesButton, .apply-changes');
    
    applyButtons.forEach(button => {
      // Remove existing event listeners by cloning
      const newButton = button.cloneNode(true);
      button.parentNode.replaceChild(newButton, button);
      
      // Add enhanced event listener
      newButton.addEventListener('click', () => this.applyIndicatorChanges());
    });

    // Also monitor for new apply buttons
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const newApplyButtons = node.querySelectorAll?.('#applyIndicatorsButton, #applyChangesButton, .apply-changes');
            newApplyButtons?.forEach(button => {
              button.addEventListener('click', () => this.applyIndicatorChanges());
            });
          }
        });
      });
    });

    observer.observe(document.body, { childList: true, subtree: true });
  }

  applyIndicatorChanges() {
    if (this.isApplying) {
      console.log('[IndicatorMenuFixes] Apply already in progress, skipping...');
      return;
    }

    this.isApplying = true;
    
    try {
      console.log('[IndicatorMenuFixes] Applying indicator changes...');

      // Get currently enabled indicators from checkboxes
      const enabledIndicators = [];
      const checkboxes = document.querySelectorAll('.indicator-toggle:checked, input[data-indicator]:checked');
      
      checkboxes.forEach(checkbox => {
        const indicator = checkbox.dataset.indicator || checkbox.dataset.name;
        if (indicator && !enabledIndicators.includes(indicator)) {
          enabledIndicators.push(indicator);
        }
      });

      // Also check enhanced indicator menu
      if (window.enhancedIndicatorMenu?.enabledIndicators) {
        window.enhancedIndicatorMenu.enabledIndicators.forEach(indicator => {
          if (!enabledIndicators.includes(indicator)) {
            enabledIndicators.push(indicator);
          }
        });
      }

      console.log('[IndicatorMenuFixes] Enabled indicators:', enabledIndicators);

      // Update global enabled indicators
      if (window.enabledIndicators) {
        window.enabledIndicators.forEach(indicator => {
          indicator.enabled = enabledIndicators.includes(indicator.name);
        });
      }

      // Save to localStorage
      localStorage.setItem('enabledIndicators', JSON.stringify(enabledIndicators));

      // Update Oracle Matrix - try multiple methods
      const updateMethods = [
        () => window.updateSignalMatrix?.(),
        () => window.updateOracleMatrix?.(),
        () => window.renderSignalMatrix?.(),
        () => window.refreshSignalMatrix?.()
      ];

      let matrixUpdated = false;
      updateMethods.forEach(method => {
        try {
          if (method()) {
            matrixUpdated = true;
          }
        } catch (error) {
          console.warn('[IndicatorMenuFixes] Matrix update method failed:', error);
        }
      });

      // Update signal lights
      const lightUpdateMethods = [
        () => window.updateAllSignalLights?.(),
        () => window.refreshSignalLights?.(),
        () => window.renderSignalLights?.()
      ];

      lightUpdateMethods.forEach(method => {
        try {
          method();
        } catch (error) {
          console.warn('[IndicatorMenuFixes] Light update method failed:', error);
        }
      });

      // Update indicator tables/rows
      const tableUpdateMethods = [
        () => window.renderIndicatorTables?.(),
        () => window.updateIndicatorRows?.(),
        () => window.refreshIndicatorDisplay?.()
      ];

      tableUpdateMethods.forEach(method => {
        try {
          method();
        } catch (error) {
          console.warn('[IndicatorMenuFixes] Table update method failed:', error);
        }
      });

      // Force a complete UI refresh
      setTimeout(() => {
        // Trigger a resize event to refresh layouts
        window.dispatchEvent(new Event('resize'));
        
        // Trigger custom events
        window.dispatchEvent(new CustomEvent('indicatorsUpdated', { 
          detail: { enabledIndicators } 
        }));
      }, 100);

      this.showNotification(`✅ Applied ${enabledIndicators.length} indicators to Oracle Matrix`, 'success');
      console.log('[IndicatorMenuFixes] Successfully applied indicator changes');

    } catch (error) {
      console.error('[IndicatorMenuFixes] Error applying indicator changes:', error);
      this.showNotification('❌ Error applying indicator changes', 'error');
    } finally {
      setTimeout(() => {
        this.isApplying = false;
      }, 1000);
    }
  }

  enhanceIndicatorToggles() {
    // Add real-time feedback to indicator toggles
    document.addEventListener('change', (e) => {
      if (e.target.matches('.indicator-toggle, input[data-indicator]')) {
        const indicator = e.target.dataset.indicator || e.target.dataset.name;
        const enabled = e.target.checked;
        
        // Visual feedback
        const item = e.target.closest('.indicator-item, .indicator-row');
        if (item) {
          item.classList.toggle('enabled', enabled);
          item.classList.add('changed');
          
          setTimeout(() => {
            item.classList.remove('changed');
          }, 300);
        }

        console.log(`[IndicatorMenuFixes] Indicator ${indicator} ${enabled ? 'enabled' : 'disabled'}`);
      }
    });
  }

  showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.indicator-menu-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Create notification
    const notification = document.createElement('div');
    notification.className = `indicator-menu-notification ${type}`;
    notification.textContent = message;

    // Style notification
    Object.assign(notification.style, {
      position: 'fixed',
      top: '20px',
      right: '20px',
      padding: '12px 20px',
      borderRadius: '6px',
      color: '#ffffff',
      fontWeight: 'bold',
      zIndex: '10000',
      opacity: '0',
      transform: 'translateX(100%)',
      transition: 'all 0.3s ease',
      backgroundColor: type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3',
      border: `2px solid ${type === 'success' ? '#45a049' : type === 'error' ? '#da190b' : '#0b7dda'}`
    });

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.style.opacity = '1';
      notification.style.transform = 'translateX(0)';
    }, 10);

    // Auto remove
    setTimeout(() => {
      notification.style.opacity = '0';
      notification.style.transform = 'translateX(100%)';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.remove();
        }
      }, 300);
    }, 3000);
  }

  addMenuStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .reset-to-default-btn {
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        border: 1px solid #ff6b6b;
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        font-weight: bold;
        display: flex;
        align-items: center;
        gap: 6px;
        transition: all 0.3s ease;
        margin-right: 8px;
      }
      
      .reset-to-default-btn:hover {
        background: linear-gradient(135deg, #ee5a24, #ff6b6b);
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(255, 107, 107, 0.3);
      }
      
      .reset-to-default-btn:active {
        transform: translateY(0);
      }
      
      .indicator-item.changed,
      .indicator-row.changed {
        background: rgba(0, 255, 255, 0.1);
        border-left: 3px solid #00ffff;
        transition: all 0.3s ease;
      }
      
      .indicator-item.enabled,
      .indicator-row.enabled {
        background: rgba(76, 175, 80, 0.1);
        border-left: 3px solid #4CAF50;
      }
      
      .menu-actions {
        display: flex;
        gap: 8px;
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #303045;
        flex-wrap: wrap;
      }
      
      .action-button {
        flex: 1;
        min-width: 100px;
      }
    `;
    document.head.appendChild(style);
  }
}

// Initialize indicator menu fixes
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.indicatorMenuFixes = new IndicatorMenuFixes();
  }, 2000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = IndicatorMenuFixes;
}
