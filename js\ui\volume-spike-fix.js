/**
 * Volume Spike Fix - Only Change When Spike Level Changes
 */

class VolumeSpikefix {
  constructor() {
    this.lastSpikeStates = new Map(); // Track last spike state for each timeframe
    this.spikeThresholds = {
      low: 1.2,    // 20% above average
      medium: 1.5, // 50% above average
      high: 2.0,   // 100% above average
      extreme: 3.0 // 300% above average
    };
    this.init();
  }

  init() {
    console.log('[VolumeSpikefix] 🔧 Fixing volume spike indicators...');
    
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => this.setupVolumeSpikeTracking());
    } else {
      this.setupVolumeSpikeTracking();
    }
  }

  setupVolumeSpikeTracking() {
    // Override volume update functions
    this.overrideVolumeUpdates();
    
    // Set up periodic volume monitoring
    this.startVolumeMonitoring();
    
    console.log('[VolumeSpikefix] ✅ Volume spike tracking setup complete');
  }

  overrideVolumeUpdates() {
    // Override the volume status update function
    const originalUpdateVolumeStatus = window.updateVolumeStatus;
    if (originalUpdateVolumeStatus) {
      window.updateVolumeStatus = (timeframe, volumeData) => {
        const spikeLevel = this.calculateSpikeLevel(volumeData);
        const lastSpikeLevel = this.lastSpikeStates.get(timeframe);
        
        // Only update if spike level has actually changed
        if (spikeLevel !== lastSpikeLevel) {
          console.log(`[VolumeSpikefix] Volume spike level changed for ${timeframe}: ${lastSpikeLevel} -> ${spikeLevel}`);
          this.lastSpikeStates.set(timeframe, spikeLevel);
          
          // Update the visual indicator
          this.updateVolumeSpikeVisual(timeframe, spikeLevel, volumeData);
          
          // Call original function
          originalUpdateVolumeStatus(timeframe, volumeData);
        }
      };
    }

    // Override volume indicator update
    const originalUpdateVolumeIndicator = window.updateVolumeIndicator;
    if (originalUpdateVolumeIndicator) {
      window.updateVolumeIndicator = (timeframe, volume, avgVolume) => {
        const volumeData = { current: volume, average: avgVolume };
        const spikeLevel = this.calculateSpikeLevel(volumeData);
        const lastSpikeLevel = this.lastSpikeStates.get(timeframe);
        
        if (spikeLevel !== lastSpikeLevel) {
          this.lastSpikeStates.set(timeframe, spikeLevel);
          this.updateVolumeSpikeVisual(timeframe, spikeLevel, volumeData);
          originalUpdateVolumeIndicator(timeframe, volume, avgVolume);
        }
      };
    }
  }

  calculateSpikeLevel(volumeData) {
    if (!volumeData || !volumeData.current || !volumeData.average) {
      return 'none';
    }

    const ratio = volumeData.current / volumeData.average;

    if (ratio >= this.spikeThresholds.extreme) return 'extreme';
    if (ratio >= this.spikeThresholds.high) return 'high';
    if (ratio >= this.spikeThresholds.medium) return 'medium';
    if (ratio >= this.spikeThresholds.low) return 'low';
    
    return 'none';
  }

  updateVolumeSpikeVisual(timeframe, spikeLevel, volumeData) {
    // Find volume spike indicators for this timeframe
    const volumeIndicators = document.querySelectorAll(`[data-timeframe="${timeframe}"][data-indicator="volume"], .volume-spike-${timeframe}, #volume-${timeframe}`);
    
    volumeIndicators.forEach(indicator => {
      this.applySpikeStyling(indicator, spikeLevel, volumeData);
    });

    // Update volume blips/dots
    const volumeBlips = document.querySelectorAll(`.volume-blip-${timeframe}, .volume-dot-${timeframe}`);
    volumeBlips.forEach(blip => {
      this.updateVolumeBlip(blip, spikeLevel, volumeData);
    });
  }

  applySpikeStyling(indicator, spikeLevel, volumeData) {
    // Remove existing spike classes
    indicator.classList.remove('volume-spike-none', 'volume-spike-low', 'volume-spike-medium', 'volume-spike-high', 'volume-spike-extreme');
    
    // Add new spike class
    indicator.classList.add(`volume-spike-${spikeLevel}`);
    
    // Update visual appearance based on spike level
    const styling = this.getSpikeStyling(spikeLevel);
    Object.assign(indicator.style, styling);
    
    // Update tooltip
    const ratio = volumeData.current / volumeData.average;
    indicator.title = `Volume Spike: ${spikeLevel.toUpperCase()} (${ratio.toFixed(2)}x average)`;
  }

  getSpikeStyling(spikeLevel) {
    const stylings = {
      'none': {
        backgroundColor: '#404040',
        border: '1px solid #606060',
        boxShadow: 'none'
      },
      'low': {
        backgroundColor: '#4a90e2',
        border: '1px solid #357abd',
        boxShadow: '0 0 5px rgba(74, 144, 226, 0.5)'
      },
      'medium': {
        backgroundColor: '#f5a623',
        border: '1px solid #d68910',
        boxShadow: '0 0 8px rgba(245, 166, 35, 0.6)'
      },
      'high': {
        backgroundColor: '#e74c3c',
        border: '1px solid #c0392b',
        boxShadow: '0 0 10px rgba(231, 76, 60, 0.7)'
      },
      'extreme': {
        backgroundColor: '#9b59b6',
        border: '1px solid #8e44ad',
        boxShadow: '0 0 15px rgba(155, 89, 182, 0.8)',
        animation: 'pulse 1s infinite'
      }
    };

    return stylings[spikeLevel] || stylings['none'];
  }

  updateVolumeBlip(blip, spikeLevel, volumeData) {
    // Only update if the blip exists and spike level changed
    if (!blip) return;

    // Remove existing classes
    blip.classList.remove('blip-none', 'blip-low', 'blip-medium', 'blip-high', 'blip-extreme');
    
    // Add new class
    blip.classList.add(`blip-${spikeLevel}`);
    
    // Update blip appearance
    const blipStyling = this.getBlipStyling(spikeLevel);
    Object.assign(blip.style, blipStyling);
  }

  getBlipStyling(spikeLevel) {
    const blipStylings = {
      'none': {
        opacity: '0.3',
        transform: 'scale(1)',
        backgroundColor: '#666666'
      },
      'low': {
        opacity: '0.6',
        transform: 'scale(1.1)',
        backgroundColor: '#4a90e2'
      },
      'medium': {
        opacity: '0.8',
        transform: 'scale(1.3)',
        backgroundColor: '#f5a623'
      },
      'high': {
        opacity: '1',
        transform: 'scale(1.5)',
        backgroundColor: '#e74c3c'
      },
      'extreme': {
        opacity: '1',
        transform: 'scale(1.8)',
        backgroundColor: '#9b59b6',
        animation: 'blip-pulse 0.5s infinite alternate'
      }
    };

    return blipStylings[spikeLevel] || blipStylings['none'];
  }

  startVolumeMonitoring() {
    // Monitor volume changes every 5 seconds
    setInterval(() => {
      this.checkVolumeChanges();
    }, 5000);
  }

  checkVolumeChanges() {
    // Check if volume data is available
    if (!window.indicatorsData) return;

    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
    
    timeframes.forEach(timeframe => {
      const tfData = window.indicatorsData[timeframe];
      if (tfData && tfData.volume) {
        const volumeData = {
          current: tfData.volume.current || tfData.volume.value,
          average: tfData.volume.average || tfData.volume.avg
        };
        
        if (volumeData.current && volumeData.average) {
          const spikeLevel = this.calculateSpikeLevel(volumeData);
          const lastSpikeLevel = this.lastSpikeStates.get(timeframe);
          
          if (spikeLevel !== lastSpikeLevel) {
            console.log(`[VolumeSpikefix] Detected volume spike change for ${timeframe}: ${lastSpikeLevel} -> ${spikeLevel}`);
            this.lastSpikeStates.set(timeframe, spikeLevel);
            this.updateVolumeSpikeVisual(timeframe, spikeLevel, volumeData);
          }
        }
      }
    });
  }

  addVolumeSpikeCSS() {
    const style = document.createElement('style');
    style.id = 'volume-spike-fix-css';
    style.textContent = `
      /* Volume Spike Animations */
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.7; }
        100% { opacity: 1; }
      }

      @keyframes blip-pulse {
        0% { transform: scale(1.8); opacity: 1; }
        100% { transform: scale(2.2); opacity: 0.8; }
      }

      /* Volume Spike Classes */
      .volume-spike-none {
        transition: all 0.3s ease;
      }

      .volume-spike-low {
        transition: all 0.3s ease;
      }

      .volume-spike-medium {
        transition: all 0.3s ease;
      }

      .volume-spike-high {
        transition: all 0.3s ease;
      }

      .volume-spike-extreme {
        transition: all 0.3s ease;
      }

      /* Volume Blip Classes */
      .blip-none, .blip-low, .blip-medium, .blip-high, .blip-extreme {
        transition: all 0.3s ease;
        border-radius: 50%;
        width: 8px;
        height: 8px;
        display: inline-block;
        margin: 2px;
      }

      /* Volume indicator containers */
      .volume-indicator-container {
        display: flex;
        align-items: center;
        gap: 5px;
      }

      .volume-spike-indicator {
        min-width: 12px;
        min-height: 12px;
        border-radius: 3px;
        transition: all 0.3s ease;
      }
    `;

    // Remove existing style if present
    const existingStyle = document.getElementById('volume-spike-fix-css');
    if (existingStyle) existingStyle.remove();

    document.head.appendChild(style);
  }

  // Public method to manually trigger volume spike check
  triggerVolumeCheck(timeframe, volumeData) {
    const spikeLevel = this.calculateSpikeLevel(volumeData);
    const lastSpikeLevel = this.lastSpikeStates.get(timeframe);
    
    if (spikeLevel !== lastSpikeLevel) {
      this.lastSpikeStates.set(timeframe, spikeLevel);
      this.updateVolumeSpikeVisual(timeframe, spikeLevel, volumeData);
      return true; // Spike level changed
    }
    
    return false; // No change
  }

  // Get current spike level for a timeframe
  getCurrentSpikeLevel(timeframe) {
    return this.lastSpikeStates.get(timeframe) || 'none';
  }

  // Reset all spike states
  resetSpikeStates() {
    this.lastSpikeStates.clear();
    console.log('[VolumeSpikefix] All spike states reset');
  }
}

// Initialize the volume spike fix
document.addEventListener('DOMContentLoaded', () => {
  window.volumeSpikefix = new VolumeSpikefix();
  window.volumeSpikefix.addVolumeSpikeCSS();
});

// Also initialize if DOM is already loaded
if (document.readyState !== 'loading') {
  window.volumeSpikefix = new VolumeSpikefix();
  window.volumeSpikefix.addVolumeSpikeCSS();
}
