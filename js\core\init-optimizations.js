/**
 * Initialization script for StarCrypt optimizations
 * Loads and initializes all optimization modules in the correct order
 */

(function() {
  'use strict';

  // Configuration for initialization
  const INIT_CONFIG = {
    loadTimeout: 10000, // 10 seconds timeout for module loading
    retryAttempts: 3,
    retryDelay: 1000, // 1 second between retries
    modules: [
      'eventManager',
      'performanceOptimizer', 
      'codeOptimizer'
    ]
  };

  // Track initialization state
  const initState = {
    modulesLoaded: new Set(),
    modulesFailed: new Set(),
    isInitializing: false,
    startTime: null,
    attempts: 0
  };

  /**
   * Main initialization function
   */
  async function initializeOptimizations() {
    if (initState.isInitializing) {
      console.log('[InitOptimizations] Already initializing...');
      return;
    }

    initState.isInitializing = true;
    initState.startTime = Date.now();
    initState.attempts++;

    console.log(`[InitOptimizations] Starting optimization initialization (attempt ${initState.attempts})...`);

    try {
      // Wait for DOM to be ready
      await waitForDOM();
      
      // Load core optimization modules
      await loadOptimizationModules();
      
      // Initialize modules in order
      await initializeModules();
      
      // Apply initial optimizations
      await applyInitialOptimizations();
      
      // Setup monitoring
      setupPerformanceMonitoring();
      
      // Mark as complete
      const duration = Date.now() - initState.startTime;
      console.log(`[InitOptimizations] Optimization initialization complete in ${duration}ms`);
      
      // Dispatch completion event
      window.dispatchEvent(new CustomEvent('optimizationsReady', {
        detail: {
          duration,
          modulesLoaded: Array.from(initState.modulesLoaded),
          modulesFailed: Array.from(initState.modulesFailed)
        }
      }));

    } catch (error) {
      console.error('[InitOptimizations] Initialization failed:', error);
      
      // Retry if we haven't exceeded max attempts
      if (initState.attempts < INIT_CONFIG.retryAttempts) {
        console.log(`[InitOptimizations] Retrying in ${INIT_CONFIG.retryDelay}ms...`);
        setTimeout(() => {
          initState.isInitializing = false;
          initializeOptimizations();
        }, INIT_CONFIG.retryDelay);
      } else {
        console.error('[InitOptimizations] Max retry attempts exceeded');
        initState.isInitializing = false;
      }
    }
  }

  /**
   * Wait for DOM to be ready
   */
  function waitForDOM() {
    return new Promise((resolve) => {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', resolve);
      } else {
        resolve();
      }
    });
  }

  /**
   * Load optimization modules
   */
  async function loadOptimizationModules() {
    console.log('[InitOptimizations] Loading optimization modules...');
    
    const moduleChecks = INIT_CONFIG.modules.map(moduleName => {
      return waitForModule(moduleName, INIT_CONFIG.loadTimeout);
    });

    const results = await Promise.allSettled(moduleChecks);
    
    results.forEach((result, index) => {
      const moduleName = INIT_CONFIG.modules[index];
      if (result.status === 'fulfilled') {
        initState.modulesLoaded.add(moduleName);
        console.log(`[InitOptimizations] Module loaded: ${moduleName}`);
      } else {
        initState.modulesFailed.add(moduleName);
        console.warn(`[InitOptimizations] Module failed to load: ${moduleName}`, result.reason);
      }
    });
  }

  /**
   * Wait for a specific module to be available
   */
  function waitForModule(moduleName, timeout) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      function checkModule() {
        if (window[moduleName]) {
          resolve(window[moduleName]);
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          reject(new Error(`Module ${moduleName} failed to load within ${timeout}ms`));
          return;
        }
        
        setTimeout(checkModule, 100);
      }
      
      checkModule();
    });
  }

  /**
   * Initialize modules in the correct order
   */
  async function initializeModules() {
    console.log('[InitOptimizations] Initializing modules...');
    
    // Initialize Event Manager first
    if (initState.modulesLoaded.has('eventManager') && window.eventManager) {
      try {
        // Event manager is already initialized in its constructor
        console.log('[InitOptimizations] Event Manager ready');
      } catch (error) {
        console.error('[InitOptimizations] Event Manager initialization failed:', error);
      }
    }
    
    // Initialize Performance Optimizer
    if (initState.modulesLoaded.has('performanceOptimizer') && window.performanceOptimizer) {
      try {
        // Performance optimizer is already initialized in its constructor
        console.log('[InitOptimizations] Performance Optimizer ready');
      } catch (error) {
        console.error('[InitOptimizations] Performance Optimizer initialization failed:', error);
      }
    }
    
    // Initialize Code Optimizer
    if (initState.modulesLoaded.has('codeOptimizer') && window.codeOptimizer) {
      try {
        await window.codeOptimizer.initialize();
        console.log('[InitOptimizations] Code Optimizer initialized');
      } catch (error) {
        console.error('[InitOptimizations] Code Optimizer initialization failed:', error);
      }
    }
  }

  /**
   * Apply initial optimizations
   */
  async function applyInitialOptimizations() {
    console.log('[InitOptimizations] Applying initial optimizations...');
    
    try {
      // Clean up any existing redundancies
      if (window.codeOptimizer) {
        // Code optimizer already runs cleanup during initialization
      }
      
      // Optimize existing event listeners
      if (window.eventManager) {
        const stats = window.eventManager.getStats();
        console.log('[InitOptimizations] Current event listener stats:', stats);
      }
      
      // Start performance monitoring
      if (window.performanceOptimizer) {
        window.performanceOptimizer.startMemoryCleanup();
      }
      
    } catch (error) {
      console.error('[InitOptimizations] Error applying initial optimizations:', error);
    }
  }

  /**
   * Setup performance monitoring
   */
  function setupPerformanceMonitoring() {
    console.log('[InitOptimizations] Setting up performance monitoring...');
    
    // Monitor memory usage
    if (performance.memory) {
      setInterval(() => {
        const memoryInfo = {
          used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
          total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
          limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        };
        
        // Log if memory usage is high
        if (memoryInfo.used > 100) { // 100MB threshold
          console.warn('[InitOptimizations] High memory usage detected:', memoryInfo);
        }
      }, 30000); // Check every 30 seconds
    }
    
    // Monitor performance metrics
    if (window.performanceOptimizer) {
      setInterval(() => {
        const stats = window.performanceOptimizer.getStats();
        if (stats.queueSizes && Object.values(stats.queueSizes).some(size => size > 50)) {
          console.warn('[InitOptimizations] Large update queues detected:', stats.queueSizes);
        }
      }, 60000); // Check every minute
    }
  }

  /**
   * Get initialization status
   */
  function getInitializationStatus() {
    return {
      isInitializing: initState.isInitializing,
      modulesLoaded: Array.from(initState.modulesLoaded),
      modulesFailed: Array.from(initState.modulesFailed),
      attempts: initState.attempts,
      startTime: initState.startTime,
      duration: initState.startTime ? Date.now() - initState.startTime : null
    };
  }

  /**
   * Force re-initialization
   */
  function reinitialize() {
    console.log('[InitOptimizations] Forcing re-initialization...');
    initState.isInitializing = false;
    initState.modulesLoaded.clear();
    initState.modulesFailed.clear();
    initializeOptimizations();
  }

  // Expose global functions
  window.initializeOptimizations = initializeOptimizations;
  window.getOptimizationStatus = getInitializationStatus;
  window.reinitializeOptimizations = reinitialize;

  // Auto-start initialization
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeOptimizations);
  } else {
    // DOM already loaded, start immediately
    setTimeout(initializeOptimizations, 0);
  }

  // Handle page visibility changes
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden && window.performanceOptimizer) {
      // Page became visible, check if optimization is needed
      const stats = window.performanceOptimizer.getStats();
      if (stats.queueSizes && Object.values(stats.queueSizes).some(size => size > 100)) {
        console.log('[InitOptimizations] Large queues detected after visibility change, optimizing...');
        window.performanceOptimizer.cleanupMemory();
      }
    }
  });

  console.log('[InitOptimizations] Optimization initialization script loaded');
})();
