/**
 * NotificationManager - Centralized notification system
 * Handles all toast notifications in the application
 */

class NotificationManager {
  constructor() {
    // Singleton pattern
    if (NotificationManager.instance) {
      return NotificationManager.instance;
    }
    NotificationManager.instance = this;

    // Create toast container if it doesn't exist
    this.container = document.getElementById('toast-container');
    if (!this.container) {
      this.container = document.createElement('div');
      this.container.id = 'toast-container';
      this.container.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 10000;
        display: flex;
        flex-direction: column;
        gap: 10px;
        max-width: 350px;
        pointer-events: none;
      `;
      
      // Function to safely append container
      const tryAppendContainer = () => {
        try {
          if (document.body) {
            document.body.appendChild(this.container);
            return true;
          }
          return false;
        } catch (e) {
          console.warn('[NotificationManager] Error appending container:', e);
          return false;
        }
      };
      
      // Try to append immediately
      if (!tryAppendContainer()) {
        // If that fails, wait for DOMContentLoaded
        const onDOMContentLoaded = () => {
          if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', onDOMContentLoaded, { once: true });
          } else {
            tryAppendContainer();
          }
        };
        onDOMContentLoaded();
      }
    }

    // Track active toasts to prevent duplicates
    this.activeToasts = new Map();
    this.toastId = 0;
  }

  /**
   * Show a toast notification
   * @param {string} message - The message to display
   * @param {Object} options - Notification options
   * @param {string} [options.type='info'] - Type of notification (info, success, warning, error)
   * @param {number} [options.duration=5000] - Duration in milliseconds
   * @param {string} [options.id] - Optional ID to prevent duplicates
   * @returns {string} The toast ID
   */
  show(message, { type = 'info', duration = 5000, id } = {}) {
    // Generate an ID if not provided
    const toastId = id || `toast-${Date.now()}-${this.toastId++}`;
    
    // Remove any existing toast with the same ID
    this.hide(toastId);

    // Create toast element
    const toast = document.createElement('div');
    toast.id = toastId;
    toast.className = `toast-notification toast-${type}`;
    toast.style.cssText = `
      padding: 12px 20px;
      border-radius: 4px;
      background: ${this.getBackgroundColor(type)};
      color: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      opacity: 0;
      transform: translateX(100px);
      transition: opacity 0.3s ease, transform 0.3s ease;
      pointer-events: auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 15px;
    `;

    // Add message
    const messageEl = document.createElement('div');
    messageEl.textContent = message;
    messageEl.style.flex = '1';
    toast.appendChild(messageEl);

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.innerHTML = '&times;';
    closeButton.style.cssText = `
      background: none;
      border: none;
      color: white;
      font-size: 1.2em;
      cursor: pointer;
      padding: 0;
      margin: 0;
      line-height: 1;
      opacity: 0.7;
      transition: opacity 0.2s;
    `;
    closeButton.addEventListener('mouseenter', () => {
      closeButton.style.opacity = '1';
    });
    closeButton.addEventListener('mouseleave', () => {
      closeButton.style.opacity = '0.7';
    });
    closeButton.addEventListener('click', () => {
      this.hide(toastId);
    });
    toast.appendChild(closeButton);

    // Add to container
    this.container.insertBefore(toast, this.container.firstChild);
    this.activeToasts.set(toastId, {
      element: toast,
      timeout: null
    });

    // Trigger animation
    requestAnimationFrame(() => {
      toast.style.opacity = '1';
      toast.style.transform = 'translateX(0)';
    });

    // Auto-dismiss
    if (duration > 0) {
      const timeout = setTimeout(() => {
        this.hide(toastId);
      }, duration);
      this.activeToasts.get(toastId).timeout = timeout;
    }

    return toastId;
  }

  /**
   * Hide a toast notification
   * @param {string} id - The ID of the toast to hide
   */
  hide(id) {
    const toastData = this.activeToasts.get(id);
    if (!toastData) return;

    // Clear any pending timeout
    if (toastData.timeout) {
      clearTimeout(toastData.timeout);
    }

    // Animate out
    const toast = toastData.element;
    toast.style.opacity = '0';
    toast.style.transform = 'translateX(100px)';

    // Remove from DOM after animation
    setTimeout(() => {
      if (toast.parentNode === this.container) {
        this.container.removeChild(toast);
      }
      this.activeToasts.delete(id);
    }, 300);
  }

  /**
   * Hide all toast notifications
   */
  hideAll() {
    for (const id of this.activeToasts.keys()) {
      this.hide(id);
    }
  }

  /**
   * Get background color based on notification type
   * @private
   */
  getBackgroundColor(type) {
    const colors = {
      info: '#3498db',
      success: '#2ecc71',
      warning: '#f39c12',
      error: '#e74c3c'
    };
    return colors[type] || colors.info;
  }
}

// Initialize and expose singleton instance
const notificationManager = new NotificationManager();
window.NotificationManager = notificationManager;

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    // This ensures the container is created when DOM is ready
    if (notificationManager.container && !document.body.contains(notificationManager.container)) {
      document.body.appendChild(notificationManager.container);
    }
  }, { once: true });
}
