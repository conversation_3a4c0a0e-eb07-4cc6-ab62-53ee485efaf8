/**
 * StarCrypt System Validation Script
 * Tests the new unified system for functionality and performance
 */

const fs = require('fs');
const path = require('path');

// Validation results
const results = {
  coreModules: {},
  fileStructure: {},
  dependencies: {},
  conflicts: {},
  performance: {},
  errors: []
};

/**
 * Validate core module files exist and are properly structured
 */
function validateCoreModules() {
  console.log('🔍 Validating core modules...');
  
  const coreModules = [
    'js/core/ConfigCore.js',
    'js/core/WebSocketCore.js', 
    'js/core/StrategyCore.js',
    'js/core/SignalCore.js',
    'js/core/StarCryptCore.js'
  ];

  for (const modulePath of coreModules) {
    const fullPath = path.join(__dirname, modulePath);
    const moduleName = path.basename(modulePath, '.js');
    
    try {
      if (fs.existsSync(fullPath)) {
        const content = fs.readFileSync(fullPath, 'utf8');
        
        // Check for class definition
        const hasClass = content.includes(`class ${moduleName.replace('Core', '')}`) || 
                        content.includes(`class StarCrypt${moduleName}`);
        
        // Check for singleton pattern
        const hasSingleton = content.includes('let ') && content.includes('Instance = null');
        
        // Check for proper exports
        const hasExports = content.includes('module.exports') || content.includes('window.');
        
        results.coreModules[moduleName] = {
          exists: true,
          hasClass,
          hasSingleton,
          hasExports,
          size: content.length,
          valid: hasClass && hasSingleton && hasExports
        };
        
        console.log(`  ✓ ${moduleName}: ${results.coreModules[moduleName].valid ? 'Valid' : 'Issues detected'}`);
      } else {
        results.coreModules[moduleName] = { exists: false, valid: false };
        console.log(`  ✗ ${moduleName}: File not found`);
      }
    } catch (error) {
      results.coreModules[moduleName] = { exists: false, valid: false, error: error.message };
      console.log(`  ✗ ${moduleName}: Error - ${error.message}`);
    }
  }
}

/**
 * Validate file structure and organization
 */
function validateFileStructure() {
  console.log('\n📁 Validating file structure...');
  
  const expectedStructure = {
    'js/core': ['ConfigCore.js', 'WebSocketCore.js', 'StrategyCore.js', 'SignalCore.js', 'StarCryptCore.js'],
    'js/ui': ['theme-manager.js', 'signal-lights.js', 'timeframe-ui.js'],
    'js/services': ['AIIntegrationService.js'],
    'js/ml': ['prediction-handler.js'],
    'css': ['main.css', 'theme.css', 'signal-lights.css'],
    'docs': ['CODEBASE_ANALYSIS_REPORT.md', 'CLEANUP_REPORT.md']
  };

  for (const [directory, expectedFiles] of Object.entries(expectedStructure)) {
    const dirPath = path.join(__dirname, directory);
    
    if (fs.existsSync(dirPath)) {
      const actualFiles = fs.readdirSync(dirPath).filter(f => f.endsWith('.js') || f.endsWith('.css') || f.endsWith('.md'));
      const missingFiles = expectedFiles.filter(f => !actualFiles.includes(f));
      const extraFiles = actualFiles.filter(f => !expectedFiles.includes(f) && !f.includes('.disabled'));
      
      results.fileStructure[directory] = {
        exists: true,
        expectedFiles: expectedFiles.length,
        actualFiles: actualFiles.length,
        missingFiles,
        extraFiles,
        valid: missingFiles.length === 0
      };
      
      console.log(`  ${results.fileStructure[directory].valid ? '✓' : '⚠'} ${directory}: ${actualFiles.length}/${expectedFiles.length} files`);
      
      if (missingFiles.length > 0) {
        console.log(`    Missing: ${missingFiles.join(', ')}`);
      }
    } else {
      results.fileStructure[directory] = { exists: false, valid: false };
      console.log(`  ✗ ${directory}: Directory not found`);
    }
  }
}

/**
 * Check for remaining conflicts and duplicate files
 */
function validateNoConflicts() {
  console.log('\n🔍 Checking for conflicts...');
  
  const conflictPatterns = [
    { pattern: /websocket.*init/i, description: 'WebSocket initialization conflicts' },
    { pattern: /strategy.*manager/i, description: 'Strategy manager conflicts' },
    { pattern: /signal.*processor/i, description: 'Signal processor conflicts' },
    { pattern: /\.bak$|\.old$|\.temp$/, description: 'Backup files' },
    { pattern: /\.disabled$/, description: 'Disabled files' }
  ];

  function scanDirectory(dir, relativePath = '') {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const itemPath = path.join(dir, item);
      const relativeItemPath = path.join(relativePath, item);
      
      if (fs.statSync(itemPath).isDirectory()) {
        // Skip backup directories
        if (item === 'Backups' || item === 'node_modules') continue;
        scanDirectory(itemPath, relativeItemPath);
      } else {
        // Check for conflict patterns
        for (const { pattern, description } of conflictPatterns) {
          if (pattern.test(item)) {
            if (!results.conflicts[description]) {
              results.conflicts[description] = [];
            }
            results.conflicts[description].push(relativeItemPath);
          }
        }
      }
    }
  }

  scanDirectory(__dirname);

  const conflictCount = Object.values(results.conflicts).reduce((sum, arr) => sum + arr.length, 0);
  
  if (conflictCount === 0) {
    console.log('  ✓ No conflicts detected');
  } else {
    console.log(`  ⚠ ${conflictCount} potential conflicts found:`);
    for (const [description, files] of Object.entries(results.conflicts)) {
      console.log(`    ${description}: ${files.length} files`);
    }
  }
}

/**
 * Validate dependencies and imports
 */
function validateDependencies() {
  console.log('\n📦 Validating dependencies...');
  
  // Check package.json
  const packagePath = path.join(__dirname, 'package.json');
  if (fs.existsSync(packagePath)) {
    const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    results.dependencies.packageJson = {
      exists: true,
      dependencies: Object.keys(packageData.dependencies || {}).length,
      devDependencies: Object.keys(packageData.devDependencies || {}).length,
      scripts: Object.keys(packageData.scripts || {}).length
    };
    
    console.log(`  ✓ package.json: ${results.dependencies.packageJson.dependencies} deps, ${results.dependencies.packageJson.devDependencies} dev deps`);
  } else {
    results.dependencies.packageJson = { exists: false };
    console.log('  ✗ package.json: Not found');
  }

  // Check for circular dependencies in core modules
  const coreModules = ['ConfigCore', 'WebSocketCore', 'StrategyCore', 'SignalCore', 'StarCryptCore'];
  const imports = {};
  
  for (const module of coreModules) {
    const filePath = path.join(__dirname, 'js/core', `${module}.js`);
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      imports[module] = [];
      
      // Look for imports from other core modules
      for (const otherModule of coreModules) {
        if (module !== otherModule && content.includes(otherModule)) {
          imports[module].push(otherModule);
        }
      }
    }
  }
  
  results.dependencies.coreImports = imports;
  console.log('  ✓ Core module dependencies mapped');
}

/**
 * Basic performance validation
 */
function validatePerformance() {
  console.log('\n⚡ Validating performance characteristics...');
  
  // Check file sizes
  const coreFiles = [
    'js/core/ConfigCore.js',
    'js/core/WebSocketCore.js',
    'js/core/StrategyCore.js', 
    'js/core/SignalCore.js',
    'js/core/StarCryptCore.js',
    'js/init.js'
  ];

  let totalSize = 0;
  const fileSizes = {};

  for (const file of coreFiles) {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
      const stats = fs.statSync(filePath);
      fileSizes[file] = stats.size;
      totalSize += stats.size;
    }
  }

  results.performance = {
    totalCoreSize: totalSize,
    fileSizes,
    averageFileSize: totalSize / Object.keys(fileSizes).length,
    largestFile: Object.entries(fileSizes).reduce((max, [file, size]) => 
      size > max.size ? { file, size } : max, { file: '', size: 0 })
  };

  console.log(`  ✓ Total core size: ${(totalSize / 1024).toFixed(2)} KB`);
  console.log(`  ✓ Average file size: ${(results.performance.averageFileSize / 1024).toFixed(2)} KB`);
  console.log(`  ✓ Largest file: ${results.performance.largestFile.file} (${(results.performance.largestFile.size / 1024).toFixed(2)} KB)`);
}

/**
 * Generate validation report
 */
function generateValidationReport() {
  console.log('\n📊 Generating validation report...');
  
  const timestamp = new Date().toISOString();
  const totalIssues = results.errors.length + 
                     Object.values(results.conflicts).reduce((sum, arr) => sum + arr.length, 0) +
                     Object.values(results.coreModules).filter(m => !m.valid).length;

  const report = `# StarCrypt System Validation Report

Generated: ${timestamp}

## Summary

${totalIssues === 0 ? '✅ All validations passed successfully!' : `⚠️ ${totalIssues} issues detected that need attention.`}

## Core Modules Validation

${Object.entries(results.coreModules).map(([name, result]) => 
  `- **${name}**: ${result.valid ? '✅ Valid' : '❌ Issues'} ${result.size ? `(${(result.size/1024).toFixed(2)} KB)` : ''}`
).join('\n')}

## File Structure Validation

${Object.entries(results.fileStructure).map(([dir, result]) => 
  `- **${dir}**: ${result.valid ? '✅ Complete' : '⚠️ Issues'} (${result.actualFiles || 0}/${result.expectedFiles || 0} files)`
).join('\n')}

## Conflict Detection

${Object.keys(results.conflicts).length === 0 ? '✅ No conflicts detected' : 
  Object.entries(results.conflicts).map(([type, files]) => 
    `- **${type}**: ${files.length} files\n${files.map(f => `  - ${f}`).join('\n')}`
  ).join('\n\n')}

## Dependencies

- **Package.json**: ${results.dependencies.packageJson?.exists ? '✅ Found' : '❌ Missing'}
- **Dependencies**: ${results.dependencies.packageJson?.dependencies || 0}
- **Dev Dependencies**: ${results.dependencies.packageJson?.devDependencies || 0}

## Performance Metrics

- **Total Core Size**: ${(results.performance.totalCoreSize / 1024).toFixed(2)} KB
- **Average File Size**: ${(results.performance.averageFileSize / 1024).toFixed(2)} KB
- **Largest File**: ${results.performance.largestFile.file} (${(results.performance.largestFile.size / 1024).toFixed(2)} KB)

## Recommendations

${totalIssues === 0 ? 
  '🎉 The system is ready for production use!' : 
  '⚠️ Please address the issues identified above before deploying to production.'}

### Next Steps

1. **Test Functionality**: Run the application and test all features
2. **Performance Testing**: Monitor memory usage and response times
3. **Error Monitoring**: Check browser console for any runtime errors
4. **User Acceptance**: Verify all user-facing features work correctly

## Validation Details

\`\`\`json
${JSON.stringify(results, null, 2)}
\`\`\`
`;

  const reportPath = path.join(__dirname, 'VALIDATION_REPORT.md');
  fs.writeFileSync(reportPath, report);
  console.log(`✓ Validation report saved: ${reportPath}`);
}

/**
 * Main validation function
 */
function runValidation() {
  console.log('🔍 Starting StarCrypt system validation...\n');
  
  try {
    validateCoreModules();
    validateFileStructure();
    validateNoConflicts();
    validateDependencies();
    validatePerformance();
    generateValidationReport();
    
    const totalIssues = results.errors.length + 
                       Object.values(results.conflicts).reduce((sum, arr) => sum + arr.length, 0) +
                       Object.values(results.coreModules).filter(m => !m.valid).length;
    
    console.log('\n✅ Validation completed!');
    console.log(`📊 Summary: ${totalIssues === 0 ? 'All checks passed' : `${totalIssues} issues detected`}`);
    console.log('📋 See VALIDATION_REPORT.md for detailed results');
    
    return totalIssues === 0;
    
  } catch (error) {
    console.error('\n❌ Validation failed:', error);
    results.errors.push(error.message);
    return false;
  }
}

// Run validation if this script is executed directly
if (require.main === module) {
  const success = runValidation();
  process.exit(success ? 0 : 1);
}

module.exports = {
  runValidation,
  validateCoreModules,
  validateFileStructure,
  validateNoConflicts,
  validateDependencies,
  validatePerformance,
  results
};
