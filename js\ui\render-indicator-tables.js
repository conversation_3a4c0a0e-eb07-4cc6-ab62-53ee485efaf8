/**
 * Render Indicator Tables Module
 * Responsible for rendering indicator tables based on selected strategy
 */

(function() {
  'use strict';

  /**
   * Renders indicator tables based on the current strategy and enabled indicators
   * This function is critical for strategy switching to work correctly
   */
  function renderIndicatorTables() {
    try {
      // Get the momentum table container
      const momentumTable = document.getElementById('momentum-table');
      if (!momentumTable) {
        console.error('Momentum table container not found');
        return;
      }

      // Clear the table first
      momentumTable.innerHTML = '';

      // Get the current strategy's indicators
      const strategyIndicators = window.TRADING_STRATEGIES[window.currentStrategy]?.indicators || [];
      
      // Get all enabled indicators based on the current strategy
      const allIndicators = [
        ...(window.INDICATORS.momentum || []),
        ...(window.INDICATORS.trend || []),
        ...(window.INDICATORS.volume || []),
        ...(window.INDICATORS.ml || [])
      ];
      
      // Filter enabled indicators
      let enabledIndicators = [];
      
      // Special case for Admiral T.O.A. strategy which shows all indicators
      if (window.currentStrategy === 'admiral_toa') {
        enabledIndicators = allIndicators;
      } else {
        // For other strategies, only show indicators that are part of the strategy
        enabledIndicators = allIndicators.filter(ind => strategyIndicators.includes(ind));
      }

      // Log the indicators being rendered
      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Rendering ${enabledIndicators.length} indicators for ${window.TRADING_STRATEGIES[window.currentStrategy]?.name || 'unknown'} strategy`);
        if (typeof window.updateLogger === 'function') window.updateLogger();
      }

      // Create rows for each enabled indicator
      enabledIndicators.forEach(indicator => {
        // Create a new row for this indicator
        const row = document.createElement('div');
        row.className = 'signal-row';
        row.setAttribute('data-indicator', indicator);
        
        // Create the indicator name cell
        const nameCell = document.createElement('div');
        nameCell.className = 'signal-name';
        nameCell.textContent = indicator.toUpperCase();
        nameCell.style.color = typeof window.getIndicatorColor === 'function' ? 
          window.getIndicatorColor(indicator) : '#FFFFFF';
        
        // Add tooltip with indicator description
        if (typeof window.getIndicatorDescription === 'function') {
          nameCell.title = window.getIndicatorDescription(indicator);
        }
        
        row.appendChild(nameCell);
        
        // Create signal cells for each timeframe
        const timeframes = window.TIMEFRAMES || ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
        
        timeframes.forEach(tf => {
          const signalColumn = document.createElement('div');
          signalColumn.className = 'signal-column';
          signalColumn.setAttribute('data-timeframe', tf);
          
          // Create the signal light container
          const signalLight = document.createElement('div');
          signalLight.className = 'signal-light waiting';
          signalLight.id = `signal-${indicator}-${tf}`;
          signalLight.setAttribute('data-indicator', indicator);
          signalLight.setAttribute('data-timeframe', tf);
          
          // Add click handler if available
          if (typeof window.handleSignalClick === 'function') {
            signalLight.addEventListener('click', () => window.handleSignalClick(indicator, tf));
          }
          
          // Add waiting text
          signalLight.innerHTML = '<span class="waiting-text">waiting for data...</span>';
          
          signalColumn.appendChild(signalLight);
          row.appendChild(signalColumn);
        });
        
        // Add the row to the table
        momentumTable.appendChild(row);
      });
      
      // Ensure all indicators for this strategy are enabled in the checkbox settings
      if (window.enabledIndicators && Array.isArray(window.enabledIndicators)) {
        // Update the enabled indicators array to include all strategy indicators
        enabledIndicators.forEach(indicator => {
          const existingIndex = window.enabledIndicators.findIndex(ind => ind.name === indicator);
          if (existingIndex >= 0) {
            // Update existing entry
            window.enabledIndicators[existingIndex].enabled = true;
          } else {
            // Add new entry
            window.enabledIndicators.push({
              name: indicator,
              enabled: true
            });
          }
        });
        
        // Update checkboxes in the UI if they exist
        document.querySelectorAll('.indicator-checkbox').forEach(checkbox => {
          const indicator = checkbox.getAttribute('data-indicator');
          if (enabledIndicators.includes(indicator)) {
            checkbox.checked = true;
          }
        });
      }
      
      // After rendering, update all signal lights with current data immediately
      if (typeof window.updateAllSignalLights === 'function') {
        window.updateAllSignalLights(); // Remove timeout for instant update
      }
      
      return true;
    } catch (e) {
      console.error('Error rendering indicator tables:', e);
      if (window.logMessages) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Error rendering indicator tables: ${e.message}`);
        if (typeof window.updateLogger === 'function') window.updateLogger();
      }
      return false;
    }
  }

  // Make the function available globally
  window.renderIndicatorTables = renderIndicatorTables;
})();
