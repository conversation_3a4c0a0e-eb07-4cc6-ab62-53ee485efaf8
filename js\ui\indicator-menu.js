/**
 * Indicator <PERSON><PERSON>
 * Manages the indicator selection menu and related functionality
 */

(function() {
    'use strict';
    
    // State
    let enabledIndicators = [];
    let allIndicators = [];
    
    /**
     * Initialize the indicator menu
     */
    function initializeIndicatorMenu() {
        // Get existing indicator lists from global variables if available
        if (window.INDICATORS) {
            allIndicators = [
                ...window.INDICATORS.momentum || [],
                ...window.INDICATORS.trend || [],
                ...window.INDICATORS.volume || [],
                ...window.INDICATORS.volatility || [],
                ...window.INDICATORS.advanced || []
            ];
        }
        
        // Get enabled indicators from strategy
        if (window.currentStrategy && window.TRADING_STRATEGIES) {
            const strategy = window.TRADING_STRATEGIES[window.currentStrategy];
            if (strategy && strategy.indicators) {
                enabledIndicators = [...strategy.indicators];
            }
        }
        
        // Ensure container exists with proper ID that matches menu-controller expectations
        ensureMenuContainerExists();
        renderIndicatorMenu();
        attachIndicatorMenuEventHandlers();
        
        console.log('Indicator menu initialized with', enabledIndicators.length, 'enabled indicators');
    }
    
    /**
     * Ensures the indicator menu container exists
     */
    function ensureMenuContainerExists() {
        let container = document.getElementById('indicatorMenu');
        
        if (!container) {
            console.log('Creating indicator menu container with ID: indicatorMenu');
            container = document.createElement('div');
            container.id = 'indicatorMenu';
            container.className = 'menu-content';
            // Start hidden
            container.style.display = 'none';
            container.style.position = 'absolute';
            container.style.top = '100%';
            container.style.right = '0';
            container.style.width = '280px';
            container.style.backgroundColor = '#1a1a2e';
            container.style.border = '1px solid #303045';
            container.style.zIndex = '1000';
            container.style.padding = '10px';
            container.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';
            
            // Find the tickerContainer to append to
            const tickerContainer = document.getElementById('tickerContainer');
            if (tickerContainer) {
                tickerContainer.appendChild(container);
                console.log('Appended indicatorMenu container to tickerContainer');
            } else {
                // Fallback to body
                document.body.appendChild(container);
                console.log('Appended indicatorMenu container to body (fallback)');
            }
        }
        return container;
    }
    
    /**
     * Renders the indicator menu with checkboxes
     */
    function renderIndicatorMenu() {
        // Always ensure container exists
        const container = ensureMenuContainerExists();
        if (!container) {
            console.error('Failed to create indicator menu container');
            return;
        }
        
        // Group indicators by category
        const categories = {
            momentum: [],
            trend: [],
            volume: [],
            volatility: [],
            advanced: []
        };
        
        // Put indicators in their categories if window.INDICATORS exists
        if (window.INDICATORS) {
            Object.keys(window.INDICATORS).forEach(category => {
                if (window.INDICATORS[category] && Array.isArray(window.INDICATORS[category])) {
                    categories[category] = window.INDICATORS[category];
                }
            });
        }
        
        // Create HTML content
        let html = '<h3>Indicator Selection</h3>';
        html += '<div class="indicator-sections">';
        
        // Generate sections for each category
        Object.keys(categories).forEach(category => {
            if (categories[category].length > 0) {
                html += `
                    <div class="indicator-section">
                        <h4>${capitalize(category)} Indicators</h4>
                        <div class="indicator-group">
                `;
                
                // Add checkboxes for each indicator in this category
                categories[category].forEach(indicator => {
                    const isChecked = enabledIndicators.includes(indicator);
                    html += `
                        <div class="indicator-checkbox">
                            <label>
                                <input type="checkbox" class="indicator-toggle" 
                                       data-indicator="${indicator}" 
                                       ${isChecked ? 'checked' : ''}>
                                ${getIndicatorDisplayName(indicator)}
                            </label>
                            <span class="indicator-info" data-indicator="${indicator}">ⓘ</span>
                        </div>
                    `;
                });
                
                html += `
                        </div>
                    </div>
                `;
            }
        });
        
        html += '</div>';
        html += `
            <div class="indicator-actions">
                <button id="applyIndicatorsButton" class="apply-indicators-button">Apply Indicators</button>
                <button id="resetIndicatorsButton" class="reset-indicators-button">Reset to Default</button>
            </div>
        `;
        
        container.innerHTML = html;
    }
    
    /**
     * Attach event handlers to the indicator menu elements
     */
    function attachIndicatorMenuEventHandlers() {
        const container = document.getElementById('indicatorMenu');
        if (!container) return;
        
        // Toggle indicators when checkboxes are clicked
        const checkboxes = container.querySelectorAll('.indicator-toggle');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const indicator = this.dataset.indicator;
                if (this.checked) {
                    if (!enabledIndicators.includes(indicator)) {
                        enabledIndicators.push(indicator);
                    }
                } else {
                    enabledIndicators = enabledIndicators.filter(ind => ind !== indicator);
                }
            });
        });
        
        // Show indicator info tooltip
        const infoIcons = container.querySelectorAll('.indicator-info');
        infoIcons.forEach(icon => {
            icon.addEventListener('click', function() {
                const indicator = this.dataset.indicator;
                showIndicatorInfo(indicator);
            });
        });
        
        // Apply button handler
        const applyButton = document.getElementById('applyIndicatorsButton');
        if (applyButton) {
            applyButton.addEventListener('click', function() {
                applyIndicatorSelection();
            });
        }
        
        // Reset button handler
        const resetButton = document.getElementById('resetIndicatorsButton');
        if (resetButton) {
            resetButton.addEventListener('click', function() {
                resetIndicatorsToDefault();
            });
        }
    }
    
    /**
     * Apply the selected indicators
     */
    function applyIndicatorSelection() {
        console.log('Applying indicator selection:', enabledIndicators);
        
        // Save to local storage
        localStorage.setItem('enabledIndicators', JSON.stringify(enabledIndicators));
        
        // Update UI
        if (typeof updateEnabledIndicatorsForStrategy === 'function') {
            updateEnabledIndicatorsForStrategy(window.currentStrategy);
        }
        
        // Update signal matrix
        if (typeof updateSignalMatrix === 'function') {
            updateSignalMatrix();
        }
        
        // Notify user
        showNotification('Indicator selection applied');
    }
    
    /**
     * Reset indicators to default based on current strategy
     */
    function resetIndicatorsToDefault() {
        if (window.currentStrategy && window.TRADING_STRATEGIES) {
            const strategy = window.TRADING_STRATEGIES[window.currentStrategy];
            if (strategy && strategy.indicators) {
                enabledIndicators = [...strategy.indicators];
                
                // Update checkboxes to reflect reset
                const checkboxes = document.querySelectorAll('.indicator-toggle');
                checkboxes.forEach(checkbox => {
                    const indicator = checkbox.dataset.indicator;
                    checkbox.checked = enabledIndicators.includes(indicator);
                });
                
                showNotification('Indicators reset to strategy defaults');
            }
        }
    }
    
    /**
     * Show detailed information about an indicator
     */
    function showIndicatorInfo(indicator) {
        let description = getIndicatorDescription(indicator);
        let detailedInfo = getIndicatorDetailedInfo(indicator);
        
        const modal = document.createElement('div');
        modal.className = 'indicator-modal';
        modal.innerHTML = `
            <div class="indicator-modal-content">
                <span class="indicator-modal-close">&times;</span>
                <h3>${getIndicatorDisplayName(indicator)}</h3>
                <p class="indicator-description">${description}</p>
                <div class="indicator-details">${detailedInfo}</div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close button functionality
        const closeButton = modal.querySelector('.indicator-modal-close');
        closeButton.addEventListener('click', function() {
            modal.remove();
        });
        
        // Close when clicking outside the modal
        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.remove();
            }
        });
    }
    
    /**
     * Get a user-friendly display name for an indicator
     */
    function getIndicatorDisplayName(indicator) {
        const nameMap = {
            'rsi': 'RSI',
            'stochRsi': 'Stochastic RSI',
            'macd': 'MACD',
            'bollingerBands': 'Bollinger Bands',
            'adx': 'ADX',
            'atr': 'ATR',
            'williamsR': 'Williams %R',
            'ultimateOscillator': 'Ultimate Oscillator',
            'mfi': 'Money Flow Index',
            'vwap': 'VWAP',
            'volume': 'Volume',
            'fractal': 'Fractals',
            'ml': 'ML Predictor',
            'sentiment': 'Sentiment',
            'entropy': 'Entropy',
            'correlation': 'Correlation',
            'time_anomaly': 'Time Anomaly'
        };
        
        return nameMap[indicator] || indicator;
    }
    
    /**
     * Show a temporary notification
     */
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.className = 'system-notification';
        notification.textContent = message;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
    
    /**
     * Capitalize first letter of a string
     */
    function capitalize(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }
    
    /**
     * Get description for an indicator
     */
    function getIndicatorDescription(indicator) {
        // Use global function if available
        if (typeof window.getIndicatorDescription === 'function') {
            return window.getIndicatorDescription(indicator);
        }
        
        const descriptions = {
            'rsi': 'Relative Strength Index (0-100) signals overbought (>70) or oversold (<30) conditions. Use for potential reversals and divergence with price. Best in ranging markets.',
            'stochRsi': 'Stochastic RSI combines two momentum oscillators for earlier signals. Values >80 indicate overbought, <20 oversold. Look for crossovers and divergences for trade signals.',
            'macd': 'MACD shows trend direction, strength, and momentum. Signal line crossovers, zero line crossovers, and divergences provide trade signals. Histogram shows momentum strength.',
            'bollingerBands': 'Bollinger Bands show volatility and potential price targets. Price touching upper band may signal overbought, lower band oversold. Band width indicates volatility.',
            'adx': 'Average Directional Index (0-100) measures trend strength regardless of direction. Values >25 indicate strong trends where trend-following strategies work best.',
            'atr': 'Average True Range quantifies volatility in absolute price terms. Use for setting stop-loss levels, position sizing, and identifying volatility breakouts.',
            'williamsR': 'Williams %R (-100 to 0) identifies overbought (>-20) and oversold (<-80) conditions. Look for divergences and failure swings for potential reversals.',
            'ultimateOscillator': 'Ultimate Oscillator combines multiple timeframes (7, 14, 28) to reduce false signals. Values >70 are overbought, <30 oversold. Divergences signal potential reversals.',
            'mfi': 'Money Flow Index incorporates volume with price for stronger signals than price-only indicators. Values >80 indicate overbought, <20 oversold conditions.',
            'vwap': 'Volume Weighted Average Price serves as intraday support/resistance. Institutional traders use it as a benchmark - prices above VWAP attract buyers, below attract sellers.',
            'volume': 'Volume confirms price movements - rising prices with rising volume indicates strength. Volume spikes often mark exhaustion or reversal points. Low volume suggests weak trends.',
            'fractal': 'Fractals identify potential reversal points where price action forms specific patterns. Use with other indicators for confirmation of support/resistance levels.',
            'ml': 'Machine Learning Predictor analyzes historical patterns to forecast price direction. Higher confidence scores (>70%) suggest stronger signals. Always verify with other indicators.',
            'sentiment': 'Market sentiment analysis quantifies social media and news sentiment. Extreme readings often precede reversals. Contrarian opportunities exist at sentiment extremes.',
            'entropy': 'Entropy measures market randomness - high values indicate chaotic, unpredictable markets where trend strategies fail. Low values suggest orderly markets suitable for trend following.',
            'correlation': 'Inter-market correlation shows relationships between assets. High correlation (>0.7) suggests similar movement; negative correlation (<-0.7) indicates opposite movement. Use for diversification.',
            'time_anomaly': 'Time Anomaly Detector identifies unusual patterns that deviate from normal market behavior. Anomalies often precede significant market moves or volatility events.'
        };
        
        return descriptions[indicator] || 'No description available.';
    }
    
    /**
     * Get detailed technical information about an indicator
     */
    function getIndicatorDetailedInfo(indicator) {
        // Use global function if available
        if (typeof window.getIndicatorDetailedInfo === 'function') {
            return window.getIndicatorDetailedInfo(indicator);
        }
        
        const details = {
            'rsi': 'RSI is calculated using the formula: RSI = 100 - (100 / (1 + RS)), where RS = Average Gain / Average Loss over a specified period, typically 14 periods.',
            'macd': 'MACD is calculated by subtracting the 26-period EMA from the 12-period EMA. The 9-period EMA of the MACD is called the "signal line."',
            'bollingerBands': 'Bollinger Bands consist of a middle band (20-period SMA) and two outer bands placed 2 standard deviations above and below the middle band.'
        };
        
        return details[indicator] || 'No detailed information available.';
    }
    
    // Initialize when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Delay initialization slightly to ensure containers are ready
        setTimeout(initializeIndicatorMenu, 600);
    });
    
    // Export public functions to global scope
    window.renderIndicatorMenu = renderIndicatorMenu;
    window.getIndicatorDescription = window.getIndicatorDescription || getIndicatorDescription;
    window.getIndicatorDetailedInfo = window.getIndicatorDetailedInfo || getIndicatorDetailedInfo;
    
})();
