/* Base Theme Styles for StarCrypt */
:root {
  /* Dark theme as default */
  --bg-color: #0a0a1a;
  --text-color: #00FFFF;
  --border-color: #333;
  --box-size: 6.5rem;
  --label-width: 8rem;
  --mini-chart-width: 24rem;
  --logger-width: 100%;
  --logger-height: 12rem;
  --secondary-bg: #1a1a2a;
  --highlight-color: #00FF00;
  --animation-intensity: 3;
  
  /* Extended theme variables */
  --bg-primary: #0a0e17;
  --bg-secondary: #121d33;
  --bg-tertiary: #1a2942;
  --text-primary: #e0e0e0;
  --text-secondary: #a0a0a0;
  --accent-primary: #00b4d8;
  --accent-secondary: #0077b6;
  --positive: #2ecc71;
  --negative: #e74c3c;
  --neutral: #f39c12;
  --grid-color: rgba(255, 255, 255, 0.05);
  --tooltip-bg: rgba(10, 14, 23, 0.95);
  --tooltip-text: #ffffff;
  --chart-bg: #0a0e17;
  --chart-grid: rgba(255, 255, 255, 0.05);
  --chart-candle-up: #26a69a;
  --chart-candle-down: #ef5350;
  --chart-volume: rgba(0, 180, 216, 0.3);
  --chart-crosshair: rgba(255, 255, 255, 0.5);
}

/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5em;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1em;
  color: var(--text-secondary);
}

a {
  color: var(--accent-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--accent-secondary);
  text-decoration: underline;
}

/* Layout */
.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Buttons */
button, .btn {
  background-color: var(--accent-primary);
  color: var(--bg-primary);
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

button:hover, .btn:hover {
  background-color: var(--accent-secondary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

button:active, .btn:active {
  transform: translateY(0);
  box-shadow: none;
}

button:disabled, .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Forms */
input, select, textarea {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  font-size: 0.9rem;
  width: 100%;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 2px rgba(0, 180, 216, 0.2);
}

/* Cards */
.card {
  background-color: var(--bg-secondary);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Toolbar */
.toolbar {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Status indicators */
.status {
  display: inline-block;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.status.positive { background-color: var(--positive); }
.status.negative { background-color: var(--negative); }
.status.neutral { background-color: var(--neutral); }

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Utility classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-muted { color: var(--text-secondary); }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 0.75rem;
  }
  
  .toolbar {
    padding: 0.5rem;
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
}
