/**
 * Performance Optimizer for StarCrypt
 * Handles throttling, debouncing, memory management, and performance monitoring
 */

class PerformanceOptimizer {
  constructor() {
    this.updateQueues = new Map(); // Separate queues for different update types
    this.throttleTimers = new Map(); // Throttle timers
    this.debounceTimers = new Map(); // Debounce timers
    this.performanceMetrics = {
      updateCounts: {},
      averageUpdateTime: {},
      memoryUsage: [],
      lastCleanup: Date.now()
    };
    
    // Configuration
    this.config = {
      signalLightUpdateThrottle: 100, // ms
      indicatorUpdateThrottle: 150, // ms
      priceUpdateThrottle: 50, // ms
      chartUpdateThrottle: 200, // ms
      memoryCleanupInterval: 30000, // 30 seconds
      maxQueueSize: 100,
      maxMemoryEntries: 1000
    };
    
    // Start memory cleanup interval
    this.startMemoryCleanup();
    
    // Bind methods
    this.throttle = this.throttle.bind(this);
    this.debounce = this.debounce.bind(this);
    this.queueUpdate = this.queueUpdate.bind(this);
  }

  /**
   * Throttle function execution
   * @param {string} key - Unique key for this throttle
   * @param {Function} func - Function to throttle
   * @param {number} delay - Throttle delay in ms
   * @returns {Function} - Throttled function
   */
  throttle(key, func, delay) {
    return (...args) => {
      const now = Date.now();
      const lastCall = this.throttleTimers.get(key) || 0;
      
      if (now - lastCall >= delay) {
        this.throttleTimers.set(key, now);
        this.trackPerformance(key, () => func.apply(this, args));
      }
    };
  }

  /**
   * Debounce function execution
   * @param {string} key - Unique key for this debounce
   * @param {Function} func - Function to debounce
   * @param {number} delay - Debounce delay in ms
   * @returns {Function} - Debounced function
   */
  debounce(key, func, delay) {
    return (...args) => {
      const existingTimer = this.debounceTimers.get(key);
      if (existingTimer) {
        clearTimeout(existingTimer);
      }
      
      const timer = setTimeout(() => {
        this.debounceTimers.delete(key);
        this.trackPerformance(key, () => func.apply(this, args));
      }, delay);
      
      this.debounceTimers.set(key, timer);
    };
  }

  /**
   * Queue updates to prevent overwhelming the UI
   * @param {string} type - Update type (signalLights, indicators, etc.)
   * @param {Object} update - Update data
   */
  queueUpdate(type, update) {
    if (!this.updateQueues.has(type)) {
      this.updateQueues.set(type, []);
    }
    
    const queue = this.updateQueues.get(type);
    
    // Prevent queue overflow
    if (queue.length >= this.config.maxQueueSize) {
      queue.shift(); // Remove oldest update
    }
    
    queue.push({
      ...update,
      timestamp: Date.now()
    });
    
    // Process queue with appropriate throttling
    this.processQueue(type);
  }

  /**
   * Process update queue for a specific type
   * @param {string} type - Update type
   */
  processQueue(type) {
    const throttleKey = `queue_${type}`;
    const delay = this.config[`${type}UpdateThrottle`] || 100;
    
    const processFunc = this.throttle(throttleKey, () => {
      const queue = this.updateQueues.get(type);
      if (!queue || queue.length === 0) return;
      
      // Process all queued updates
      const updates = queue.splice(0);
      this.executeQueuedUpdates(type, updates);
    }, delay);
    
    processFunc();
  }

  /**
   * Execute queued updates
   * @param {string} type - Update type
   * @param {Array} updates - Array of updates to execute
   */
  executeQueuedUpdates(type, updates) {
    try {
      switch (type) {
        case 'signalLights':
          this.processSignalLightUpdates(updates);
          break;
        case 'indicators':
          this.processIndicatorUpdates(updates);
          break;
        case 'prices':
          this.processPriceUpdates(updates);
          break;
        case 'charts':
          this.processChartUpdates(updates);
          break;
        default:
          console.warn(`[PerformanceOptimizer] Unknown update type: ${type}`);
      }
    } catch (error) {
      console.error(`[PerformanceOptimizer] Error processing ${type} updates:`, error);
    }
  }

  /**
   * Process signal light updates efficiently
   * @param {Array} updates - Signal light updates
   */
  processSignalLightUpdates(updates) {
    // Group updates by indicator and timeframe to avoid duplicates
    const groupedUpdates = new Map();
    
    updates.forEach(update => {
      const key = `${update.indicator}_${update.timeframe}`;
      groupedUpdates.set(key, update); // Latest update wins
    });
    
    // Apply updates in batch
    const fragment = document.createDocumentFragment();
    let updateCount = 0;
    
    for (const [key, update] of groupedUpdates) {
      const element = document.querySelector(
        `.signal-circle[data-ind="${update.indicator}"][data-tf="${update.timeframe}"]`
      );
      
      if (element) {
        this.updateSignalElement(element, update);
        updateCount++;
      }
    }
    
    console.log(`[PerformanceOptimizer] Processed ${updateCount} signal light updates`);
  }

  /**
   * Update individual signal element
   * @param {Element} element - Signal circle element
   * @param {Object} update - Update data
   */
  updateSignalElement(element, update) {
    // Batch DOM updates
    element.style.cssText = `
      background-color: ${update.color || '#808080'};
      opacity: ${update.opacity || 1};
      transform: scale(${update.scale || 1});
    `;
    
    if (update.tooltip) {
      element.title = update.tooltip;
    }
    
    if (update.classes) {
      element.className = `signal-circle ${update.classes.join(' ')}`;
    }
  }

  /**
   * Process indicator updates
   * @param {Array} updates - Indicator updates
   */
  processIndicatorUpdates(updates) {
    // Group by indicator to avoid duplicate processing
    const groupedUpdates = new Map();
    
    updates.forEach(update => {
      if (!groupedUpdates.has(update.indicator)) {
        groupedUpdates.set(update.indicator, []);
      }
      groupedUpdates.get(update.indicator).push(update);
    });
    
    // Process each indicator's updates
    for (const [indicator, indicatorUpdates] of groupedUpdates) {
      this.processIndicatorGroup(indicator, indicatorUpdates);
    }
  }

  /**
   * Process price updates
   * @param {Array} updates - Price updates
   */
  processPriceUpdates(updates) {
    // Only use the latest price update
    const latestUpdate = updates[updates.length - 1];
    
    if (window.updatePriceDisplay && latestUpdate.price) {
      window.updatePriceDisplay(latestUpdate.price);
    }
  }

  /**
   * Process chart updates
   * @param {Array} updates - Chart updates
   */
  processChartUpdates(updates) {
    // Group by chart ID
    const chartUpdates = new Map();
    
    updates.forEach(update => {
      if (!chartUpdates.has(update.chartId)) {
        chartUpdates.set(update.chartId, []);
      }
      chartUpdates.get(update.chartId).push(update);
    });
    
    // Update each chart
    for (const [chartId, updates] of chartUpdates) {
      this.updateChart(chartId, updates);
    }
  }

  /**
   * Track performance metrics
   * @param {string} operation - Operation name
   * @param {Function} func - Function to track
   */
  trackPerformance(operation, func) {
    const startTime = performance.now();
    
    try {
      const result = func();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Update metrics
      if (!this.performanceMetrics.updateCounts[operation]) {
        this.performanceMetrics.updateCounts[operation] = 0;
        this.performanceMetrics.averageUpdateTime[operation] = 0;
      }
      
      const count = this.performanceMetrics.updateCounts[operation];
      const avgTime = this.performanceMetrics.averageUpdateTime[operation];
      
      this.performanceMetrics.updateCounts[operation] = count + 1;
      this.performanceMetrics.averageUpdateTime[operation] = 
        (avgTime * count + duration) / (count + 1);
      
      return result;
    } catch (error) {
      console.error(`[PerformanceOptimizer] Error in ${operation}:`, error);
      throw error;
    }
  }

  /**
   * Start memory cleanup interval
   */
  startMemoryCleanup() {
    setInterval(() => {
      this.cleanupMemory();
    }, this.config.memoryCleanupInterval);
  }

  /**
   * Clean up memory and expired timers
   */
  cleanupMemory() {
    const now = Date.now();
    
    // Clean up old performance metrics
    if (this.performanceMetrics.memoryUsage.length > this.config.maxMemoryEntries) {
      this.performanceMetrics.memoryUsage = this.performanceMetrics.memoryUsage.slice(-100);
    }
    
    // Clear expired debounce timers
    for (const [key, timer] of this.debounceTimers) {
      if (timer._destroyed) {
        this.debounceTimers.delete(key);
      }
    }
    
    // Clear old throttle timestamps
    for (const [key, timestamp] of this.throttleTimers) {
      if (now - timestamp > 60000) { // 1 minute old
        this.throttleTimers.delete(key);
      }
    }
    
    // Record memory usage
    if (performance.memory) {
      this.performanceMetrics.memoryUsage.push({
        timestamp: now,
        used: performance.memory.usedJSHeapSize,
        total: performance.memory.totalJSHeapSize
      });
    }
    
    this.performanceMetrics.lastCleanup = now;
    console.log('[PerformanceOptimizer] Memory cleanup completed');
  }

  /**
   * Get performance statistics
   * @returns {Object} - Performance statistics
   */
  getStats() {
    return {
      ...this.performanceMetrics,
      queueSizes: Object.fromEntries(
        Array.from(this.updateQueues.entries()).map(([key, queue]) => [key, queue.length])
      ),
      activeTimers: {
        throttle: this.throttleTimers.size,
        debounce: this.debounceTimers.size
      }
    };
  }

  /**
   * Reset performance metrics
   */
  resetMetrics() {
    this.performanceMetrics = {
      updateCounts: {},
      averageUpdateTime: {},
      memoryUsage: [],
      lastCleanup: Date.now()
    };
  }
}

// Create global instance
window.performanceOptimizer = new PerformanceOptimizer();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PerformanceOptimizer;
}
