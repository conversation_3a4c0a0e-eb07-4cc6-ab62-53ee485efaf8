/**
 * StarCrypt WebSocket Core - Unified WebSocket Management System
 * Consolidates all WebSocket functionality into a single, robust system
 */

class StarCryptWebSocketCore {
  constructor(options = {}) {
    // Configuration with sensible defaults
    this.config = {
      url: options.url || this.getWebSocketURL(),
      protocols: options.protocols || [],
      maxReconnectAttempts: options.maxReconnectAttempts || 10,
      reconnectInterval: options.reconnectInterval || 1000,
      maxReconnectInterval: options.maxReconnectInterval || 30000,
      connectionTimeout: options.connectionTimeout || 5000,
      pingInterval: options.pingInterval || 25000,
      pingTimeout: options.pingTimeout || 10000,
      maxBatchSize: options.maxBatchSize || 10,
      maxQueueSize: options.maxQueueSize || 1000,
      processDelay: options.processDelay || 10,
      maxDepth: options.maxDepth || 5,
      maxConsecutiveErrors: options.maxConsecutiveErrors || 10,
      errorResetTime: options.errorResetTime || 60000,
      debug: options.debug || false,
      ...options
    };

    // Connection state
    this.ws = null;
    this.isConnected = false;
    this.isConnecting = false;
    this.isReconnecting = false;
    this.reconnectAttempts = 0;
    this.shouldReconnect = true;
    this.lastMessageTime = 0;

    // Message processing
    this.messageQueue = [];
    this.messageHandlers = new Map();
    this.processedMessages = new Set();
    this.currentlyProcessing = new Set();
    this.isProcessing = false;
    this.isPaused = false;
    this.consecutiveErrors = 0;
    this.lastErrorTime = 0;
    this.currentDepth = 0;

    // Timers and intervals
    this.connectionTimeoutId = null;
    this.reconnectTimeoutId = null;
    this.pingIntervalId = null;
    this.pingTimeoutId = null;
    this.processIntervalId = null;
    this.cleanupIntervalId = null;

    // Event system
    this.eventListeners = new Map();

    // Initialize
    this.init();
  }

  /**
   * Get the appropriate WebSocket URL based on environment
   */
  getWebSocketURL() {
    if (typeof window !== 'undefined') {
      const isLocalhost = window.location.hostname === 'localhost';
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = isLocalhost ? 'localhost:8080' : window.location.host;
      return `${protocol}//${host}`;
    }
    return 'ws://localhost:8080';
  }

  /**
   * Initialize the WebSocket core
   */
  init() {
    this.log('Initializing StarCrypt WebSocket Core...');
    
    // Set up cleanup interval
    this.cleanupIntervalId = setInterval(() => {
      this.cleanup();
    }, 60000); // Cleanup every minute

    // Set up message processing
    this.startMessageProcessor();

    // Auto-connect if not disabled
    if (this.config.autoConnect !== false) {
      this.connect();
    }

    this.log('WebSocket Core initialized');
  }

  /**
   * Connect to WebSocket server
   */
  connect() {
    if (this.isConnecting || this.isConnected) {
      this.log('Already connecting or connected');
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      this.isConnecting = true;
      this.log(`Connecting to ${this.config.url}...`);

      try {
        // Create WebSocket connection
        this.ws = new WebSocket(this.config.url, this.config.protocols);

        // Set connection timeout
        this.connectionTimeoutId = setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close();
            this.handleConnectionError(new Error('Connection timeout'));
            reject(new Error('Connection timeout'));
          }
        }, this.config.connectionTimeout);

        // WebSocket event handlers
        this.ws.onopen = (event) => {
          this.handleOpen(event);
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event);
        };

        this.ws.onclose = (event) => {
          this.handleClose(event);
        };

        this.ws.onerror = (event) => {
          this.handleError(event);
          if (this.isConnecting) {
            reject(new Error('WebSocket connection failed'));
          }
        };

      } catch (error) {
        this.isConnecting = false;
        this.handleConnectionError(error);
        reject(error);
      }
    });
  }

  /**
   * Handle WebSocket open event
   */
  handleOpen(event) {
    this.log('WebSocket connected successfully');
    
    // Clear connection timeout
    if (this.connectionTimeoutId) {
      clearTimeout(this.connectionTimeoutId);
      this.connectionTimeoutId = null;
    }

    // Update state
    this.isConnected = true;
    this.isConnecting = false;
    this.isReconnecting = false;
    this.reconnectAttempts = 0;
    this.lastMessageTime = Date.now();

    // Start ping mechanism
    this.startPing();

    // Process any queued messages
    this.processQueuedMessages();

    // Emit connected event
    this.emit('connected', { event });

    // Subscribe to default channels
    this.subscribeToChannels();
  }

  /**
   * Handle WebSocket message event
   */
  handleMessage(event) {
    this.lastMessageTime = Date.now();
    
    try {
      // Parse message
      let message;
      if (typeof event.data === 'string') {
        message = JSON.parse(event.data);
      } else {
        message = this.handleBinaryData(event.data);
      }

      // Add message metadata
      message._messageId = this.generateMessageId();
      message._timestamp = Date.now();
      message._processed = false;

      // Queue message for processing
      this.queueMessage(message);

    } catch (error) {
      this.error('Error parsing WebSocket message:', error);
      this.handleMessageError(error, event.data);
    }
  }

  /**
   * Handle WebSocket close event
   */
  handleClose(event) {
    this.log(`WebSocket closed: ${event.code} - ${event.reason}`);
    
    // Update state
    this.isConnected = false;
    this.isConnecting = false;

    // Clear timers
    this.clearTimers();

    // Emit disconnected event
    this.emit('disconnected', { event });

    // Attempt reconnection if appropriate
    if (this.shouldReconnect && !event.wasClean) {
      this.scheduleReconnect();
    }
  }

  /**
   * Handle WebSocket error event
   */
  handleError(event) {
    this.error('WebSocket error:', event);
    this.emit('error', { event });
  }

  /**
   * Queue message for processing
   */
  queueMessage(message) {
    // Check queue size
    if (this.messageQueue.length >= this.config.maxQueueSize) {
      this.warn('Message queue full, dropping oldest message');
      this.messageQueue.shift();
    }

    // Add to queue
    this.messageQueue.push(message);
    
    // Start processing if not already running
    if (!this.isProcessing && !this.isPaused) {
      this.processMessages();
    }
  }

  /**
   * Start message processor
   */
  startMessageProcessor() {
    if (this.processIntervalId) return;

    this.processIntervalId = setInterval(() => {
      if (!this.isProcessing && !this.isPaused && this.messageQueue.length > 0) {
        this.processMessages();
      }
    }, this.config.processDelay);
  }

  /**
   * Process queued messages
   */
  async processMessages() {
    if (this.isProcessing || this.isPaused || this.messageQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    this.currentDepth++;

    try {
      // Process batch of messages
      const batchSize = Math.min(this.config.maxBatchSize, this.messageQueue.length);
      const batch = this.messageQueue.splice(0, batchSize);

      for (const message of batch) {
        await this.processMessage(message);
      }

      // Reset error counter on successful batch
      this.consecutiveErrors = 0;

    } catch (error) {
      this.handleProcessingError(error);
    } finally {
      this.currentDepth--;
      this.isProcessing = false;
    }
  }

  /**
   * Process a single message
   */
  async processMessage(message) {
    const messageId = message._messageId;
    
    // Check for duplicate processing
    if (this.currentlyProcessing.has(messageId) || message._processed) {
      return;
    }

    this.currentlyProcessing.add(messageId);

    try {
      // Mark as processed
      message._processed = true;
      message._processedAt = Date.now();

      // Get message type and data
      const type = message.type;
      const data = message.data || message;

      // Process with handlers
      if (type && this.messageHandlers.has(type)) {
        const handlers = this.messageHandlers.get(type);
        for (const handler of handlers) {
          try {
            await handler(data, message);
          } catch (handlerError) {
            this.error(`Handler error for ${type}:`, handlerError);
          }
        }
      }

      // Emit event
      if (type) {
        this.emit(`message:${type}`, { data, message });
        this.emit('message', { type, data, message });
      }

      // Add to processed set
      this.processedMessages.add(messageId);

    } catch (error) {
      this.error(`Error processing message ${messageId}:`, error);
      throw error;
    } finally {
      this.currentlyProcessing.delete(messageId);
    }
  }

  /**
   * Send message to server
   */
  send(message) {
    if (!this.isConnected || !this.ws) {
      this.warn('Cannot send message: not connected');
      return false;
    }

    try {
      const data = typeof message === 'string' ? message : JSON.stringify(message);
      this.ws.send(data);
      this.log('Message sent:', message);
      return true;
    } catch (error) {
      this.error('Error sending message:', error);
      return false;
    }
  }

  /**
   * Subscribe to channels
   */
  subscribeToChannels() {
    const channels = ['prices', 'signals', 'heartbeat'];
    for (const channel of channels) {
      this.send({
        type: 'subscribe',
        channel: channel
      });
    }
  }

  /**
   * Add message handler
   */
  addHandler(type, handler) {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type).push(handler);
  }

  /**
   * Remove message handler
   */
  removeHandler(type, handler) {
    if (this.messageHandlers.has(type)) {
      const handlers = this.messageHandlers.get(type);
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * Event system methods
   */
  on(event, listener) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(listener);
  }

  off(event, listener) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event);
      for (const listener of listeners) {
        try {
          listener(data);
        } catch (error) {
          this.error(`Event listener error for ${event}:`, error);
        }
      }
    }

    // Also dispatch DOM event
    if (typeof document !== 'undefined') {
      try {
        const customEvent = new CustomEvent(`starcrypt:${event}`, { detail: data });
        document.dispatchEvent(customEvent);
      } catch (error) {
        this.error(`Error dispatching DOM event ${event}:`, error);
      }
    }
  }

  /**
   * Utility methods
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  log(...args) {
    if (this.config.debug) {
      console.log('[StarCrypt WebSocket]', ...args);
    }
  }

  warn(...args) {
    console.warn('[StarCrypt WebSocket]', ...args);
  }

  error(...args) {
    console.error('[StarCrypt WebSocket]', ...args);
  }

  /**
   * Cleanup and resource management
   */
  cleanup() {
    // Clean old processed messages
    const cutoff = Date.now() - (5 * 60 * 1000); // 5 minutes
    const toDelete = [];
    
    for (const messageId of this.processedMessages) {
      if (messageId.includes('_') && parseInt(messageId.split('_')[1]) < cutoff) {
        toDelete.push(messageId);
      }
    }
    
    for (const messageId of toDelete) {
      this.processedMessages.delete(messageId);
    }
  }

  clearTimers() {
    if (this.connectionTimeoutId) {
      clearTimeout(this.connectionTimeoutId);
      this.connectionTimeoutId = null;
    }
    if (this.reconnectTimeoutId) {
      clearTimeout(this.reconnectTimeoutId);
      this.reconnectTimeoutId = null;
    }
    if (this.pingIntervalId) {
      clearInterval(this.pingIntervalId);
      this.pingIntervalId = null;
    }
    if (this.pingTimeoutId) {
      clearTimeout(this.pingTimeoutId);
      this.pingTimeoutId = null;
    }
  }

  /**
   * Disconnect and cleanup
   */
  disconnect() {
    this.shouldReconnect = false;
    this.clearTimers();
    
    if (this.cleanupIntervalId) {
      clearInterval(this.cleanupIntervalId);
      this.cleanupIntervalId = null;
    }
    
    if (this.processIntervalId) {
      clearInterval(this.processIntervalId);
      this.processIntervalId = null;
    }
    
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    
    this.isConnected = false;
    this.isConnecting = false;
  }

  /**
   * Start ping mechanism
   */
  startPing() {
    if (this.pingIntervalId) return;

    this.pingIntervalId = setInterval(() => {
      if (this.isConnected && this.ws) {
        this.send({ type: 'ping', timestamp: Date.now() });

        // Set ping timeout
        this.pingTimeoutId = setTimeout(() => {
          this.warn('Ping timeout - connection may be lost');
          this.handleConnectionError(new Error('Ping timeout'));
        }, this.config.pingTimeout);
      }
    }, this.config.pingInterval);
  }

  /**
   * Handle connection error
   */
  handleConnectionError(error) {
    this.error('Connection error:', error);
    this.isConnecting = false;
    this.isConnected = false;
    this.clearTimers();
    this.emit('connectionError', { error });
  }

  /**
   * Handle message processing error
   */
  handleProcessingError(error) {
    this.consecutiveErrors++;
    this.lastErrorTime = Date.now();

    this.error('Message processing error:', error);

    // Pause processing if too many consecutive errors
    if (this.consecutiveErrors >= this.config.maxConsecutiveErrors) {
      this.warn('Too many consecutive errors, pausing message processing');
      this.isPaused = true;

      // Resume after error reset time
      setTimeout(() => {
        this.consecutiveErrors = 0;
        this.isPaused = false;
        this.log('Resuming message processing after error reset');
      }, this.config.errorResetTime);
    }
  }

  /**
   * Handle message parsing error
   */
  handleMessageError(error, rawData) {
    this.error('Message parsing error:', error);
    this.emit('messageError', { error, rawData });
  }

  /**
   * Schedule reconnection attempt
   */
  scheduleReconnect() {
    if (this.isReconnecting || !this.shouldReconnect) return;

    this.isReconnecting = true;
    this.reconnectAttempts++;

    if (this.reconnectAttempts > this.config.maxReconnectAttempts) {
      this.error('Max reconnection attempts reached');
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    // Calculate delay with exponential backoff
    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(1.5, this.reconnectAttempts - 1),
      this.config.maxReconnectInterval
    );

    this.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);

    this.reconnectTimeoutId = setTimeout(() => {
      this.log(`Reconnection attempt ${this.reconnectAttempts}`);
      this.connect().catch(error => {
        this.error('Reconnection failed:', error);
        this.scheduleReconnect();
      });
    }, delay);
  }

  /**
   * Process queued messages after connection
   */
  processQueuedMessages() {
    if (this.messageQueue.length > 0) {
      this.log(`Processing ${this.messageQueue.length} queued messages`);
      this.processMessages();
    }
  }

  /**
   * Handle binary data
   */
  handleBinaryData(data) {
    try {
      // Convert ArrayBuffer to string
      if (data instanceof ArrayBuffer) {
        return JSON.parse(String.fromCharCode.apply(null, new Uint8Array(data)));
      }
      // Handle other binary formats as needed
      return JSON.parse(data.toString());
    } catch (error) {
      this.error('Error handling binary data:', error);
      throw error;
    }
  }

  /**
   * Get connection status
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      isReconnecting: this.isReconnecting,
      reconnectAttempts: this.reconnectAttempts,
      messageQueueLength: this.messageQueue.length,
      processedMessagesCount: this.processedMessages.size,
      consecutiveErrors: this.consecutiveErrors,
      isPaused: this.isPaused,
      lastMessageTime: this.lastMessageTime,
      url: this.config.url
    };
  }

  /**
   * Force reconnection
   */
  forceReconnect() {
    this.log('Forcing reconnection...');
    this.shouldReconnect = true;
    this.reconnectAttempts = 0;

    if (this.ws) {
      this.ws.close();
    }

    setTimeout(() => {
      this.connect();
    }, 100);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.log('Configuration updated:', newConfig);
  }

  /**
   * Get statistics
   */
  getStats() {
    return {
      ...this.getStatus(),
      config: { ...this.config },
      messageHandlers: Array.from(this.messageHandlers.keys()),
      eventListeners: Array.from(this.eventListeners.keys()),
      uptime: this.isConnected ? Date.now() - this.lastMessageTime : 0
    };
  }
}

// Create singleton instance
let webSocketCoreInstance = null;

/**
 * Get or create WebSocket core singleton
 */
function getWebSocketCore(options = {}) {
  if (!webSocketCoreInstance) {
    webSocketCoreInstance = new StarCryptWebSocketCore(options);
  }
  return webSocketCoreInstance;
}

// Export for both CommonJS and ES6
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { StarCryptWebSocketCore, getWebSocketCore };
} else if (typeof window !== 'undefined') {
  window.StarCryptWebSocketCore = StarCryptWebSocketCore;
  window.getWebSocketCore = getWebSocketCore;

  // Initialize global instance
  window.wsCore = getWebSocketCore();
}
