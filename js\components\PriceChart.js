class PriceChart {
  constructor(containerId) {
    this.container = document.getElementById(containerId)
    if (!this.container) {
      throw new Error(`Container with id "${containerId}" not found`)
    }

    this.chart = null
    this.resizeObserver = null
    this.resizeHandler = null

    this.initialize()
  }

  initialize() {
    // Create canvas element
    this.canvas = document.createElement('canvas')
    this.canvas.id = 'priceChart'
    this.canvas.style.width = '100%'
    this.canvas.style.height = '100%'
    this.container.appendChild(this.canvas)

    // Set up resize handling
    this.setupResizeHandling()

    // Initialize chart
    this.initChart()
  }

  setupResizeHandling() {
    // Handle container resize
    this.resizeObserver = new ResizeObserver(() => {
      if (this.chart) {
        this.chart.resize()
      }
    })

    this.resizeObserver.observe(this.container)

    // Handle window resize
    this.resizeHandler = () => {
      if (this.chart) {
        this.chart.resize()
      }
    }

    window.addEventListener('resize', this.resizeHandler)
  }

  generateRandomWalk(length, start, volatility) {
    const result = [start]
    for (let i = 1; i < length; i++) {
      result.push(result[i - 1] + (Math.random() - 0.5) * volatility)
    }
    return result
  }

  initChart() {
    try {
      // Sample data
      const labels = Array.from({ length: 100 }, (_, i) => `P${i + 1}`)
      const data = {
        labels,
        datasets: [{
          label: 'Price',
          data: this.generateRandomWalk(100, 100, 0.5),
          borderColor: '#00FFAA',
          backgroundColor: 'rgba(0, 255, 170, 0.1)',
          borderWidth: 2,
          pointRadius: 0,
          tension: 0.1,
          fill: true,
        }],
      }

      // Chart configuration
      const config = {
        type: 'line',
        data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          animation: false,
          interaction: {
            mode: 'index',
            intersect: false,
          },
          plugins: {
            legend: {
              display: true,
              position: 'top',
              labels: {
                color: '#fff',
                font: {
                  size: 12,
                },
              },
            },
            tooltip: {
              enabled: true,
              mode: 'index',
              intersect: false,
              backgroundColor: 'rgba(0, 0, 0, 0.8)',
              titleColor: '#fff',
              bodyColor: '#fff',
              borderColor: 'rgba(255, 255, 255, 0.1)',
              borderWidth: 1,
              padding: 10,
              displayColors: true,
            },
          },
          scales: {
            x: {
              grid: {
                display: true,
                color: 'rgba(255, 255, 255, 0.05)',
              },
              ticks: {
                color: 'rgba(255, 255, 255, 0.7)',
                font: {
                  size: 10,
                },
                maxRotation: 0,
                autoSkip: true,
                maxTicksLimit: 10,
              },
            },
            y: {
              beginAtZero: false,
              grid: {
                display: true,
                color: 'rgba(255, 255, 255, 0.05)',
              },
              ticks: {
                color: 'rgba(255, 255, 255, 0.7)',
                font: {
                  size: 10,
                },
                callback(value) {
                  return value.toFixed(2)
                },
              },
            },
          },
          elements: {
            line: {
              borderWidth: 2,
              tension: 0.1,
            },
            point: {
              radius: 0,
              hitRadius: 10,
              hoverRadius: 4,
              hoverBorderWidth: 2,
            },
          },
          layout: {
            padding: {
              top: 10,
              right: 10,
              bottom: 10,
              left: 10,
            },
          },
        },
      }

      // Create the chart
      const ctx = this.canvas.getContext('2d')
      this.chart = new Chart(ctx, config)
    } catch (error) {
      console.error('Error initializing chart:', error)
      throw error
    }
  }

  destroy() {
    // Clean up event listeners
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }

    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler)
    }

    // Destroy chart
    if (this.chart) {
      this.chart.destroy()
    }

    // Remove canvas
    if (this.canvas && this.canvas.parentNode) {
      this.canvas.parentNode.removeChild(this.canvas)
    }
  }

  // Public method to update chart data
  updateData(newData) {
    if (this.chart) {
      this.chart.data.datasets[0].data = newData
      this.chart.update()
    }
  }
}

// Export as ES Module
export default PriceChart
