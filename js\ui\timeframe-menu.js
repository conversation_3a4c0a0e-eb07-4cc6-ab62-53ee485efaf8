/**
 * Timeframe Menu Controller
 * Handles the timeframe menu UI and functionality for removing timeframe columns from the matrix
 */

class TimeframeMenuController {
  constructor() {
    this.timeframes = [...(window.TIMEFRAMES || ['1m', '5m', '15m', '1h', '4h', '1d', '1w'])];
    this.activeTimeframes = [...this.timeframes];
    this.initialize();
  }

  initialize() {
    this.createTimeframeMenu();
    console.log('[TimeframeMenu] Initialized with timeframes:', this.timeframes);
  }

  createTimeframeMenu() {
    // Create timeframe menu container if it doesn't exist
    let timeframeMenu = document.getElementById('timeframeMenu');
    if (!timeframeMenu) {
      timeframeMenu = document.createElement('div');
      timeframeMenu.id = 'timeframeMenu';
      timeframeMenu.className = 'menu-content';
      
      // Create header
      const header = document.createElement('div');
      header.className = 'menu-header';
      header.innerHTML = `
        <h3>Timeframe Selector</h3>
        <button class="close-menu" id="closeTimeframeMenu">×</button>
      `;
      timeframeMenu.appendChild(header);
      
      // Create content
      const content = document.createElement('div');
      content.className = 'timeframe-menu-content';
      content.innerHTML = `
        <div class="menu-description">
          Select which timeframes to display in the momentum indicators matrix. Uncheck timeframes to hide those columns.
        </div>
        <div id="timeframe-checkboxes" class="timeframe-checkboxes">
          ${this.renderTimeframeCheckboxes()}
        </div>
        <button id="applyTimeframes" class="apply-button">Apply Timeframes</button>
      `;
      timeframeMenu.appendChild(content);
      
      // Add to document
      const tickerContainer = document.getElementById('tickerContainer');
      if (tickerContainer) {
        tickerContainer.appendChild(timeframeMenu);
      } else {
        document.body.appendChild(timeframeMenu);
      }
      
      // Setup event listeners
      this.setupEventListeners();
    }
  }
  
  renderTimeframeCheckboxes() {
    return this.timeframes.map(timeframe => `
      <div class="timeframe-checkbox-item">
        <input type="checkbox" id="timeframe-${timeframe}" data-timeframe="${timeframe}" 
               ${this.activeTimeframes.includes(timeframe) ? 'checked' : ''}>
        <label for="timeframe-${timeframe}">${timeframe}</label>
      </div>
    `).join('');
  }
  
  setupEventListeners() {
    // Close button
    const closeButton = document.getElementById('closeTimeframeMenu');
    if (closeButton) {
      closeButton.addEventListener('click', () => {
        this.closeMenu();
      });
    }
    
    // Apply button
    const applyButton = document.getElementById('applyTimeframes');
    if (applyButton) {
      applyButton.addEventListener('click', () => {
        this.applyTimeframeSelection();
      });
    }
  }
  
  closeMenu() {
    const menu = document.getElementById('timeframeMenu');
    if (menu) {
      menu.style.display = 'none';
    }
    
    // If using MenuController, update its state
    if (window.menuController && window.menuController.activeMenu === 'timeframeMenu') {
      window.menuController.activeMenu = null;
    }
  }
  
  applyTimeframeSelection() {
    // Get selected timeframes
    const checkboxes = document.querySelectorAll('#timeframe-checkboxes input[type="checkbox"]');
    this.activeTimeframes = Array.from(checkboxes)
      .filter(cb => cb.checked)
      .map(cb => cb.dataset.timeframe);
    
    // Update the UI
    this.updateTimeframeColumns();
    
    // Show confirmation
    this.showToast(`Timeframes updated: ${this.activeTimeframes.join(', ')}`, 'success');
    
    // Close the menu
    this.closeMenu();
  }
  
  updateTimeframeColumns() {
    // Get all timeframe columns in the momentum indicators matrix
    const signalMatrix = document.getElementById('momentum-indicators');
    if (!signalMatrix) return;
    
    // Update column visibility based on active timeframes
    this.timeframes.forEach(timeframe => {
      const columns = signalMatrix.querySelectorAll(`.signal-column[data-timeframe="${timeframe}"]`);
      const isActive = this.activeTimeframes.includes(timeframe);
      
      columns.forEach(column => {
        column.style.display = isActive ? '' : 'none';
      });
    });
    
    console.log(`[TimeframeMenu] Updated timeframe columns: ${this.activeTimeframes.join(', ')}`);
  }
  
  showToast(message, type = 'info') {
    if (typeof window.showToast === 'function') {
      window.showToast(message, type);
    } else {
      console.log(`[TimeframeMenu] ${type.toUpperCase()}: ${message}`);
      alert(message);
    }
  }
}

// Initialize on load
function initializeTimeframeMenu() {
  window.timeframeMenuController = new TimeframeMenuController();
}

// Add to window for global access
window.initializeTimeframeMenu = initializeTimeframeMenu;

// Auto-initialize if document is already loaded
if (document.readyState === 'complete' || document.readyState === 'interactive') {
  setTimeout(initializeTimeframeMenu, 100);
} else {
  document.addEventListener('DOMContentLoaded', initializeTimeframeMenu);
}
