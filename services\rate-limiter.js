const config = require('./config');
const RATE_LIMIT = config.KRAKEN.RATE_LIMIT;

class RateLimiter {
  constructor() {
    this.requestsPerInterval = RATE_LIMIT.REQUESTS_PER_INTERVAL;
    this.interval = RATE_LIMIT.INTERVAL_MS;
    this.maxRetries = RATE_LIMIT.MAX_RETRIES;
    this.retryDelay = RATE_LIMIT.RETRY_DELAY;
    this.queue = [];
    this.tokens = this.requestsPerInterval;
    this.isProcessing = false;
    
    // Start the token replenishment
    this.refillInterval = setInterval(() => {
      this.tokens = Math.min(this.tokens + 1, this.requestsPerInterval);
      this.processQueue();
    }, this.interval / this.requestsPerInterval);
    
    // Don't keep the process alive just for this interval
    if (this.refillInterval.unref) {
      this.refillInterval.unref();
    }
  }

  /**
   * Acquire a token from the rate limiter
   * @returns {Promise<void>} Resolves when a token is available
   */
  async acquire() {
    return new Promise((resolve, reject) => {
      try {
        this.queue.push(resolve);
        this.processQueue();
      } catch (error) {
        console.error('Error in rate limiter acquire:', error);
        reject(error);
      }
    });
  }

  /**
   * Process the queue of waiting operations
   */
  processQueue() {
    if (this.isProcessing || this.queue.length === 0 || this.tokens <= 0) {
      return;
    }

    this.isProcessing = true;
    
    // Use setImmediate to avoid blocking the event loop
    setImmediate(() => {
      try {
        while (this.tokens > 0 && this.queue.length > 0) {
          const resolve = this.queue.shift();
          if (typeof resolve === 'function') {
            this.tokens--;
            resolve();
          }
        }
      } catch (error) {
        console.error('Error in rate limiter queue processing:', error);
      } finally {
        this.isProcessing = false;
      }
    });
  }

  async withRetry(operation, maxRetries = this.maxRetries, initialDelay = this.retryDelay) {
    let lastError;
    let attempt = 0;
    let delay = initialDelay;

    while (attempt < maxRetries) {
      try {
        // Wait for a token using the correct method name
        await this.acquire();
        // Execute the operation
        return await operation();
      } catch (error) {
        lastError = error;
        attempt++;
        
        if (attempt < maxRetries) {
          // Calculate exponential backoff with jitter
          const jitter = Math.random() * 0.3 + 0.85; // Random between 0.85 and 1.15
          const backoff = Math.min(delay * Math.pow(2, attempt - 1) * jitter, 30000); // Cap at 30s
          
          console.warn(`Attempt ${attempt} failed (${error.message}). Retrying in ${Math.round(backoff)}ms...`);
          await new Promise(resolve => setTimeout(resolve, backoff));
        }
      }
    }

    // All retries failed
    const errorMessage = lastError ? lastError.message : 'Unknown error';
    console.error(`All ${maxRetries} attempts failed. Last error:`, errorMessage);
    throw lastError || new Error('Rate limiter retries exhausted');
  }

  destroy() {
    if (this.refillInterval) {
      clearInterval(this.refillInterval);
      this.refillInterval = null;
    }
    
    // Reject all pending requests
    while (this.queue.length > 0) {
      const resolve = this.queue.shift();
      resolve(new Error('Rate limiter destroyed'));
    }
  }
}

// Singleton instance
const krakenRateLimiter = new RateLimiter();

module.exports = krakenRateLimiter;
