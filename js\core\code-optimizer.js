/**
 * Code Optimizer for StarCrypt
 * Handles redundancy removal, performance optimization, and code organization
 */

class CodeOptimizer {
  constructor() {
    this.redundancies = {
      duplicateEventListeners: [],
      duplicateFunctions: [],
      unusedVariables: [],
      redundantCalls: []
    };
    
    this.optimizations = {
      applied: [],
      pending: [],
      failed: []
    };
    
    this.isInitialized = false;
  }

  /**
   * Initialize the code optimizer
   */
  async initialize() {
    if (this.isInitialized) {
      console.log('[CodeOptimizer] Already initialized');
      return;
    }

    console.log('[CodeOptimizer] Initializing code optimization...');
    
    try {
      // Clean up duplicate event listeners
      await this.cleanupEventListeners();
      
      // Optimize WebSocket connections
      await this.optimizeWebSocketConnections();
      
      // Clean up unused DOM elements
      await this.cleanupUnusedElements();
      
      // Optimize signal light updates
      await this.optimizeSignalUpdates();
      
      // Clean up memory leaks
      await this.cleanupMemoryLeaks();
      
      this.isInitialized = true;
      console.log('[CodeOptimizer] Initialization complete');
      
    } catch (error) {
      console.error('[CodeOptimizer] Initialization failed:', error);
    }
  }

  /**
   * Clean up duplicate event listeners
   */
  async cleanupEventListeners() {
    console.log('[CodeOptimizer] Cleaning up event listeners...');
    
    try {
      // Use the event manager if available
      if (window.eventManager) {
        const stats = window.eventManager.getStats();
        console.log('[CodeOptimizer] Event listener stats:', stats);
        
        // Log any potential duplicates
        Object.entries(stats.listenersByEvent).forEach(([event, count]) => {
          if (count > 5) { // Arbitrary threshold for "too many"
            console.warn(`[CodeOptimizer] Potentially excessive listeners for ${event}: ${count}`);
          }
        });
      }
      
      // Clean up specific known duplicate patterns
      this.cleanupKnownDuplicates();
      
      this.optimizations.applied.push('eventListenerCleanup');
      
    } catch (error) {
      console.error('[CodeOptimizer] Event listener cleanup failed:', error);
      this.optimizations.failed.push('eventListenerCleanup');
    }
  }

  /**
   * Clean up known duplicate event listener patterns
   */
  cleanupKnownDuplicates() {
    // Remove duplicate change listeners on document
    const changeListeners = this.getEventListenerCount(document, 'change');
    if (changeListeners > 1) {
      console.warn(`[CodeOptimizer] Found ${changeListeners} change listeners on document`);
    }
    
    // Remove duplicate input listeners
    const inputListeners = this.getEventListenerCount(document, 'input');
    if (inputListeners > 1) {
      console.warn(`[CodeOptimizer] Found ${inputListeners} input listeners on document`);
    }
  }

  /**
   * Get event listener count for an element (approximation)
   */
  getEventListenerCount(element, eventType) {
    // This is a rough approximation since we can't directly count listeners
    // In a real implementation, you'd track this through the event manager
    return 1; // Placeholder
  }

  /**
   * Optimize WebSocket connections
   */
  async optimizeWebSocketConnections() {
    console.log('[CodeOptimizer] Optimizing WebSocket connections...');
    
    try {
      // Check for multiple WebSocket instances
      const wsInstances = [];
      
      if (window.ws) wsInstances.push('window.ws');
      if (window.wsManager) wsInstances.push('window.wsManager');
      if (window.wsProcessor) wsInstances.push('window.wsProcessor');
      
      if (wsInstances.length > 1) {
        console.warn('[CodeOptimizer] Multiple WebSocket instances detected:', wsInstances);
      }
      
      // Optimize message processing
      if (window.wsProcessor && window.wsProcessor.messageQueue) {
        const queueSize = window.wsProcessor.messageQueue.length;
        if (queueSize > 100) {
          console.warn(`[CodeOptimizer] Large message queue detected: ${queueSize} messages`);
          // Could implement queue trimming here
        }
      }
      
      this.optimizations.applied.push('websocketOptimization');
      
    } catch (error) {
      console.error('[CodeOptimizer] WebSocket optimization failed:', error);
      this.optimizations.failed.push('websocketOptimization');
    }
  }

  /**
   * Clean up unused DOM elements
   */
  async cleanupUnusedElements() {
    console.log('[CodeOptimizer] Cleaning up unused DOM elements...');
    
    try {
      // Remove empty containers
      const emptyContainers = document.querySelectorAll('div:empty, span:empty');
      let removedCount = 0;
      
      emptyContainers.forEach(element => {
        // Only remove if it doesn't have important classes or IDs
        if (!element.id && !element.className.includes('important')) {
          element.remove();
          removedCount++;
        }
      });
      
      if (removedCount > 0) {
        console.log(`[CodeOptimizer] Removed ${removedCount} empty elements`);
      }
      
      // Clean up orphaned tooltips
      this.cleanupOrphanedTooltips();
      
      this.optimizations.applied.push('domCleanup');
      
    } catch (error) {
      console.error('[CodeOptimizer] DOM cleanup failed:', error);
      this.optimizations.failed.push('domCleanup');
    }
  }

  /**
   * Clean up orphaned tooltips
   */
  cleanupOrphanedTooltips() {
    const tooltips = document.querySelectorAll('[class*="tooltip"], [id*="tooltip"]');
    let removedCount = 0;
    
    tooltips.forEach(tooltip => {
      // Check if tooltip is still connected to a valid parent
      if (!tooltip.parentElement || !document.contains(tooltip.parentElement)) {
        tooltip.remove();
        removedCount++;
      }
    });
    
    if (removedCount > 0) {
      console.log(`[CodeOptimizer] Removed ${removedCount} orphaned tooltips`);
    }
  }

  /**
   * Optimize signal light updates
   */
  async optimizeSignalUpdates() {
    console.log('[CodeOptimizer] Optimizing signal updates...');
    
    try {
      // Check for excessive update calls
      if (window.signalSystem) {
        const updateStats = window.signalSystem.getUpdateStats?.() || {};
        console.log('[CodeOptimizer] Signal update stats:', updateStats);
        
        // Optimize update frequency if needed
        if (updateStats.updatesPerSecond > 10) {
          console.warn('[CodeOptimizer] High signal update frequency detected');
          // Could implement additional throttling here
        }
      }
      
      // Consolidate signal light containers
      this.consolidateSignalContainers();
      
      this.optimizations.applied.push('signalOptimization');
      
    } catch (error) {
      console.error('[CodeOptimizer] Signal optimization failed:', error);
      this.optimizations.failed.push('signalOptimization');
    }
  }

  /**
   * Consolidate signal light containers
   */
  consolidateSignalContainers() {
    // Check for duplicate signal containers
    const containers = document.querySelectorAll('[class*="signal"], [id*="signal"]');
    const containerMap = new Map();
    
    containers.forEach(container => {
      const key = container.className + container.id;
      if (containerMap.has(key)) {
        console.warn('[CodeOptimizer] Duplicate signal container detected:', container);
      } else {
        containerMap.set(key, container);
      }
    });
  }

  /**
   * Clean up memory leaks
   */
  async cleanupMemoryLeaks() {
    console.log('[CodeOptimizer] Cleaning up memory leaks...');
    
    try {
      // Clear old intervals and timeouts
      this.clearOldTimers();
      
      // Clean up chart instances
      this.cleanupChartInstances();
      
      // Clear cached data
      this.clearCachedData();
      
      this.optimizations.applied.push('memoryCleanup');
      
    } catch (error) {
      console.error('[CodeOptimizer] Memory cleanup failed:', error);
      this.optimizations.failed.push('memoryCleanup');
    }
  }

  /**
   * Clear old timers
   */
  clearOldTimers() {
    // This is a simplified approach - in practice, you'd track timers more systematically
    if (window.reconnectTimer) {
      clearTimeout(window.reconnectTimer);
      window.reconnectTimer = null;
    }
    
    if (window.updateTimer) {
      clearInterval(window.updateTimer);
      window.updateTimer = null;
    }
  }

  /**
   * Clean up chart instances
   */
  cleanupChartInstances() {
    if (window.chartInstances) {
      Object.values(window.chartInstances).forEach(chart => {
        try {
          if (chart && typeof chart.destroy === 'function') {
            chart.destroy();
          }
        } catch (error) {
          console.warn('[CodeOptimizer] Error destroying chart:', error);
        }
      });
      
      // Reset chart instances
      window.chartInstances = {};
    }
  }

  /**
   * Clear cached data
   */
  clearCachedData() {
    // Clear old indicator data
    if (window.indicatorsData) {
      const now = Date.now();
      const maxAge = 5 * 60 * 1000; // 5 minutes
      
      Object.keys(window.indicatorsData).forEach(pair => {
        Object.keys(window.indicatorsData[pair]).forEach(timeframe => {
          const data = window.indicatorsData[pair][timeframe];
          if (data.timestamp && (now - data.timestamp) > maxAge) {
            delete window.indicatorsData[pair][timeframe];
          }
        });
      });
    }
  }

  /**
   * Get optimization report
   */
  getOptimizationReport() {
    return {
      applied: this.optimizations.applied,
      pending: this.optimizations.pending,
      failed: this.optimizations.failed,
      redundancies: this.redundancies,
      isInitialized: this.isInitialized,
      timestamp: Date.now()
    };
  }

  /**
   * Run periodic optimization
   */
  startPeriodicOptimization(interval = 300000) { // 5 minutes
    setInterval(() => {
      console.log('[CodeOptimizer] Running periodic optimization...');
      this.cleanupMemoryLeaks();
    }, interval);
  }
}

// Create global instance
window.codeOptimizer = new CodeOptimizer();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    window.codeOptimizer.initialize();
  });
} else {
  window.codeOptimizer.initialize();
}

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CodeOptimizer;
}
