/**
 * Compact ML Controls for Ticker Container
 * Moves ML Control Center and Historical Analysis to ticker as compact toggleable buttons
 */

class CompactMLControls {
  constructor() {
    this.isMLControlOpen = false;
    this.isMLHistoryOpen = false;
    this.init();
  }

  init() {
    console.log('[CompactMLControls] Initializing compact ML controls...');
    
    try {
      this.createCompactPanels();
      this.setupEventListeners();
      this.hideOriginalPanels();
      this.applyCompactStyles();
      
      console.log('[CompactMLControls] Compact ML controls initialized successfully');
    } catch (error) {
      console.error('[CompactMLControls] Error initializing compact ML controls:', error);
    }
  }

  createCompactPanels() {
    // Create ML Control Center Panel
    this.createMLControlPanel();
    
    // Create ML Historical Analysis Panel
    this.createMLHistoryPanel();
  }

  createMLControlPanel() {
    const panel = document.createElement('div');
    panel.id = 'compactMLControlPanel';
    panel.className = 'compact-ml-panel';
    panel.style.display = 'none';
    
    panel.innerHTML = `
      <div class="compact-panel-header">
        <h3>ML Control Center</h3>
        <button class="close-compact-panel" data-panel="mlControl">×</button>
      </div>
      <div class="compact-panel-content">
        <div class="compact-control-group">
          <label>Admiral Mode</label>
          <button id="compactAdmiralToggle" class="compact-toggle">OFF</button>
        </div>
        
        <div class="compact-control-group">
          <label>Confidence Threshold</label>
          <input type="range" id="compactConfidenceSlider" min="0.5" max="0.95" step="0.05" value="0.7">
          <span id="compactConfidenceValue">70%</span>
        </div>
        
        <div class="compact-control-group">
          <label>Risk Level</label>
          <select id="compactRiskLevel">
            <option value="conservative">Conservative</option>
            <option value="medium" selected>Medium</option>
            <option value="aggressive">Aggressive</option>
            <option value="degen">Degen Mode</option>
          </select>
        </div>
        
        <div class="compact-control-group">
          <label>Time Horizon</label>
          <select id="compactTimeHorizon">
            <option value="scalp">Scalp (1-5m)</option>
            <option value="short" selected>Short (15m-1h)</option>
            <option value="medium">Medium (4h-1d)</option>
            <option value="long">Long (1d+)</option>
          </select>
        </div>
        
        <div class="compact-predictions">
          <div class="compact-prediction-box long">
            <div class="prediction-label">LONG Entry</div>
            <div class="prediction-price" id="compactLongPrice">--</div>
            <div class="prediction-confidence" id="compactLongConfidence">--</div>
          </div>
          <div class="compact-prediction-box short">
            <div class="prediction-label">SHORT Entry</div>
            <div class="prediction-price" id="compactShortPrice">--</div>
            <div class="prediction-confidence" id="compactShortConfidence">--</div>
          </div>
        </div>
        
        <div class="compact-actions">
          <button id="compactRunAnalysis" class="compact-action-btn">Run Analysis</button>
          <button id="compactResetML" class="compact-action-btn secondary">Reset</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(panel);
  }

  createMLHistoryPanel() {
    const panel = document.createElement('div');
    panel.id = 'compactMLHistoryPanel';
    panel.className = 'compact-ml-panel';
    panel.style.display = 'none';
    
    panel.innerHTML = `
      <div class="compact-panel-header">
        <h3>ML Historical Analysis</h3>
        <button class="close-compact-panel" data-panel="mlHistory">×</button>
      </div>
      <div class="compact-panel-content">
        <div class="compact-control-group">
          <label>Analysis Period</label>
          <select id="compactAnalysisPeriod">
            <option value="today">Today</option>
            <option value="yesterday">Yesterday</option>
            <option value="week" selected>Last Week</option>
            <option value="month">Last Month</option>
            <option value="all">All Time</option>
          </select>
        </div>
        
        <div class="compact-control-group">
          <label>Search Depth</label>
          <input type="range" id="compactSearchDepth" min="50" max="500" step="50" value="100">
          <span id="compactSearchDepthValue">100 points</span>
        </div>
        
        <div class="compact-fibonacci">
          <div class="fibonacci-header">
            <label>Fibonacci Levels</label>
            <button id="compactFibToggle" class="compact-toggle">ON</button>
          </div>
          <div class="fibonacci-levels" id="compactFibLevels">
            <div class="fib-level">23.6%: <span id="fib236">--</span></div>
            <div class="fib-level">38.2%: <span id="fib382">--</span></div>
            <div class="fib-level">50.0%: <span id="fib500">--</span></div>
            <div class="fib-level">61.8%: <span id="fib618">--</span></div>
          </div>
        </div>
        
        <div class="compact-convergence">
          <div class="convergence-header">
            <label>Convergence Analysis</label>
            <div class="convergence-stats">
              <span>Success Rate: <span id="compactSuccessRate">--</span></span>
              <span>Total Events: <span id="compactTotalEvents">--</span></span>
            </div>
          </div>
        </div>
        
        <div class="compact-actions">
          <button id="compactRunHistoryAnalysis" class="compact-action-btn">Analyze History</button>
          <button id="compactClearSelection" class="compact-action-btn secondary">Clear Selection</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(panel);
  }

  setupEventListeners() {
    // ML Control Center Button
    const mlControlBtn = document.getElementById('mlControlCenterButton');
    if (mlControlBtn) {
      mlControlBtn.addEventListener('click', () => this.toggleMLControl());
    }
    
    // ML Historical Button
    const mlHistoryBtn = document.getElementById('mlHistoricalButton');
    if (mlHistoryBtn) {
      mlHistoryBtn.addEventListener('click', () => this.toggleMLHistory());
    }
    
    // Close panel buttons
    document.addEventListener('click', (e) => {
      if (e.target.classList.contains('close-compact-panel')) {
        const panel = e.target.getAttribute('data-panel');
        if (panel === 'mlControl') {
          this.closeMLControl();
        } else if (panel === 'mlHistory') {
          this.closeMLHistory();
        }
      }
    });
    
    // Setup control interactions
    this.setupMLControlInteractions();
    this.setupMLHistoryInteractions();
    
    // Close panels when clicking outside
    document.addEventListener('click', (e) => {
      if (!e.target.closest('.compact-ml-panel') && 
          !e.target.closest('#mlControlCenterButton') && 
          !e.target.closest('#mlHistoricalButton')) {
        this.closeAllPanels();
      }
    });
  }

  setupMLControlInteractions() {
    // Admiral Mode Toggle
    const admiralToggle = document.getElementById('compactAdmiralToggle');
    if (admiralToggle) {
      admiralToggle.addEventListener('click', () => {
        const isOn = admiralToggle.textContent === 'ON';
        admiralToggle.textContent = isOn ? 'OFF' : 'ON';
        admiralToggle.classList.toggle('active', !isOn);
        
        // Integrate with existing Admiral mode if available
        if (window.advancedMLFeatures) {
          window.advancedMLFeatures.admiralMode = !isOn;
        }
      });
    }
    
    // Confidence Slider
    const confidenceSlider = document.getElementById('compactConfidenceSlider');
    const confidenceValue = document.getElementById('compactConfidenceValue');
    if (confidenceSlider && confidenceValue) {
      confidenceSlider.addEventListener('input', (e) => {
        const value = Math.round(e.target.value * 100);
        confidenceValue.textContent = `${value}%`;
        
        if (window.advancedMLFeatures) {
          window.advancedMLFeatures.mlOptions.confidenceThreshold = parseFloat(e.target.value);
        }
      });
    }
    
    // Risk Level
    const riskLevel = document.getElementById('compactRiskLevel');
    if (riskLevel) {
      riskLevel.addEventListener('change', (e) => {
        if (window.advancedMLFeatures) {
          window.advancedMLFeatures.mlOptions.riskLevel = e.target.value;
        }
      });
    }
    
    // Time Horizon
    const timeHorizon = document.getElementById('compactTimeHorizon');
    if (timeHorizon) {
      timeHorizon.addEventListener('change', (e) => {
        if (window.advancedMLFeatures) {
          window.advancedMLFeatures.mlOptions.timeHorizon = e.target.value;
        }
      });
    }
    
    // Run Analysis
    const runAnalysis = document.getElementById('compactRunAnalysis');
    if (runAnalysis) {
      runAnalysis.addEventListener('click', () => {
        this.runMLAnalysis();
      });
    }
    
    // Reset ML
    const resetML = document.getElementById('compactResetML');
    if (resetML) {
      resetML.addEventListener('click', () => {
        this.resetMLSettings();
      });
    }
  }

  setupMLHistoryInteractions() {
    // Search Depth Slider
    const searchDepthSlider = document.getElementById('compactSearchDepth');
    const searchDepthValue = document.getElementById('compactSearchDepthValue');
    if (searchDepthSlider && searchDepthValue) {
      searchDepthSlider.addEventListener('input', (e) => {
        searchDepthValue.textContent = `${e.target.value} points`;
        
        if (window.mlHistoricalAnalysis) {
          window.mlHistoricalAnalysis.analysisOptions.searchDepth = parseInt(e.target.value);
        }
      });
    }
    
    // Fibonacci Toggle
    const fibToggle = document.getElementById('compactFibToggle');
    if (fibToggle) {
      fibToggle.addEventListener('click', () => {
        const isOn = fibToggle.textContent === 'ON';
        fibToggle.textContent = isOn ? 'OFF' : 'ON';
        fibToggle.classList.toggle('active', !isOn);
        
        if (window.mlHistoricalAnalysis) {
          window.mlHistoricalAnalysis.analysisOptions.fibonacciEnabled = !isOn;
        }
      });
    }
    
    // Run History Analysis
    const runHistoryAnalysis = document.getElementById('compactRunHistoryAnalysis');
    if (runHistoryAnalysis) {
      runHistoryAnalysis.addEventListener('click', () => {
        this.runHistoryAnalysis();
      });
    }
    
    // Clear Selection
    const clearSelection = document.getElementById('compactClearSelection');
    if (clearSelection) {
      clearSelection.addEventListener('click', () => {
        if (window.mlHistoricalAnalysis) {
          window.mlHistoricalAnalysis.clearAllSelections();
        }
      });
    }
  }

  toggleMLControl() {
    if (this.isMLControlOpen) {
      this.closeMLControl();
    } else {
      this.closeAllPanels();
      this.openMLControl();
    }
  }

  toggleMLHistory() {
    if (this.isMLHistoryOpen) {
      this.closeMLHistory();
    } else {
      this.closeAllPanels();
      this.openMLHistory();
    }
  }

  openMLControl() {
    const panel = document.getElementById('compactMLControlPanel');
    if (panel) {
      panel.style.display = 'block';
      this.positionPanel(panel, 'mlControlCenterButton');
      this.isMLControlOpen = true;
      
      // Update button state
      const btn = document.getElementById('mlControlCenterButton');
      if (btn) btn.classList.add('active');
    }
  }

  openMLHistory() {
    const panel = document.getElementById('compactMLHistoryPanel');
    if (panel) {
      panel.style.display = 'block';
      this.positionPanel(panel, 'mlHistoricalButton');
      this.isMLHistoryOpen = true;
      
      // Update button state
      const btn = document.getElementById('mlHistoricalButton');
      if (btn) btn.classList.add('active');
    }
  }

  closeMLControl() {
    const panel = document.getElementById('compactMLControlPanel');
    if (panel) {
      panel.style.display = 'none';
      this.isMLControlOpen = false;
      
      // Update button state
      const btn = document.getElementById('mlControlCenterButton');
      if (btn) btn.classList.remove('active');
    }
  }

  closeMLHistory() {
    const panel = document.getElementById('compactMLHistoryPanel');
    if (panel) {
      panel.style.display = 'none';
      this.isMLHistoryOpen = false;
      
      // Update button state
      const btn = document.getElementById('mlHistoricalButton');
      if (btn) btn.classList.remove('active');
    }
  }

  closeAllPanels() {
    this.closeMLControl();
    this.closeMLHistory();
  }

  positionPanel(panel, buttonId) {
    const button = document.getElementById(buttonId);
    if (!button || !panel) return;
    
    const buttonRect = button.getBoundingClientRect();
    const tickerContainer = document.getElementById('tickerContainer');
    const containerRect = tickerContainer ? tickerContainer.getBoundingClientRect() : { right: window.innerWidth };
    
    // Position panel to the right of ticker container
    panel.style.position = 'fixed';
    panel.style.top = `${buttonRect.bottom + 5}px`;
    panel.style.left = `${containerRect.right + 10}px`;
    panel.style.zIndex = '10000';
    
    // Ensure panel doesn't go off screen
    const panelRect = panel.getBoundingClientRect();
    if (panelRect.right > window.innerWidth) {
      panel.style.left = `${window.innerWidth - panelRect.width - 10}px`;
    }
    if (panelRect.bottom > window.innerHeight) {
      panel.style.top = `${window.innerHeight - panelRect.height - 10}px`;
    }
  }

  hideOriginalPanels() {
    // Hide the original large ML panels at bottom of page
    const originalMLControl = document.querySelector('.ml-advanced-options');
    if (originalMLControl) {
      originalMLControl.style.display = 'none';
    }
    
    const originalMLHistory = document.querySelector('.ml-historical-analysis');
    if (originalMLHistory) {
      originalMLHistory.style.display = 'none';
    }
  }

  runMLAnalysis() {
    console.log('[CompactMLControls] Running ML analysis...');
    
    // Integrate with existing ML features
    if (window.advancedMLFeatures) {
      const currentData = {
        currentPrice: window.currentPrice || 0,
        atr: window.indicatorsData?.[window.currentTf]?.atr?.value || 0,
        rsi: window.indicatorsData?.[window.currentTf]?.rsi?.value || 50,
        macd: window.indicatorsData?.[window.currentTf]?.macd || { macd: 0, signal: 0 }
      };
      
      const longEntry = window.advancedMLFeatures.calculateLongEntry(currentData);
      const shortEntry = window.advancedMLFeatures.calculateShortEntry(currentData);
      
      // Update compact display
      document.getElementById('compactLongPrice').textContent = `$${longEntry.price.toFixed(2)}`;
      document.getElementById('compactLongConfidence').textContent = `${(longEntry.confidence * 100).toFixed(1)}%`;
      document.getElementById('compactShortPrice').textContent = `$${shortEntry.price.toFixed(2)}`;
      document.getElementById('compactShortConfidence').textContent = `${(shortEntry.confidence * 100).toFixed(1)}%`;
    }
  }

  runHistoryAnalysis() {
    console.log('[CompactMLControls] Running historical analysis...');
    
    if (window.mlHistoricalAnalysis) {
      const period = document.getElementById('compactAnalysisPeriod').value;
      const results = window.mlHistoricalAnalysis.runConvergenceAnalysis(period);
      
      // Update compact display
      document.getElementById('compactSuccessRate').textContent = `${(results.successRate * 100).toFixed(1)}%`;
      document.getElementById('compactTotalEvents').textContent = results.totalEvents;
      
      // Update Fibonacci levels if enabled
      if (window.mlHistoricalAnalysis.analysisOptions.fibonacciEnabled) {
        const fibLevels = window.mlHistoricalAnalysis.calculateFibonacciLevels();
        document.getElementById('fib236').textContent = `$${fibLevels['23.6'].toFixed(2)}`;
        document.getElementById('fib382').textContent = `$${fibLevels['38.2'].toFixed(2)}`;
        document.getElementById('fib500').textContent = `$${fibLevels['50.0'].toFixed(2)}`;
        document.getElementById('fib618').textContent = `$${fibLevels['61.8'].toFixed(2)}`;
      }
    }
  }

  resetMLSettings() {
    // Reset all ML settings to defaults
    document.getElementById('compactConfidenceSlider').value = 0.7;
    document.getElementById('compactConfidenceValue').textContent = '70%';
    document.getElementById('compactRiskLevel').value = 'medium';
    document.getElementById('compactTimeHorizon').value = 'short';
    document.getElementById('compactAdmiralToggle').textContent = 'OFF';
    document.getElementById('compactAdmiralToggle').classList.remove('active');
    
    // Clear predictions
    document.getElementById('compactLongPrice').textContent = '--';
    document.getElementById('compactLongConfidence').textContent = '--';
    document.getElementById('compactShortPrice').textContent = '--';
    document.getElementById('compactShortConfidence').textContent = '--';
  }

  applyCompactStyles() {
    const style = document.createElement('style');
    style.textContent = `
      /* Compact ML Panel Styles */
      .compact-ml-panel {
        position: fixed;
        background: rgba(0, 20, 40, 0.95);
        border: 2px solid rgba(0, 255, 255, 0.5);
        border-radius: 8px;
        padding: 0;
        min-width: 280px;
        max-width: 320px;
        box-shadow: 0 8px 32px rgba(0, 255, 255, 0.3);
        backdrop-filter: blur(10px);
        z-index: 10000;
      }
      
      .compact-panel-header {
        background: rgba(0, 255, 255, 0.1);
        padding: 8px 12px;
        border-bottom: 1px solid rgba(0, 255, 255, 0.3);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .compact-panel-header h3 {
        margin: 0;
        color: #00ffff;
        font-size: 14px;
        font-weight: bold;
      }
      
      .close-compact-panel {
        background: none;
        border: none;
        color: #ff6b6b;
        font-size: 18px;
        cursor: pointer;
        padding: 0;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .close-compact-panel:hover {
        color: #ff4757;
        background: rgba(255, 107, 107, 0.1);
        border-radius: 50%;
      }
      
      .compact-panel-content {
        padding: 12px;
        max-height: 400px;
        overflow-y: auto;
      }
      
      .compact-control-group {
        margin-bottom: 12px;
      }
      
      .compact-control-group label {
        display: block;
        color: #00ffff;
        font-size: 12px;
        margin-bottom: 4px;
        font-weight: bold;
      }
      
      .compact-toggle {
        background: rgba(255, 107, 107, 0.2);
        border: 1px solid #ff6b6b;
        color: #ff6b6b;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 11px;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      
      .compact-toggle.active {
        background: rgba(0, 255, 0, 0.2);
        border-color: #00ff00;
        color: #00ff00;
      }
      
      .compact-control-group input[type="range"] {
        width: 100%;
        margin: 4px 0;
      }
      
      .compact-control-group select {
        width: 100%;
        background: rgba(0, 50, 100, 0.3);
        border: 1px solid rgba(0, 255, 255, 0.3);
        color: #00ffff;
        padding: 4px;
        border-radius: 4px;
        font-size: 12px;
      }
      
      .compact-predictions {
        display: flex;
        gap: 8px;
        margin: 12px 0;
      }
      
      .compact-prediction-box {
        flex: 1;
        background: rgba(0, 50, 100, 0.2);
        border-radius: 6px;
        padding: 8px;
        text-align: center;
      }
      
      .compact-prediction-box.long {
        border: 1px solid rgba(0, 255, 0, 0.3);
      }
      
      .compact-prediction-box.short {
        border: 1px solid rgba(255, 107, 107, 0.3);
      }
      
      .prediction-label {
        font-size: 10px;
        color: #888;
        margin-bottom: 4px;
      }
      
      .prediction-price {
        font-size: 12px;
        font-weight: bold;
        color: #00ffff;
        margin-bottom: 2px;
      }
      
      .prediction-confidence {
        font-size: 10px;
        color: #ffa502;
      }
      
      .compact-actions {
        display: flex;
        gap: 8px;
        margin-top: 12px;
      }
      
      .compact-action-btn {
        flex: 1;
        background: rgba(0, 255, 255, 0.1);
        border: 1px solid rgba(0, 255, 255, 0.3);
        color: #00ffff;
        padding: 6px 8px;
        border-radius: 4px;
        font-size: 11px;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      
      .compact-action-btn:hover {
        background: rgba(0, 255, 255, 0.2);
        border-color: #00ffff;
      }
      
      .compact-action-btn.secondary {
        background: rgba(128, 128, 128, 0.1);
        border-color: rgba(128, 128, 128, 0.3);
        color: #888;
      }
      
      .compact-action-btn.secondary:hover {
        background: rgba(128, 128, 128, 0.2);
        border-color: #888;
      }
      
      .fibonacci-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 6px;
      }
      
      .fibonacci-levels {
        background: rgba(0, 30, 60, 0.3);
        border-radius: 4px;
        padding: 6px;
      }
      
      .fib-level {
        display: flex;
        justify-content: space-between;
        font-size: 11px;
        color: #feca57;
        margin-bottom: 2px;
      }
      
      .convergence-header {
        margin-bottom: 6px;
      }
      
      .convergence-stats {
        display: flex;
        justify-content: space-between;
        font-size: 11px;
        color: #96ceb4;
      }
      
      /* Button active states */
      #mlControlCenterButton.active,
      #mlHistoricalButton.active {
        background: rgba(0, 255, 255, 0.2) !important;
        border-color: #00ffff !important;
        color: #00ffff !important;
      }
    `;
    document.head.appendChild(style);
  }
}

// Initialize compact ML controls
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.compactMLControls = new CompactMLControls();
  }, 3000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CompactMLControls;
}
