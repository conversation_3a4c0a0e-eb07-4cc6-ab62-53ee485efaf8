/**
 * StarCrypt Unified Initialization System
 * Single entry point for all application initialization
 */

(function() {
  'use strict';

  // Prevent multiple initialization
  if (window.starCryptInitialized) {
    console.log('[StarCrypt Init] Already initialized, skipping...');
    return;
  }

  console.log('[StarCrypt Init] Starting StarCrypt Enterprise initialization...');

  // Configuration for initialization
  const INIT_CONFIG = {
    timeout: 30000, // 30 seconds timeout
    retryAttempts: 3,
    retryDelay: 2000, // 2 seconds between retries
    debug: window.DEBUG_MODE || false
  };

  // Initialization state
  const initState = {
    startTime: Date.now(),
    attempts: 0,
    errors: [],
    completed: false
  };

  /**
   * Main initialization function
   */
  async function initializeStarCrypt() {
    try {
      initState.attempts++;
      console.log(`[StarCrypt Init] Initialization attempt ${initState.attempts}`);

      // Step 1: Wait for DOM to be ready
      await waitForDOM();

      // Step 2: Clean up legacy components
      cleanupLegacyComponents();

      // Step 3: Initialize core system
      await initializeCore();

      // Step 4: Initialize UI components
      await initializeUI();

      // Step 5: Start services
      await startServices();

      // Step 6: Final setup
      finalSetup();

      // Mark as completed
      initState.completed = true;
      window.starCryptInitialized = true;

      const duration = Date.now() - initState.startTime;
      console.log(`[StarCrypt Init] Initialization completed successfully in ${duration}ms`);

      // Dispatch initialization complete event
      document.dispatchEvent(new CustomEvent('starcrypt:initialized', {
        detail: {
          duration,
          attempts: initState.attempts,
          timestamp: Date.now()
        }
      }));

    } catch (error) {
      console.error('[StarCrypt Init] Initialization failed:', error);
      initState.errors.push(error);

      // Retry if attempts remaining
      if (initState.attempts < INIT_CONFIG.retryAttempts) {
        console.log(`[StarCrypt Init] Retrying in ${INIT_CONFIG.retryDelay}ms...`);
        setTimeout(() => {
          initializeStarCrypt();
        }, INIT_CONFIG.retryDelay);
      } else {
        console.error('[StarCrypt Init] Max retry attempts reached, initialization failed');
        
        // Dispatch initialization failed event
        document.dispatchEvent(new CustomEvent('starcrypt:initializationFailed', {
          detail: {
            errors: initState.errors,
            attempts: initState.attempts,
            timestamp: Date.now()
          }
        }));
      }
    }
  }

  /**
   * Wait for DOM to be ready
   */
  function waitForDOM() {
    return new Promise((resolve) => {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', resolve);
      } else {
        resolve();
      }
    });
  }

  /**
   * Clean up legacy components and conflicting initializations
   */
  function cleanupLegacyComponents() {
    console.log('[StarCrypt Init] Cleaning up legacy components...');

    // Remove legacy WebSocket instances
    const legacyWebSocketVars = [
      'wsManager',
      'wsProcessor', 
      'WebSocketManager',
      'WebSocketProcessorV2',
      'webSocketManager',
      'websocketProcessor'
    ];

    legacyWebSocketVars.forEach(varName => {
      if (window[varName]) {
        try {
          if (typeof window[varName].disconnect === 'function') {
            window[varName].disconnect();
          }
          if (typeof window[varName].destroy === 'function') {
            window[varName].destroy();
          }
          delete window[varName];
        } catch (error) {
          console.warn(`[StarCrypt Init] Error cleaning up ${varName}:`, error);
        }
      }
    });

    // Remove legacy strategy managers
    const legacyStrategyVars = [
      'StrategyManager',
      'strategyManager',
      'strategyCore'
    ];

    legacyStrategyVars.forEach(varName => {
      if (window[varName] && window[varName] !== window.starCryptCore?.modules?.strategy) {
        try {
          if (typeof window[varName].destroy === 'function') {
            window[varName].destroy();
          }
          delete window[varName];
        } catch (error) {
          console.warn(`[StarCrypt Init] Error cleaning up ${varName}:`, error);
        }
      }
    });

    // Remove legacy signal processors
    const legacySignalVars = [
      'SignalLightsManager',
      'signalLightsManager',
      'SignalMatrix',
      'signalMatrix',
      'signalProcessor'
    ];

    legacySignalVars.forEach(varName => {
      if (window[varName] && window[varName] !== window.starCryptCore?.modules?.signal) {
        try {
          if (typeof window[varName].destroy === 'function') {
            window[varName].destroy();
          }
          delete window[varName];
        } catch (error) {
          console.warn(`[StarCrypt Init] Error cleaning up ${varName}:`, error);
        }
      }
    });

    console.log('[StarCrypt Init] Legacy cleanup completed');
  }

  /**
   * Initialize core system
   */
  async function initializeCore() {
    console.log('[StarCrypt Init] Initializing core system...');

    // Initialize StarCrypt Core (this will initialize all core modules)
    if (window.getStarCryptCore) {
      const core = window.getStarCryptCore({
        debug: INIT_CONFIG.debug
      });

      // Wait for core to be ready
      await core.waitForReady();

      // Expose core modules globally for backward compatibility
      window.configCore = core.getModule('config');
      window.wsCore = core.getModule('websocket');
      window.strategyCore = core.getModule('strategy');
      window.signalCore = core.getModule('signal');

      console.log('[StarCrypt Init] Core system initialized');
    } else {
      throw new Error('StarCrypt Core not available');
    }
  }

  /**
   * Initialize UI components
   */
  async function initializeUI() {
    console.log('[StarCrypt Init] Initializing UI components...');

    // Initialize theme manager early to prevent FOUC
    if (window.ThemeManager) {
      const savedTheme = window.configCore?.get('ui.theme') || 
                        localStorage.getItem('theme') || 
                        'dark';
      window.ThemeManager.init(savedTheme);
    }

    // Initialize volume status
    if (window.VolumeIndicatorUpdater) {
      const volumeIndicatorUpdater = new window.VolumeIndicatorUpdater();
      volumeIndicatorUpdater.init();
    }

    // Initialize consolidated UI components
    const uiComponents = [
      'SignalLights',
      'TimeframeUI',
      'VolumeIndicatorUpdater',
      'SignalMatrix'
    ];

    for (const componentName of uiComponents) {
      if (window[componentName]) {
        try {
          if (typeof window[componentName].init === 'function') {
            window[componentName].init();
          } else if (typeof window[componentName] === 'function') {
            new window[componentName]();
          }
        } catch (error) {
          console.warn(`[StarCrypt Init] Failed to initialize ${componentName}:`, error);
        }
      }
    }

    console.log('[StarCrypt Init] UI components initialized');
  }

  /**
   * Start services
   */
  async function startServices() {
    console.log('[StarCrypt Init] Starting services...');

    // Start WebSocket connection
    if (window.wsCore) {
      try {
        await window.wsCore.connect();
        console.log('[StarCrypt Init] WebSocket connected');
      } catch (error) {
        console.warn('[StarCrypt Init] WebSocket connection failed, will retry automatically:', error);
      }
    }

    // Initialize AI services if available
    if (window.aiIntegrationService) {
      try {
        await window.aiIntegrationService.init();
        console.log('[StarCrypt Init] AI services initialized');
      } catch (error) {
        console.warn('[StarCrypt Init] AI services initialization failed:', error);
      }
    }

    // Initialize ML components if available
    if (window.MLVisualization) {
      try {
        window.MLVisualization.init();
        console.log('[StarCrypt Init] ML visualization initialized');
      } catch (error) {
        console.warn('[StarCrypt Init] ML visualization initialization failed:', error);
      }
    }

    console.log('[StarCrypt Init] Services started');
  }

  /**
   * Final setup tasks
   */
  function finalSetup() {
    console.log('[StarCrypt Init] Performing final setup...');

    // Set up global event listeners
    setupGlobalEventListeners();

    // Initialize TradingView widget if container exists
    initializeTradingView();

    // Set up periodic health checks
    setupHealthChecks();

    // Hide loading overlay if it exists
    hideLoadingOverlay();

    console.log('[StarCrypt Init] Final setup completed');
  }

  /**
   * Set up global event listeners
   */
  function setupGlobalEventListeners() {
    // Listen for WebSocket events
    document.addEventListener('starcrypt:websocket:connected', () => {
      console.log('[StarCrypt Init] WebSocket connected');
      updateConnectionStatus(true);
    });

    document.addEventListener('starcrypt:websocket:disconnected', () => {
      console.log('[StarCrypt Init] WebSocket disconnected');
      updateConnectionStatus(false);
    });

    // Listen for strategy changes
    document.addEventListener('starcrypt:strategy:strategyChanged', (event) => {
      console.log('[StarCrypt Init] Strategy changed:', event.detail);
    });

    // Listen for configuration changes
    document.addEventListener('starcrypt:config:configChanged', (event) => {
      console.log('[StarCrypt Init] Configuration changed:', event.detail);
    });
  }

  /**
   * Update connection status indicator
   */
  function updateConnectionStatus(connected) {
    const indicator = document.querySelector('.connection-status');
    if (indicator) {
      indicator.classList.toggle('connected', connected);
      indicator.classList.toggle('disconnected', !connected);
      indicator.title = connected ? 'Connected' : 'Disconnected';
    }
  }

  /**
   * Initialize TradingView widget
   */
  function initializeTradingView() {
    const container = document.getElementById('tradingview_widget');
    if (container && window.TradingView) {
      try {
        // Initialize TradingView widget
        if (window.initializeTradingView) {
          window.initializeTradingView();
        }
      } catch (error) {
        console.warn('[StarCrypt Init] TradingView initialization failed:', error);
      }
    }
  }

  /**
   * Set up periodic health checks
   */
  function setupHealthChecks() {
    setInterval(() => {
      if (window.starCryptCore) {
        const stats = window.starCryptCore.getStats();
        
        // Check for any issues
        if (!stats.modules.websocket?.isConnected) {
          console.warn('[StarCrypt Health] WebSocket not connected');
        }
        
        if (INIT_CONFIG.debug) {
          console.log('[StarCrypt Health] System stats:', stats);
        }
      }
    }, 60000); // Check every minute
  }

  /**
   * Hide loading overlay
   */
  function hideLoadingOverlay() {
    const overlay = document.querySelector('.loading-overlay');
    if (overlay) {
      overlay.style.opacity = '0';
      setTimeout(() => {
        overlay.style.display = 'none';
      }, 500);
    }
  }

  /**
   * Set up initialization timeout
   */
  setTimeout(() => {
    if (!initState.completed) {
      console.error('[StarCrypt Init] Initialization timeout reached');
      
      // Dispatch timeout event
      document.dispatchEvent(new CustomEvent('starcrypt:initializationTimeout', {
        detail: {
          timeout: INIT_CONFIG.timeout,
          attempts: initState.attempts,
          errors: initState.errors
        }
      }));
    }
  }, INIT_CONFIG.timeout);

  // Start initialization
  initializeStarCrypt();

})();
