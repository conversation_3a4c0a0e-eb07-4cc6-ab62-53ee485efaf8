/**
 * Tooltip Fixes for StarCrypt
 * Removes duplicate tooltips, fixes cursor issues, ensures proper z-index
 */

class TooltipFixes {
  constructor() {
    this.activeTooltip = null;
    this.tooltipTimeout = null;
    this.init();
  }

  init() {
    console.log('[TooltipFixes] Initializing tooltip fixes...');
    
    try {
      this.removeDefaultTooltips();
      this.fixCursorStyles();
      this.enhanceTooltipSystem();
      this.applyTooltipStyles();
      
      console.log('[TooltipFixes] Tooltip fixes applied successfully');
    } catch (error) {
      console.error('[TooltipFixes] Error applying tooltip fixes:', error);
    }
  }

  removeDefaultTooltips() {
    // Remove any existing title attributes that cause default browser tooltips
    document.addEventListener('DOMContentLoaded', () => {
      this.cleanupTooltips();
    });
    
    // Also clean up periodically for dynamically added elements
    setInterval(() => {
      this.cleanupTooltips();
    }, 5000);
  }

  cleanupTooltips() {
    // Remove title attributes from signal lights to prevent duplicate tooltips
    const signalLights = document.querySelectorAll('.signal-circle, .circle');
    signalLights.forEach(light => {
      if (light.hasAttribute('title')) {
        // Move title to data-tooltip if it doesn't exist
        if (!light.hasAttribute('data-tooltip')) {
          light.setAttribute('data-tooltip', light.getAttribute('title'));
        }
        light.removeAttribute('title');
      }
    });

    // Remove title attributes from other elements that might cause conflicts
    const elementsWithTitles = document.querySelectorAll('[title]');
    elementsWithTitles.forEach(element => {
      // Skip elements that should keep their titles (like buttons with specific functionality)
      if (!element.classList.contains('keep-title') && 
          !element.hasAttribute('data-keep-title')) {
        
        // For signal lights and indicators, move to data-tooltip
        if (element.classList.contains('signal-circle') || 
            element.classList.contains('circle') ||
            element.classList.contains('indicator-item')) {
          
          if (!element.hasAttribute('data-tooltip')) {
            element.setAttribute('data-tooltip', element.getAttribute('title'));
          }
          element.removeAttribute('title');
        }
      }
    });
  }

  fixCursorStyles() {
    const style = document.createElement('style');
    style.textContent = `
      /* Fix cursor styles for signal lights */
      .signal-circle,
      .circle {
        cursor: default !important; /* Remove help cursor */
      }
      
      .signal-circle:hover,
      .circle:hover {
        cursor: default !important;
      }
      
      /* Ensure interactive elements have proper cursors */
      .menu-button,
      .strategy-button,
      .admiral-mode-toggle,
      button {
        cursor: pointer !important;
      }
      
      /* Remove question mark cursor from tooltipped elements */
      [data-tooltip] {
        cursor: default !important;
      }
      
      [data-tooltip]:hover {
        cursor: default !important;
      }
      
      /* Specific fixes for signal lights */
      .momentum-indicators .signal-circle,
      .momentum-indicators .circle {
        cursor: default !important;
      }
      
      /* Ensure sliders and interactive controls keep pointer cursor */
      input[type="range"],
      .threshold-slider,
      .slider-thumb {
        cursor: pointer !important;
      }
    `;
    document.head.appendChild(style);
  }

  enhanceTooltipSystem() {
    // Create enhanced tooltip container if it doesn't exist
    let tooltip = document.getElementById('global-tooltip');
    if (!tooltip) {
      tooltip = document.createElement('div');
      tooltip.id = 'global-tooltip';
      tooltip.className = 'enhanced-tooltip';
      document.body.appendChild(tooltip);
    }

    // Remove existing tooltip event listeners to prevent conflicts
    this.removeExistingTooltipListeners();

    // Add enhanced tooltip event listeners
    this.addEnhancedTooltipListeners(tooltip);
  }

  removeExistingTooltipListeners() {
    // Clone and replace elements to remove all event listeners
    const elementsWithTooltips = document.querySelectorAll('[data-tooltip]');
    elementsWithTooltips.forEach(element => {
      // Remove any existing mouseover/mouseout listeners by cloning
      const newElement = element.cloneNode(true);
      element.parentNode.replaceChild(newElement, element);
    });
  }

  addEnhancedTooltipListeners(tooltip) {
    // Use event delegation for better performance and to handle dynamic elements
    document.addEventListener('mouseover', (e) => {
      const target = e.target.closest('[data-tooltip]');
      if (target) {
        this.showTooltip(target, tooltip, e);
      }
    });

    document.addEventListener('mouseout', (e) => {
      const target = e.target.closest('[data-tooltip]');
      if (target) {
        this.hideTooltip(tooltip);
      }
    });

    document.addEventListener('mousemove', (e) => {
      const target = e.target.closest('[data-tooltip]');
      if (target && tooltip.classList.contains('visible')) {
        this.positionTooltip(tooltip, e);
      }
    });

    // Hide tooltip when scrolling or clicking
    document.addEventListener('scroll', () => {
      this.hideTooltip(tooltip);
    });

    document.addEventListener('click', () => {
      this.hideTooltip(tooltip);
    });
  }

  showTooltip(element, tooltip, event) {
    // Clear any existing timeout
    if (this.tooltipTimeout) {
      clearTimeout(this.tooltipTimeout);
    }

    const tooltipText = element.getAttribute('data-tooltip');
    if (!tooltipText || tooltipText.trim() === '') return;

    // Enhanced tooltip content for signal lights
    let enhancedContent = tooltipText;
    
    if (element.classList.contains('signal-circle') || element.classList.contains('circle')) {
      const indicator = element.getAttribute('data-ind');
      const timeframe = element.getAttribute('data-tf');
      const color = element.style.backgroundColor || '#808080';
      
      if (indicator && timeframe) {
        // Get current indicator data for enhanced tooltip
        const indicatorData = window.indicatorsData?.[timeframe]?.[indicator];
        if (indicatorData) {
          enhancedContent = this.generateEnhancedTooltip(indicator, timeframe, indicatorData, color);
        }
      }
    }

    tooltip.innerHTML = enhancedContent;
    tooltip.classList.add('visible');
    tooltip.style.opacity = '1';
    
    this.positionTooltip(tooltip, event);
  }

  hideTooltip(tooltip) {
    // Add small delay to prevent flickering
    this.tooltipTimeout = setTimeout(() => {
      tooltip.style.opacity = '0';
      tooltip.classList.remove('visible');
    }, 100);
  }

  positionTooltip(tooltip, event) {
    const tooltipRect = tooltip.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    let x = event.clientX + 15;
    let y = event.clientY - 10;
    
    // Adjust if tooltip would go off screen
    if (x + tooltipRect.width > viewportWidth) {
      x = event.clientX - tooltipRect.width - 15;
    }
    
    if (y + tooltipRect.height > viewportHeight) {
      y = event.clientY - tooltipRect.height - 10;
    }
    
    // Ensure tooltip doesn't go above viewport
    if (y < 0) {
      y = event.clientY + 15;
    }
    
    // Ensure tooltip doesn't go left of viewport
    if (x < 0) {
      x = 10;
    }
    
    tooltip.style.left = `${x}px`;
    tooltip.style.top = `${y}px`;
  }

  generateEnhancedTooltip(indicator, timeframe, data, color) {
    const colorName = this.getColorName(color);
    const signal = this.getSignalStrength(colorName);
    
    let content = `
      <div class="tooltip-header">
        <span class="tooltip-indicator">${indicator.toUpperCase()}</span>
        <span class="tooltip-timeframe">${timeframe}</span>
      </div>
      <div class="tooltip-signal">
        <span class="tooltip-color" style="background-color: ${color}"></span>
        <span class="tooltip-signal-text">${signal}</span>
      </div>
    `;
    
    // Add specific indicator information
    if (data.value !== undefined) {
      content += `<div class="tooltip-value">Value: ${typeof data.value === 'number' ? data.value.toFixed(2) : data.value}</div>`;
    }
    
    // Add interpretation based on indicator type
    const interpretation = this.getIndicatorInterpretation(indicator, data, colorName);
    if (interpretation) {
      content += `<div class="tooltip-interpretation">${interpretation}</div>`;
    }
    
    return content;
  }

  getColorName(color) {
    const rgb = color.toLowerCase();
    if (rgb.includes('255, 0, 0') || rgb.includes('#ff0000') || rgb.includes('red')) return 'red';
    if (rgb.includes('255, 165, 0') || rgb.includes('#ffa500') || rgb.includes('orange')) return 'orange';
    if (rgb.includes('128, 128, 128') || rgb.includes('#808080') || rgb.includes('gray')) return 'grey';
    if (rgb.includes('0, 0, 255') || rgb.includes('#0000ff') || rgb.includes('blue')) return 'blue';
    if (rgb.includes('0, 255, 0') || rgb.includes('#00ff00') || rgb.includes('green')) return 'green';
    return 'neutral';
  }

  getSignalStrength(colorName) {
    switch (colorName) {
      case 'red': return 'Strong Sell Signal';
      case 'orange': return 'Mild Sell Signal';
      case 'grey': return 'Neutral Signal';
      case 'blue': return 'Mild Buy Signal';
      case 'green': return 'Strong Buy Signal';
      default: return 'Unknown Signal';
    }
  }

  getIndicatorInterpretation(indicator, data, colorName) {
    switch (indicator) {
      case 'rsi':
        if (data.value > 70) return 'Overbought - potential sell opportunity';
        if (data.value < 30) return 'Oversold - potential buy opportunity';
        return 'Neutral territory';
        
      case 'macd':
        if (data.macd > data.signal) return 'Bullish momentum';
        if (data.macd < data.signal) return 'Bearish momentum';
        return 'Momentum neutral';
        
      case 'bollingerBands':
        if (data.position > 80) return 'Price near upper band - resistance level';
        if (data.position < 20) return 'Price near lower band - support level';
        return 'Price in middle range';
        
      case 'volume':
        if (colorName === 'green' || colorName === 'blue') return 'High volume - strong interest';
        if (colorName === 'red' || colorName === 'orange') return 'Low volume - weak interest';
        return 'Average volume';
        
      default:
        return null;
    }
  }

  applyTooltipStyles() {
    const style = document.createElement('style');
    style.textContent = `
      /* Enhanced Tooltip Styles */
      .enhanced-tooltip {
        position: fixed;
        background: rgba(0, 20, 40, 0.95);
        border: 2px solid rgba(0, 255, 255, 0.6);
        border-radius: 8px;
        padding: 8px 12px;
        color: #ffffff;
        font-family: 'Orbitron', sans-serif;
        font-size: 12px;
        max-width: 250px;
        z-index: 999999 !important; /* Ensure it's above everything */
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 255, 255, 0.3);
      }
      
      .enhanced-tooltip.visible {
        opacity: 1;
      }
      
      .tooltip-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        border-bottom: 1px solid rgba(0, 255, 255, 0.3);
        padding-bottom: 4px;
      }
      
      .tooltip-indicator {
        font-weight: bold;
        color: #00ffff;
        font-size: 13px;
      }
      
      .tooltip-timeframe {
        background: rgba(0, 255, 255, 0.2);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
        color: #00ffff;
      }
      
      .tooltip-signal {
        display: flex;
        align-items: center;
        margin: 4px 0;
      }
      
      .tooltip-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 6px;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }
      
      .tooltip-signal-text {
        font-weight: bold;
        font-size: 11px;
      }
      
      .tooltip-value {
        color: #ffa502;
        font-size: 11px;
        margin: 2px 0;
      }
      
      .tooltip-interpretation {
        color: #96ceb4;
        font-size: 10px;
        font-style: italic;
        margin-top: 4px;
        border-top: 1px solid rgba(0, 255, 255, 0.2);
        padding-top: 4px;
      }
      
      /* Hide default browser tooltips */
      [title] {
        position: relative;
      }
      
      [title]:hover::after {
        display: none !important;
      }
      
      /* Ensure signal lights don't show default tooltips */
      .signal-circle[title],
      .circle[title] {
        title: none !important;
      }
      
      /* Fix z-index conflicts */
      .enhanced-tooltip {
        z-index: 999999 !important;
      }
      
      /* Ensure tooltip appears above light logic overlays */
      .light-logic-overlay {
        z-index: 1000 !important;
      }
      
      .enhanced-tooltip {
        z-index: 1001 !important;
      }
    `;
    document.head.appendChild(style);
  }
}

// Initialize tooltip fixes
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    window.tooltipFixes = new TooltipFixes();
  }, 1000);
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TooltipFixes;
}
