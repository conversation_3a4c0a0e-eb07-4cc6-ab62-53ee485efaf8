/**
 * StarCrypt Starfield Animation
 * Futuristic light-speed star field background animation
 * Reactive to ML buy/sell signals with comets/meteors
 */

class StarfieldAnimation {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.stars = [];
    this.comets = [];
    this.meteors = [];
    this.animationId = null;
    this.isRunning = false;
    
    // Configuration - Enhanced for maximum degenism
    this.config = {
      starCount: 300,
      maxSpeed: 15,
      minSpeed: 0.8,
      starColors: ['#FFFFFF', '#00FFFF', '#0088FF', '#FFD700', '#FF6B6B', '#00FF00', '#FF00FF', '#FFAA00'],
      cometColors: {
        buy: ['#00FF00', '#00FFAA', '#AAFFAA', '#00FF88', '#88FFAA'],
        sell: ['#FF0000', '#FF6666', '#FFAAAA', '#FF4444', '#FF8888'],
        neutral: ['#00FFFF', '#AAFFFF', '#FFFFFF', '#88AAFF', '#CCDDFF']
      },
      meteorFrequency: 0.03, // Increased frequency for more action
      cometFrequency: 0.015,
      trailLength: 30,
      glowIntensity: 1.2,
      warpSpeed: false,
      hyperSpace: false,
      quantumBurst: false,
      degenMode: true // Maximum visual chaos
    };
    
    this.mlSignalState = 'neutral'; // 'buy', 'sell', 'neutral'
    this.signalIntensity = 0.5; // 0-1 scale
    
    this.init();
  }
  
  init() {
    this.createCanvas();
    this.generateStars();
    this.bindEvents();
    this.start();
  }
  
  createCanvas() {
    // Create canvas element
    this.canvas = document.createElement('canvas');
    this.canvas.id = 'starfield-canvas';
    this.canvas.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      pointer-events: none;
      background: radial-gradient(ellipse at center, #0a0a1a 0%, #000000 100%);
    `;
    
    // Insert as first child of body to be behind everything
    document.body.insertBefore(this.canvas, document.body.firstChild);
    
    this.ctx = this.canvas.getContext('2d');
    this.resizeCanvas();
  }
  
  resizeCanvas() {
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
  }
  
  generateStars() {
    this.stars = [];
    for (let i = 0; i < this.config.starCount; i++) {
      this.stars.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        z: Math.random() * 1000,
        speed: Math.random() * (this.config.maxSpeed - this.config.minSpeed) + this.config.minSpeed,
        color: this.config.starColors[Math.floor(Math.random() * this.config.starColors.length)],
        brightness: Math.random() * 0.8 + 0.2,
        twinkle: Math.random() * Math.PI * 2
      });
    }
  }
  
  createComet(type = 'neutral') {
    const colors = this.config.cometColors[type];
    return {
      x: Math.random() * this.canvas.width,
      y: -50,
      vx: (Math.random() - 0.5) * 4,
      vy: Math.random() * 8 + 5,
      size: Math.random() * 3 + 2,
      color: colors[Math.floor(Math.random() * colors.length)],
      trail: [],
      life: 1.0,
      type: type
    };
  }
  
  createMeteor() {
    return {
      x: Math.random() * this.canvas.width,
      y: -50,
      vx: (Math.random() - 0.5) * 6,
      vy: Math.random() * 12 + 8,
      size: Math.random() * 5 + 3,
      color: '#FFD700',
      trail: [],
      life: 1.0,
      sparkles: []
    };
  }
  
  updateStars() {
    const centerX = this.canvas.width / 2;
    const centerY = this.canvas.height / 2;

    // Enhanced speed calculation with warp effects
    const baseSpeedMultiplier = 1 + this.signalIntensity * 2;
    const warpMultiplier = this.config.warpSpeed ? 5 : 1;
    const hyperMultiplier = this.config.hyperSpace ? 10 : 1;

    this.stars.forEach(star => {
      // Enhanced movement with quantum effects
      let speedMultiplier = baseSpeedMultiplier * warpMultiplier * hyperMultiplier;

      // Quantum burst effect - random speed spikes
      if (this.config.quantumBurst && Math.random() < 0.05) {
        speedMultiplier *= 3;
        star.quantumGlow = 1.0;
      }

      // Decay quantum glow
      if (star.quantumGlow) {
        star.quantumGlow *= 0.95;
        if (star.quantumGlow < 0.1) star.quantumGlow = 0;
      }

      // Move star towards viewer (z decreases)
      star.z -= star.speed * speedMultiplier;

      // Reset star when it gets too close
      if (star.z <= 0) {
        star.z = 1000 + Math.random() * 500; // Randomize depth
        star.x = Math.random() * this.canvas.width;
        star.y = Math.random() * this.canvas.height;
        // Randomize color on reset for degen effect
        if (this.config.degenMode) {
          star.color = this.config.starColors[Math.floor(Math.random() * this.config.starColors.length)];
        }
      }

      // Enhanced twinkle with signal intensity
      star.twinkle += 0.1 + this.signalIntensity * 0.2;
    });
  }
  
  updateComets() {
    // Create new comets based on ML signals
    if (Math.random() < this.config.cometFrequency * (1 + this.signalIntensity)) {
      this.comets.push(this.createComet(this.mlSignalState));
    }
    
    // Update existing comets
    this.comets = this.comets.filter(comet => {
      comet.x += comet.vx;
      comet.y += comet.vy;
      comet.life -= 0.01;
      
      // Add to trail
      comet.trail.push({ x: comet.x, y: comet.y, life: comet.life });
      if (comet.trail.length > this.config.trailLength) {
        comet.trail.shift();
      }
      
      return comet.life > 0 && comet.y < this.canvas.height + 100;
    });
  }
  
  updateMeteors() {
    // Create new meteors
    if (Math.random() < this.config.meteorFrequency) {
      this.meteors.push(this.createMeteor());
    }
    
    // Update existing meteors
    this.meteors = this.meteors.filter(meteor => {
      meteor.x += meteor.vx;
      meteor.y += meteor.vy;
      meteor.life -= 0.008;
      
      // Add to trail
      meteor.trail.push({ x: meteor.x, y: meteor.y, life: meteor.life });
      if (meteor.trail.length > this.config.trailLength * 1.5) {
        meteor.trail.shift();
      }
      
      // Add sparkles
      if (Math.random() < 0.3) {
        meteor.sparkles.push({
          x: meteor.x + (Math.random() - 0.5) * 10,
          y: meteor.y + (Math.random() - 0.5) * 10,
          life: 0.5,
          size: Math.random() * 2 + 1
        });
      }
      
      // Update sparkles
      meteor.sparkles = meteor.sparkles.filter(sparkle => {
        sparkle.life -= 0.05;
        return sparkle.life > 0;
      });
      
      return meteor.life > 0 && meteor.y < this.canvas.height + 100;
    });
  }
  
  drawStars() {
    const centerX = this.canvas.width / 2;
    const centerY = this.canvas.height / 2;

    this.stars.forEach(star => {
      // Calculate 3D projection
      const x = (star.x - centerX) * (1000 / star.z) + centerX;
      const y = (star.y - centerY) * (1000 / star.z) + centerY;
      let size = (1000 / star.z) * 2;

      // Skip if outside canvas
      if (x < 0 || x > this.canvas.width || y < 0 || y > this.canvas.height) return;

      // Enhanced size calculation for warp effects
      if (this.config.warpSpeed) {
        size *= 1.5;
      }
      if (this.config.hyperSpace) {
        size *= 2;
      }

      // Calculate brightness with enhanced twinkle effect
      const twinkleBrightness = (Math.sin(star.twinkle) + 1) * 0.5;
      let brightness = star.brightness * twinkleBrightness * (1000 / star.z) * 0.5;

      // Quantum glow enhancement
      if (star.quantumGlow) {
        brightness += star.quantumGlow;
        size += star.quantumGlow * 3;
      }

      // Signal intensity enhancement
      brightness *= (1 + this.signalIntensity * 0.5);

      // Draw star with enhanced glow
      this.ctx.save();
      this.ctx.globalAlpha = Math.min(brightness, 1);
      this.ctx.fillStyle = star.color;
      this.ctx.shadowColor = star.color;
      this.ctx.shadowBlur = size * this.config.glowIntensity * (1 + this.signalIntensity);

      // Draw multiple layers for degen effect
      if (this.config.degenMode && brightness > 0.7) {
        // Inner core
        this.ctx.beginPath();
        this.ctx.arc(x, y, Math.max(size * 0.3, 0.5), 0, Math.PI * 2);
        this.ctx.fill();

        // Outer glow
        this.ctx.globalAlpha *= 0.6;
        this.ctx.shadowBlur *= 2;
        this.ctx.beginPath();
        this.ctx.arc(x, y, Math.max(size, 0.5), 0, Math.PI * 2);
        this.ctx.fill();
      } else {
        this.ctx.beginPath();
        this.ctx.arc(x, y, Math.max(size, 0.5), 0, Math.PI * 2);
        this.ctx.fill();
      }

      this.ctx.restore();
    });
  }
  
  drawComets() {
    this.comets.forEach(comet => {
      // Draw trail
      comet.trail.forEach((point, index) => {
        const alpha = (point.life * index) / comet.trail.length;
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.ctx.fillStyle = comet.color;
        this.ctx.shadowColor = comet.color;
        this.ctx.shadowBlur = 5;
        
        this.ctx.beginPath();
        this.ctx.arc(point.x, point.y, comet.size * alpha, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.restore();
      });
      
      // Draw comet head
      this.ctx.save();
      this.ctx.globalAlpha = comet.life;
      this.ctx.fillStyle = comet.color;
      this.ctx.shadowColor = comet.color;
      this.ctx.shadowBlur = 10;
      
      this.ctx.beginPath();
      this.ctx.arc(comet.x, comet.y, comet.size, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.restore();
    });
  }
  
  drawMeteors() {
    this.meteors.forEach(meteor => {
      // Draw trail
      meteor.trail.forEach((point, index) => {
        const alpha = (point.life * index) / meteor.trail.length;
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.ctx.fillStyle = meteor.color;
        this.ctx.shadowColor = meteor.color;
        this.ctx.shadowBlur = 8;
        
        this.ctx.beginPath();
        this.ctx.arc(point.x, point.y, meteor.size * alpha, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.restore();
      });
      
      // Draw sparkles
      meteor.sparkles.forEach(sparkle => {
        this.ctx.save();
        this.ctx.globalAlpha = sparkle.life;
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.shadowColor = '#FFFFFF';
        this.ctx.shadowBlur = 3;
        
        this.ctx.beginPath();
        this.ctx.arc(sparkle.x, sparkle.y, sparkle.size, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.restore();
      });
      
      // Draw meteor head
      this.ctx.save();
      this.ctx.globalAlpha = meteor.life;
      this.ctx.fillStyle = meteor.color;
      this.ctx.shadowColor = meteor.color;
      this.ctx.shadowBlur = 15;
      
      this.ctx.beginPath();
      this.ctx.arc(meteor.x, meteor.y, meteor.size, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.restore();
    });
  }
  
  render() {
    // Clear canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // Draw all elements
    this.drawStars();
    this.drawComets();
    this.drawMeteors();
  }
  
  animate() {
    if (!this.isRunning) return;
    
    this.updateStars();
    this.updateComets();
    this.updateMeteors();
    this.render();
    
    this.animationId = requestAnimationFrame(() => this.animate());
  }
  
  start() {
    if (this.isRunning) return;
    this.isRunning = true;
    this.animate();
    console.log('🌟 Starfield animation started');
  }
  
  stop() {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    console.log('🌟 Starfield animation stopped');
  }
  
  // Public method to update ML signal state
  updateMLSignal(signal, intensity = 0.5) {
    this.mlSignalState = signal; // 'buy', 'sell', 'neutral'
    this.signalIntensity = Math.max(0, Math.min(1, intensity));

    console.log(`🌟 Starfield ML Signal: ${signal} (intensity: ${intensity.toFixed(2)})`);

    // Trigger special effects based on signal strength
    if (intensity > 0.9) {
      // DEGEN MODE: Maximum chaos
      this.triggerQuantumBurst();
      this.triggerHyperSpace(2000);
      this.triggerCometStorm(signal, 8);
    } else if (intensity > 0.8) {
      // High intensity: Warp speed + comet burst
      this.triggerWarpSpeed(1500);
      this.triggerCometStorm(signal, 5);
    } else if (intensity > 0.7) {
      // Medium-high intensity: Comet burst
      this.triggerCometStorm(signal, 3);
    } else if (intensity > 0.5) {
      // Medium intensity: Single comet
      this.comets.push(this.createComet(signal));
    }

    // Add meteors for sell signals (dramatic effect)
    if (signal === 'sell' && intensity > 0.6) {
      for (let i = 0; i < Math.floor(intensity * 3); i++) {
        setTimeout(() => {
          this.meteors.push(this.createMeteor());
        }, i * 300);
      }
    }
  }

  triggerQuantumBurst() {
    this.config.quantumBurst = true;
    console.log('🌟 QUANTUM BURST ACTIVATED!');
    setTimeout(() => {
      this.config.quantumBurst = false;
    }, 3000);
  }

  triggerWarpSpeed(duration = 1000) {
    this.config.warpSpeed = true;
    console.log('🌟 WARP SPEED ENGAGED!');
    setTimeout(() => {
      this.config.warpSpeed = false;
    }, duration);
  }

  triggerHyperSpace(duration = 1500) {
    this.config.hyperSpace = true;
    console.log('🌟 HYPERSPACE JUMP!');
    setTimeout(() => {
      this.config.hyperSpace = false;
    }, duration);
  }

  triggerCometStorm(signal, count = 5) {
    console.log(`🌟 COMET STORM: ${count} ${signal} comets incoming!`);
    for (let i = 0; i < count; i++) {
      setTimeout(() => {
        this.comets.push(this.createComet(signal));
      }, i * 150);
    }
  }
  
  bindEvents() {
    // Resize handler
    window.addEventListener('resize', () => {
      this.resizeCanvas();
      this.generateStars(); // Regenerate stars for new canvas size
    });
    
    // Visibility change handler
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.stop();
      } else {
        this.start();
      }
    });
  }
  
  destroy() {
    this.stop();
    if (this.canvas && this.canvas.parentNode) {
      this.canvas.parentNode.removeChild(this.canvas);
    }
  }
}

// Initialize starfield animation
window.StarfieldAnimation = StarfieldAnimation;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (!window.starfieldAnimation) {
    window.starfieldAnimation = new StarfieldAnimation();
    console.log('🌟 Starfield animation initialized');
  }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = StarfieldAnimation;
}
