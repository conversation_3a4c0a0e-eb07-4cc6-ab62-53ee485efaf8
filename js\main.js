// StarCrypt Enterprise - Legacy Main Application Controller
// DEPRECATED: This file is now replaced by the unified initialization system
// The new system uses js/init.js and the core modules in js/core/

console.warn('[DEPRECATED] main.js is deprecated. Using new unified initialization system.');

// Maintain backward compatibility for any legacy code that might depend on this
if (!window.wsUrl) {
  window.wsUrl = window.location.hostname === 'localhost' ?
    'ws://localhost:8080' :
    `wss://${window.location.host}`
  console.log('WebSocket URL (legacy):', window.wsUrl)
}

// Exit early to prevent conflicts with new system
if (window.starCryptCore || window.starCryptInitialized) {
  console.log('[DEPRECATED] New StarCrypt system detected, skipping legacy initialization');
  return;
}
// Wait for DOM to be fully loaded before initializing
document.addEventListener('DOMContentLoaded', () => {
  console.log('StarCrypt Enterprise Initializing...')

  // Global variables are defined in global-variables.js
  // Make sure these are set properly
  if (!window.TRADING_STRATEGIES) {
    console.error('Error: TRADING_STRATEGIES not defined - check that global-variables.js is loaded')
  }

  // Make sure currentStrategy is set to a valid strategy
  if (!window.currentStrategy || !window.TRADING_STRATEGIES[window.currentStrategy]) {
    console.log('Setting default strategy to admiral_toa')
    window.currentStrategy = 'admiral_toa'
  }

  // Set global variables and configurations
  window.currentPair = window.currentPair || 'xbtusdt'
  window.currentTf = window.currentTf || '1h'
  window.logMessages = window.logMessages || []
  window.enabledIndicators = [
    'rsi', 'stochRsi', 'macd', 'bollingerBands', 'atr', 'adx',
    'williamsR', 'ultimateOscillator', 'mfi', 'vwap', 'fractal',
    'volume', 'ml', 'sentiment', 'entropy', 'correlation', 'time_anomaly',
  ]

  // Add strategy switch event listener with persistence
  const strategySelect = document.getElementById('mainStrategySelector')
  const applyStrategyBtn = document.getElementById('applyStrategyButton') // Corrected ID

  // Function to handle strategy application
  const applyStrategy = () => {
    if (!strategySelect) return

    const selectedStrategy = strategySelect.value
    if (!selectedStrategy || selectedStrategy === window.currentStrategy) {
      return // No change or invalid selection
    }

    console.log('Applying strategy:', selectedStrategy)

    try {
      // Update current strategy
      const previousStrategy = window.currentStrategy
      window.currentStrategy = selectedStrategy

      // Save to localStorage
      try {
        localStorage.setItem('currentStrategy', selectedStrategy)
      } catch (e) {
        console.warn('Could not save strategy to localStorage:', e)
      }

      // Update enabled indicators based on the new strategy
      const strategy = window.TRADING_STRATEGIES[selectedStrategy]
      if (strategy && Array.isArray(strategy.indicators)) {
        window.enabledIndicators = [...strategy.indicators]

        // Update the signal matrix
        if (typeof window.updateSignalMatrix === 'function') {
          window.updateSignalMatrix()
        }

        // Update any other components that depend on the strategy
        if (typeof window.onStrategyChanged === 'function') {
          window.onStrategyChanged(selectedStrategy, previousStrategy)
        }

        // Show success message
        const statusEl = document.getElementById('strategyStatus')
        if (statusEl) {
          statusEl.textContent = `Strategy updated to: ${selectedStrategy}`
          statusEl.className = 'status success'
          setTimeout(() => {
            statusEl.textContent = ''
            statusEl.className = 'status'
          }, 3000)
        }

        console.log(`Strategy changed to ${selectedStrategy}`)
      } else {
        console.error('Invalid strategy configuration:', selectedStrategy)
      }
    } catch (error) {
      console.error('Error applying strategy:', error)
      // Revert the select to previous value
      if (strategySelect) {
        strategySelect.value = window.currentStrategy
      }

      // Show error message
      const statusEl = document.getElementById('strategyStatus')
      if (statusEl) {
        statusEl.textContent = `Failed to update strategy: ${error.message || 'Unknown error'}`
        statusEl.className = 'status error'
        setTimeout(() => {
          statusEl.textContent = ''
          statusEl.className = 'status'
        }, 5000)
      }
    }
  }

  // Set up event listeners
  if (strategySelect) {
    // Preview strategy on change
    strategySelect.addEventListener('change', (event) => {
      const selectedStrategy = event.target.value
      console.log('Strategy preview:', selectedStrategy)

      // Update strategy info panel with the selected strategy details
      if (typeof window.updateStrategyInfoPanel === 'function') {
        window.updateStrategyInfoPanel(selectedStrategy)
      }
    })

    // Apply strategy on button click or Enter key
    const handleApply = () => {
      applyStrategy()
      return false
    }

    if (applyStrategyBtn) {
      applyStrategyBtn.addEventListener('click', handleApply)
    }

    strategySelect.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        handleApply()
      }
    })

    // Set initial strategy from localStorage or default
    const savedStrategy = localStorage.getItem('currentStrategy')
    if (savedStrategy && window.TRADING_STRATEGIES[savedStrategy]) {
      window.currentStrategy = savedStrategy
      strategySelect.value = savedStrategy

      // Update UI to reflect the current strategy
      if (typeof window.updateStrategyInfoPanel === 'function') {
        window.updateStrategyInfoPanel(savedStrategy)
      }
    } else if (strategySelect.value !== window.currentStrategy) {
      strategySelect.value = window.currentStrategy
    }
  }

  // Initialize signal matrix (SignalSystem handles this now)
  /*
  const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']
  if (typeof window.createSignalMatrix === 'function') {
    if (typeof window.isSignalMatrixInitialized === 'undefined') {
      window.isSignalMatrixInitialized = true
      window.createSignalMatrix('signalMatrixContainer', timeframes, window.enabledIndicators)
      console.log('Signal matrix initialized')
    } else {
      console.log('Signal matrix already initialized, skipping to avoid duplicates.')
    }
  } else {
    console.error('Signal matrix initialization function not found')
  }
  */

  // Initialize strategy selector
  if (typeof window.initializeStrategySelector === 'function') {
    window.initializeStrategySelector()
    console.log('Strategy selector initialized')
  }

  // Initialize all event handlers
  if (typeof window.initializeEventHandlers === 'function') {
    window.initializeEventHandlers()
    console.log('Event handlers initialized')
  }

  // Initialize animations
  if (typeof window.initializeAnimations === 'function') {
    window.initializeAnimations()
    console.log('Animations initialized')
  }

  // Initialize charts
  if (typeof window.initializeCharts === 'function') {
    window.initializeCharts()
    console.log('Charts initialized')
  }

  // Initialize signal lights
  if (typeof window.updateAllSignalLights === 'function') {
    window.updateAllSignalLights()
    console.log('Signal lights initialized')
  }

  // Connect to WebSocket server
  // This happens automatically through websocket-init.js

  // Show initialization complete message
  console.log('StarCrypt Enterprise Initialized Successfully')

  // Initialize Signal Matrix first
  if (typeof window.initializeSignalMatrix === 'function') {
    console.log('Main.js is now initializing Signal Matrix...');
    window.initializeSignalMatrix();
  } else {
    console.error('CRITICAL: initializeSignalMatrix function not found. The momentum-indicators table will not be populated.');
  }

  // Initialize Indicator Displays
  if (typeof window.initializeIndicatorDisplay === 'function') {
    console.log('Main.js is now initializing Indicator Display...');
    window.initializeIndicatorDisplay();
  } else {
    console.warn('initializeIndicatorDisplay function not found. Some indicator features may be limited.');
  }

  // Manually initialize SignalSystem now that the DOM is ready
  if (window.signalSystem && typeof window.signalSystem.init === 'function') {
    console.log('Main.js is now initializing SignalSystem...');
    window.signalSystem.init();
  } else {
    console.error('CRITICAL: SignalSystem object not found. The signal glow and click-to-sync will not work.');
  }

  // Hide loading overlay once everything is ready
  const loadingOverlay = document.getElementById('loading-overlay')
  if (loadingOverlay) {
    setTimeout(() => {
      loadingOverlay.style.opacity = '0'
      setTimeout(() => {
        loadingOverlay.style.display = 'none'
      }, 1000)
    }, 500)
  }
})

// Global error handler to catch and log uncaught exceptions with descriptive details
window.onerror = function (message, source, lineno, colno, error) {
  console.error(`Global error: ${message} at ${source}:${lineno}:${colno}`, error)
  // Optionally, you can add more handling here, e.g., sending to server or UI notification
  return true // Prevent default browser error handling
}
// Add this to your main.js
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error || event.message, event)
  return false
})

// Add this to your WebSocket error handler
if (window.ws) {
  window.ws.onerror = function (error) {
    console.error('WebSocket error:', error)
  }
}
