/**
 * Enhanced Signal Logic Styles for StarCrypt
 * Comprehensive styling for enhanced signal logic system
 */

/* Enhanced Signal Logic Menu */
.enhanced-signal-logic-menu {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  border: 2px solid #00d4ff;
  border-radius: 15px;
  padding: 20px;
  color: #ffffff;
  font-family: 'Orbitron', monospace;
  box-shadow: 0 0 30px rgba(0, 212, 255, 0.3);
  backdrop-filter: blur(10px);
  max-width: 500px;
  min-height: 600px;
}

.menu-header {
  border-bottom: 2px solid #00d4ff;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.menu-header h3 {
  margin: 0 0 15px 0;
  color: #00d4ff;
  text-align: center;
  font-size: 1.4em;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

/* Menu Tabs */
.menu-tabs {
  display: flex;
  gap: 5px;
  justify-content: center;
}

.tab-btn {
  background: linear-gradient(135deg, #2a2a4e 0%, #1e1e3f 100%);
  border: 1px solid #00d4ff;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Orbitron', monospace;
  font-size: 0.9em;
}

.tab-btn:hover {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
  transform: translateY(-2px);
}

.tab-btn.active {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  color: #000000;
  box-shadow: 0 0 20px rgba(0, 212, 255, 0.6);
}

/* Tab Content */
.tab-content {
  display: none;
  animation: fadeIn 0.3s ease;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Setting Groups */
.setting-group {
  margin-bottom: 20px;
  padding: 15px;
  background: rgba(0, 212, 255, 0.05);
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 10px;
}

.setting-group label {
  display: block;
  color: #00d4ff;
  font-weight: bold;
  margin-bottom: 10px;
  font-size: 0.95em;
}

/* Enhanced Sliders */
.slider-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.enhanced-slider {
  flex: 1;
  height: 6px;
  background: linear-gradient(90deg, #1a1a2e 0%, #16213e 100%);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  position: relative;
}

.enhanced-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
  transition: all 0.3s ease;
}

.enhanced-slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.8);
}

.enhanced-slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.slider-value {
  min-width: 60px;
  text-align: center;
  color: #00d4ff;
  font-weight: bold;
  background: rgba(0, 212, 255, 0.1);
  padding: 5px 10px;
  border-radius: 5px;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

/* Enhanced Select */
.enhanced-select {
  width: 100%;
  background: linear-gradient(135deg, #2a2a4e 0%, #1e1e3f 100%);
  border: 1px solid #00d4ff;
  color: #ffffff;
  padding: 10px;
  border-radius: 8px;
  font-family: 'Orbitron', monospace;
  cursor: pointer;
  transition: all 0.3s ease;
}

.enhanced-select:hover,
.enhanced-select:focus {
  border-color: #00d4ff;
  box-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
  outline: none;
}

/* Weight Controls */
.weight-controls {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.weight-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  background: rgba(0, 212, 255, 0.05);
  border-radius: 5px;
}

.weight-item input[type="number"] {
  width: 80px;
  background: linear-gradient(135deg, #2a2a4e 0%, #1e1e3f 100%);
  border: 1px solid #00d4ff;
  color: #ffffff;
  padding: 5px;
  border-radius: 5px;
  font-family: 'Orbitron', monospace;
  text-align: center;
}

/* Checkbox Group */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.checkbox-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px;
  border-radius: 5px;
  transition: background 0.3s ease;
}

.checkbox-item:hover {
  background: rgba(0, 212, 255, 0.1);
}

.checkbox-item input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid #00d4ff;
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  border-color: #00d4ff;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #000000;
  font-weight: bold;
  font-size: 14px;
}

/* History Controls */
.history-controls {
  display: flex;
  gap: 10px;
}

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-danger {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-family: 'Orbitron', monospace;
  font-weight: bold;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-primary {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  color: #000000;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 25px rgba(0, 212, 255, 0.5);
}

.btn-secondary {
  background: linear-gradient(135deg, #2a2a4e 0%, #1e1e3f 100%);
  color: #ffffff;
  border: 1px solid #00d4ff;
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  color: #000000;
  transform: translateY(-2px);
}

.btn-danger {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  color: #ffffff;
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 15px rgba(255, 71, 87, 0.4);
}

/* Menu Footer */
.menu-footer {
  border-top: 2px solid #00d4ff;
  padding-top: 15px;
  margin-top: 20px;
}

.status-indicators {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  background: rgba(0, 212, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(0, 212, 255, 0.2);
  flex: 1;
  margin: 0 5px;
}

.status-label {
  font-size: 0.8em;
  color: #00d4ff;
  margin-bottom: 5px;
}

.convergence-strength,
.confidence-level {
  font-size: 1.2em;
  font-weight: bold;
  color: #ffffff;
}

.convergence-strength.buy,
.confidence-level.buy {
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
}

.convergence-strength.sell,
.confidence-level.sell {
  color: #ff4757;
  text-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
}

.convergence-strength.neutral,
.confidence-level.neutral {
  color: #ffa502;
  text-shadow: 0 0 10px rgba(255, 165, 2, 0.5);
}

.menu-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

/* Signal Logic Visual Effects */
.light-logic-conservative .signal-circle {
  transition: all 0.2s ease;
  filter: brightness(0.9);
}

.light-logic-wallstreet .signal-circle {
  transition: all 0.3s ease;
  box-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.light-logic-vibeflow .signal-circle {
  transition: all 0.4s ease;
  animation: vibeflow-pulse 2s infinite;
}

@keyframes vibeflow-pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.light-logic-cosmic .signal-circle {
  transition: all 0.5s ease;
  animation: cosmic-glow 3s infinite;
  filter: hue-rotate(0deg);
}

@keyframes cosmic-glow {
  0%, 100% { filter: hue-rotate(0deg) brightness(1); }
  33% { filter: hue-rotate(120deg) brightness(1.2); }
  66% { filter: hue-rotate(240deg) brightness(1.1); }
}

.light-logic-chaos .signal-circle {
  transition: all 0.1s ease;
  animation: chaos-flicker 0.5s infinite;
}

@keyframes chaos-flicker {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Enhanced Effects */
.enable-pulse {
  animation: enhanced-pulse 1.5s infinite !important;
}

@keyframes enhanced-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.1); opacity: 0.8; }
}

.enable-glow {
  box-shadow: 0 0 15px currentColor !important;
}

.enable-chevrons::before {
  content: '▶';
  position: absolute;
  left: -15px;
  top: 50%;
  transform: translateY(-50%);
  color: currentColor;
  font-size: 0.8em;
  opacity: 0.7;
}

/* Convergence Visual Effects */
.convergence-buy {
  background: radial-gradient(circle, rgba(0, 255, 136, 0.1) 0%, transparent 70%);
}

.convergence-sell {
  background: radial-gradient(circle, rgba(255, 71, 87, 0.1) 0%, transparent 70%);
}

.convergence-neutral {
  background: radial-gradient(circle, rgba(255, 165, 2, 0.1) 0%, transparent 70%);
}

/* Background Effects */
.bg-convergence-buy {
  background: linear-gradient(135deg, 
    rgba(0, 255, 136, 0.05) 0%, 
    rgba(0, 212, 255, 0.03) 50%, 
    rgba(0, 0, 0, 0.8) 100%);
}

.bg-convergence-sell {
  background: linear-gradient(135deg, 
    rgba(255, 71, 87, 0.05) 0%, 
    rgba(255, 165, 2, 0.03) 50%, 
    rgba(0, 0, 0, 0.8) 100%);
}

.bg-convergence-neutral {
  background: linear-gradient(135deg, 
    rgba(255, 165, 2, 0.05) 0%, 
    rgba(0, 212, 255, 0.03) 50%, 
    rgba(0, 0, 0, 0.8) 100%);
}

/* Convergence Animations */
.convergence-pulse {
  animation: convergence-pulse-bg 2s infinite;
}

@keyframes convergence-pulse-bg {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.1); }
}

.divergence-warning {
  animation: divergence-warning-bg 1s infinite;
}

@keyframes divergence-warning-bg {
  0%, 100% { filter: hue-rotate(0deg); }
  50% { filter: hue-rotate(30deg); }
}

/* Helper Step Enhancements */
.helper-step {
  transition: all 0.3s ease;
}

.helper-step.highlight-active {
  background: rgba(0, 212, 255, 0.1);
  border-left: 3px solid #00d4ff;
  padding-left: 15px;
  animation: step-highlight 2s infinite;
}

@keyframes step-highlight {
  0%, 100% { box-shadow: 0 0 5px rgba(0, 212, 255, 0.3); }
  50% { box-shadow: 0 0 15px rgba(0, 212, 255, 0.6); }
}

.step-buy {
  border-left-color: #00ff88;
  background: rgba(0, 255, 136, 0.05);
}

.step-sell {
  border-left-color: #ff4757;
  background: rgba(255, 71, 87, 0.05);
}

.step-neutral {
  border-left-color: #ffa502;
  background: rgba(255, 165, 2, 0.05);
}

.step-highlight {
  animation: step-highlight-enhanced 1.5s infinite;
}

@keyframes step-highlight-enhanced {
  0%, 100% { transform: translateX(0); }
  50% { transform: translateX(5px); }
}

/* Priority Signals */
.priority-signal {
  z-index: 15 !important;
  transform: scale(1.1);
  box-shadow: 0 0 20px currentColor;
  animation: priority-pulse 1s infinite;
}

@keyframes priority-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

/* Weak Signals */
.weak-signal {
  opacity: 0.5 !important;
  filter: grayscale(0.3);
}

/* Dot Matrix Effects */
.matrix-dot {
  transition: all 0.3s ease;
}

.dot-buy {
  background: radial-gradient(circle, #00ff88 0%, transparent 70%);
}

.dot-sell {
  background: radial-gradient(circle, #ff4757 0%, transparent 70%);
}

.dot-neutral {
  background: radial-gradient(circle, #ffa502 0%, transparent 70%);
}

/* Responsive Design */
@media (max-width: 768px) {
  .enhanced-signal-logic-menu {
    max-width: 100%;
    padding: 15px;
  }
  
  .menu-tabs {
    flex-direction: column;
    gap: 5px;
  }
  
  .status-indicators {
    flex-direction: column;
    gap: 10px;
  }
  
  .menu-actions {
    flex-direction: column;
  }
}
