/**
 * Enhanced Indicator Menu for Live Oracle Matrix Control
 * Provides dynamic indicator management with real-time Oracle Matrix updates
 */

class EnhancedIndicatorMenu {
  constructor() {
    this.enabledIndicators = [];
    this.availableIndicators = {
      momentum: ['rsi', 'stochRsi', 'macd', 'ultimateOscillator', 'mfi', 'williamsR', 'cci', 'roc', 'momentum'],
      trend: ['sma', 'ema', 'bollingerBands', 'atr', 'adx', 'parabolicSar', 'ichimoku', 'vwap', 'supertrend'],
      volume: ['volume', 'volumeProfile', 'obv', 'volumeOscillator', 'accumulation', 'chaikinOscillator'],
      ml: ['mlPrediction', 'neuralNetwork', 'sentimentAnalysis', 'patternRecognition', 'aiSignal']
    };
    this.currentStrategy = 'admiral_toa';
    this.isLiveMode = true;
    
    this.init();
  }
  
  init() {
    this.loadEnabledIndicators();
    this.enhanceIndicatorMenu();
    this.bindEvents();
    console.log('🎯 Enhanced Indicator Menu initialized');
  }
  
  loadEnabledIndicators() {
    // Load from localStorage or use defaults
    const saved = localStorage.getItem('enhancedEnabledIndicators');
    if (saved) {
      this.enabledIndicators = JSON.parse(saved);
    } else {
      // Default enabled indicators for Admiral TOA
      this.enabledIndicators = [
        'rsi', 'macd', 'bollingerBands', 'atr', 'volume', 'mlPrediction'
      ];
    }
  }
  
  saveEnabledIndicators() {
    localStorage.setItem('enhancedEnabledIndicators', JSON.stringify(this.enabledIndicators));
  }
  
  enhanceIndicatorMenu() {
    const menuContainer = document.getElementById('indicatorMenu');
    if (!menuContainer) {
      console.warn('Indicator menu container not found');
      return;
    }
    
    menuContainer.innerHTML = this.generateEnhancedMenuHTML();
    this.addEnhancedStyles();
  }
  
  generateEnhancedMenuHTML() {
    return `
      <div class="enhanced-indicator-menu">
        <div class="menu-header">
          <h3>🎯 Oracle Matrix Control</h3>
          <div class="menu-controls">
            <button class="live-mode-toggle ${this.isLiveMode ? 'active' : ''}" id="liveModeToggle">
              <span class="toggle-icon">⚡</span>
              Live Mode
            </button>
            <button class="preset-button" id="loadPresetButton">Load Preset</button>
          </div>
        </div>
        
        <div class="strategy-info">
          <span class="current-strategy">Strategy: ${this.getCurrentStrategyName()}</span>
          <span class="enabled-count">${this.enabledIndicators.length} indicators active</span>
        </div>
        
        <div class="indicator-categories">
          ${this.generateCategoryHTML('momentum', '📈 Momentum', '#FF6B6B')}
          ${this.generateCategoryHTML('trend', '📊 Trend', '#4ECDC4')}
          ${this.generateCategoryHTML('volume', '📦 Volume', '#45B7D1')}
          ${this.generateCategoryHTML('ml', '🤖 AI/ML', '#96CEB4')}
        </div>
        
        <div class="menu-actions">
          <button class="action-button enable-all" id="enableAllButton">Enable All</button>
          <button class="action-button disable-all" id="disableAllButton">Disable All</button>
          <button class="action-button apply-changes" id="applyChangesButton">Apply Changes</button>
        </div>
        
        <div class="live-preview" id="livePreview">
          <h4>Live Preview</h4>
          <div class="preview-indicators" id="previewIndicators">
            ${this.generatePreviewHTML()}
          </div>
        </div>
      </div>
    `;
  }
  
  generateCategoryHTML(category, title, color) {
    const indicators = this.availableIndicators[category] || [];
    
    return `
      <div class="indicator-category" data-category="${category}">
        <div class="category-header" style="border-left: 4px solid ${color};">
          <h4>${title}</h4>
          <span class="category-count">${indicators.filter(ind => this.enabledIndicators.includes(ind)).length}/${indicators.length}</span>
        </div>
        <div class="category-indicators">
          ${indicators.map(indicator => this.generateIndicatorHTML(indicator, category, color)).join('')}
        </div>
      </div>
    `;
  }
  
  generateIndicatorHTML(indicator, category, color) {
    const isEnabled = this.enabledIndicators.includes(indicator);
    const displayName = this.getIndicatorDisplayName(indicator);
    
    return `
      <div class="indicator-item ${isEnabled ? 'enabled' : ''}" data-indicator="${indicator}">
        <div class="indicator-toggle">
          <input type="checkbox" 
                 id="indicator-${indicator}" 
                 ${isEnabled ? 'checked' : ''}
                 data-indicator="${indicator}"
                 data-category="${category}">
          <label for="indicator-${indicator}" class="toggle-label">
            <span class="toggle-switch" style="background-color: ${color};"></span>
          </label>
        </div>
        <div class="indicator-info">
          <span class="indicator-name">${displayName}</span>
          <span class="indicator-description">${this.getIndicatorDescription(indicator)}</span>
        </div>
        <div class="indicator-actions">
          <button class="info-button" data-indicator="${indicator}" title="More Info">ⓘ</button>
          <button class="priority-button" data-indicator="${indicator}" title="High Priority">⭐</button>
        </div>
      </div>
    `;
  }
  
  generatePreviewHTML() {
    return this.enabledIndicators.map(indicator => {
      const category = this.getIndicatorCategory(indicator);
      const color = this.getCategoryColor(category);
      return `
        <div class="preview-indicator" style="border-left: 3px solid ${color};">
          <span class="preview-name">${this.getIndicatorDisplayName(indicator)}</span>
          <span class="preview-category">${category}</span>
        </div>
      `;
    }).join('');
  }
  
  addEnhancedStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .enhanced-indicator-menu {
        background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 30, 60, 0.95));
        border: 2px solid rgba(0, 255, 255, 0.3);
        border-radius: 12px;
        padding: 20px;
        max-height: 80vh;
        overflow-y: auto;
        font-family: 'Orbitron', sans-serif;
        color: #FFFFFF;
        box-shadow: 0 0 30px rgba(0, 255, 255, 0.2);
      }
      
      .menu-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid rgba(0, 255, 255, 0.3);
      }
      
      .menu-header h3 {
        margin: 0;
        color: #00FFFF;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      }
      
      .menu-controls {
        display: flex;
        gap: 10px;
      }
      
      .live-mode-toggle {
        background: rgba(0, 255, 0, 0.2);
        border: 1px solid #00FF00;
        color: #00FF00;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      
      .live-mode-toggle.active {
        background: rgba(0, 255, 0, 0.4);
        box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
      }
      
      .preset-button {
        background: rgba(255, 215, 0, 0.2);
        border: 1px solid #FFD700;
        color: #FFD700;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      
      .strategy-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        padding: 10px;
        background: rgba(0, 255, 255, 0.1);
        border-radius: 5px;
        font-size: 0.9rem;
      }
      
      .current-strategy {
        color: #00FFFF;
        font-weight: bold;
      }
      
      .enabled-count {
        color: #00FF00;
      }
      
      .indicator-categories {
        margin-bottom: 20px;
      }
      
      .indicator-category {
        margin-bottom: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        overflow: hidden;
      }
      
      .category-header {
        padding: 10px 15px;
        background: rgba(0, 0, 0, 0.3);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .category-header h4 {
        margin: 0;
        font-size: 1rem;
      }
      
      .category-count {
        background: rgba(255, 255, 255, 0.2);
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 0.8rem;
      }
      
      .category-indicators {
        padding: 10px;
      }
      
      .indicator-item {
        display: flex;
        align-items: center;
        padding: 8px;
        margin-bottom: 5px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 5px;
        transition: all 0.3s ease;
      }
      
      .indicator-item.enabled {
        background: rgba(0, 255, 255, 0.1);
        border-left: 3px solid #00FFFF;
      }
      
      .indicator-item:hover {
        background: rgba(255, 255, 255, 0.1);
      }
      
      .indicator-toggle {
        margin-right: 10px;
      }
      
      .toggle-label {
        display: inline-block;
        width: 40px;
        height: 20px;
        background: #333;
        border-radius: 10px;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
      }
      
      .toggle-switch {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        transition: all 0.3s ease;
      }
      
      input[type="checkbox"]:checked + .toggle-label .toggle-switch {
        transform: translateX(20px);
      }
      
      input[type="checkbox"] {
        display: none;
      }
      
      .indicator-info {
        flex: 1;
      }
      
      .indicator-name {
        display: block;
        font-weight: bold;
        margin-bottom: 2px;
      }
      
      .indicator-description {
        display: block;
        font-size: 0.8rem;
        color: #CCCCCC;
      }
      
      .indicator-actions {
        display: flex;
        gap: 5px;
      }
      
      .info-button, .priority-button {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: #FFFFFF;
        width: 25px;
        height: 25px;
        border-radius: 50%;
        cursor: pointer;
        font-size: 0.8rem;
        transition: all 0.3s ease;
      }
      
      .info-button:hover {
        background: rgba(0, 255, 255, 0.3);
      }
      
      .priority-button:hover {
        background: rgba(255, 215, 0, 0.3);
      }
      
      .menu-actions {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
      }
      
      .action-button {
        flex: 1;
        padding: 10px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 5px;
        background: rgba(255, 255, 255, 0.1);
        color: #FFFFFF;
        cursor: pointer;
        transition: all 0.3s ease;
        font-family: 'Orbitron', sans-serif;
      }
      
      .action-button:hover {
        background: rgba(255, 255, 255, 0.2);
      }
      
      .enable-all {
        border-color: #00FF00;
        color: #00FF00;
      }
      
      .disable-all {
        border-color: #FF6B6B;
        color: #FF6B6B;
      }
      
      .apply-changes {
        border-color: #00FFFF;
        color: #00FFFF;
        font-weight: bold;
      }
      
      .live-preview {
        border-top: 1px solid rgba(0, 255, 255, 0.3);
        padding-top: 15px;
      }
      
      .live-preview h4 {
        margin: 0 0 10px 0;
        color: #00FFFF;
      }
      
      .preview-indicators {
        max-height: 150px;
        overflow-y: auto;
      }
      
      .preview-indicator {
        display: flex;
        justify-content: space-between;
        padding: 5px 10px;
        margin-bottom: 3px;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 3px;
      }
      
      .preview-name {
        font-weight: bold;
      }
      
      .preview-category {
        font-size: 0.8rem;
        color: #CCCCCC;
        text-transform: uppercase;
      }
    `;
    document.head.appendChild(style);
  }
  
  bindEvents() {
    const menuContainer = document.getElementById('indicatorMenu');
    if (!menuContainer) return;
    
    // Indicator toggle events
    menuContainer.addEventListener('change', (e) => {
      if (e.target.type === 'checkbox' && e.target.dataset.indicator) {
        this.toggleIndicator(e.target.dataset.indicator, e.target.checked);
      }
    });
    
    // Action button events
    const enableAllBtn = document.getElementById('enableAllButton');
    const disableAllBtn = document.getElementById('disableAllButton');
    const applyChangesBtn = document.getElementById('applyChangesButton');
    const liveModeToggle = document.getElementById('liveModeToggle');
    
    if (enableAllBtn) {
      enableAllBtn.addEventListener('click', () => this.enableAllIndicators());
    }
    
    if (disableAllBtn) {
      disableAllBtn.addEventListener('click', () => this.disableAllIndicators());
    }
    
    if (applyChangesBtn) {
      applyChangesBtn.addEventListener('click', () => this.applyChanges());
    }
    
    if (liveModeToggle) {
      liveModeToggle.addEventListener('click', () => this.toggleLiveMode());
    }
    
    // Info button events
    menuContainer.addEventListener('click', (e) => {
      if (e.target.classList.contains('info-button')) {
        this.showIndicatorInfo(e.target.dataset.indicator);
      }
    });
  }
  
  toggleIndicator(indicator, enabled) {
    if (enabled && !this.enabledIndicators.includes(indicator)) {
      this.enabledIndicators.push(indicator);
    } else if (!enabled) {
      this.enabledIndicators = this.enabledIndicators.filter(ind => ind !== indicator);
    }
    
    this.updatePreview();
    this.updateEnabledCount();
    
    if (this.isLiveMode) {
      this.applyChanges();
    }
    
    console.log(`🎯 Indicator ${indicator} ${enabled ? 'enabled' : 'disabled'}`);
  }
  
  enableAllIndicators() {
    this.enabledIndicators = Object.values(this.availableIndicators).flat();
    this.updateCheckboxes();
    this.updatePreview();
    this.updateEnabledCount();
    
    if (this.isLiveMode) {
      this.applyChanges();
    }
  }
  
  disableAllIndicators() {
    this.enabledIndicators = [];
    this.updateCheckboxes();
    this.updatePreview();
    this.updateEnabledCount();
    
    if (this.isLiveMode) {
      this.applyChanges();
    }
  }
  
  toggleLiveMode() {
    this.isLiveMode = !this.isLiveMode;
    const toggle = document.getElementById('liveModeToggle');
    if (toggle) {
      toggle.classList.toggle('active', this.isLiveMode);
    }
    console.log(`🎯 Live mode ${this.isLiveMode ? 'enabled' : 'disabled'}`);
  }
  
  updateCheckboxes() {
    const checkboxes = document.querySelectorAll('#indicatorMenu input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
      const indicator = checkbox.dataset.indicator;
      checkbox.checked = this.enabledIndicators.includes(indicator);
      
      const item = checkbox.closest('.indicator-item');
      if (item) {
        item.classList.toggle('enabled', checkbox.checked);
      }
    });
  }
  
  updatePreview() {
    const previewContainer = document.getElementById('previewIndicators');
    if (previewContainer) {
      previewContainer.innerHTML = this.generatePreviewHTML();
    }
  }
  
  updateEnabledCount() {
    const countElement = document.querySelector('.enabled-count');
    if (countElement) {
      countElement.textContent = `${this.enabledIndicators.length} indicators active`;
    }
  }
  
  applyChanges() {
    this.saveEnabledIndicators();
    
    // Update global enabled indicators
    if (window.enabledIndicators) {
      window.enabledIndicators.forEach(ind => {
        ind.enabled = this.enabledIndicators.includes(ind.name);
      });
    }
    
    // Update Oracle Matrix
    if (typeof window.updateSignalMatrix === 'function') {
      window.updateSignalMatrix();
    }
    
    // Update signal lights
    if (typeof window.updateAllSignalLights === 'function') {
      window.updateAllSignalLights();
    }
    
    // Update indicator tables
    if (typeof window.renderIndicatorTables === 'function') {
      window.renderIndicatorTables();
    }
    
    this.showNotification('Oracle Matrix updated successfully!');
    console.log('🎯 Applied indicator changes to Oracle Matrix');
  }
  
  showIndicatorInfo(indicator) {
    const info = this.getIndicatorInfo(indicator);
    this.showTooltip(info);
  }
  
  showNotification(message) {
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: rgba(0, 255, 0, 0.9);
      color: #000;
      padding: 10px 20px;
      border-radius: 5px;
      font-family: 'Orbitron', sans-serif;
      font-weight: bold;
      z-index: 10000;
      animation: slideIn 0.3s ease;
    `;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 3000);
  }
  
  showTooltip(text) {
    const tooltip = document.createElement('div');
    tooltip.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 20, 40, 0.95);
      color: #FFFFFF;
      padding: 20px;
      border-radius: 8px;
      border: 2px solid rgba(0, 255, 255, 0.5);
      font-family: 'Orbitron', monospace;
      font-size: 0.9rem;
      white-space: pre-line;
      z-index: 10000;
      max-width: 400px;
      box-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
    `;
    tooltip.textContent = text;
    
    document.body.appendChild(tooltip);
    
    setTimeout(() => {
      if (tooltip.parentNode) {
        tooltip.parentNode.removeChild(tooltip);
      }
    }, 5000);
  }
  
  // Helper methods
  getCurrentStrategyName() {
    const strategies = window.TRADING_STRATEGIES || {};
    const strategy = strategies[this.currentStrategy];
    return strategy ? strategy.name : 'Unknown Strategy';
  }
  
  getIndicatorDisplayName(indicator) {
    const names = {
      'rsi': 'RSI',
      'stochRsi': 'Stochastic RSI',
      'macd': 'MACD',
      'ultimateOscillator': 'Ultimate Oscillator',
      'mfi': 'Money Flow Index',
      'williamsR': 'Williams %R',
      'cci': 'CCI',
      'roc': 'Rate of Change',
      'momentum': 'Momentum',
      'sma': 'Simple Moving Average',
      'ema': 'Exponential Moving Average',
      'bollingerBands': 'Bollinger Bands',
      'atr': 'Average True Range',
      'adx': 'ADX',
      'parabolicSar': 'Parabolic SAR',
      'ichimoku': 'Ichimoku Cloud',
      'vwap': 'VWAP',
      'supertrend': 'SuperTrend',
      'volume': 'Volume',
      'volumeProfile': 'Volume Profile',
      'obv': 'On-Balance Volume',
      'volumeOscillator': 'Volume Oscillator',
      'accumulation': 'Accumulation/Distribution',
      'chaikinOscillator': 'Chaikin Oscillator',
      'mlPrediction': 'ML Prediction',
      'neuralNetwork': 'Neural Network',
      'sentimentAnalysis': 'Sentiment Analysis',
      'patternRecognition': 'Pattern Recognition',
      'aiSignal': 'AI Signal'
    };
    return names[indicator] || indicator.toUpperCase();
  }
  
  getIndicatorDescription(indicator) {
    const descriptions = {
      'rsi': 'Momentum oscillator (0-100)',
      'macd': 'Trend-following momentum indicator',
      'bollingerBands': 'Volatility bands around price',
      'atr': 'Measures market volatility',
      'volume': 'Trading volume analysis',
      'mlPrediction': 'AI-powered price prediction'
    };
    return descriptions[indicator] || 'Technical indicator';
  }
  
  getIndicatorCategory(indicator) {
    for (const [category, indicators] of Object.entries(this.availableIndicators)) {
      if (indicators.includes(indicator)) {
        return category;
      }
    }
    return 'unknown';
  }
  
  getCategoryColor(category) {
    const colors = {
      momentum: '#FF6B6B',
      trend: '#4ECDC4',
      volume: '#45B7D1',
      ml: '#96CEB4'
    };
    return colors[category] || '#FFFFFF';
  }
  
  getIndicatorInfo(indicator) {
    const info = {
      'rsi': 'RSI (Relative Strength Index)\n\nMeasures momentum on a scale of 0-100.\n• Above 70: Overbought\n• Below 30: Oversold\n• Divergences signal potential reversals',
      'macd': 'MACD (Moving Average Convergence Divergence)\n\nTrend-following momentum indicator.\n• Signal line crossovers\n• Histogram shows momentum\n• Centerline crossovers indicate trend changes',
      'bollingerBands': 'Bollinger Bands\n\nVolatility indicator with upper and lower bands.\n• Price touching upper band: Potential resistance\n• Price touching lower band: Potential support\n• Band squeeze indicates low volatility'
    };
    return info[indicator] || `${this.getIndicatorDisplayName(indicator)}\n\nTechnical indicator for market analysis.`;
  }
}

// Initialize enhanced indicator menu
window.EnhancedIndicatorMenu = EnhancedIndicatorMenu;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (!window.enhancedIndicatorMenu) {
    window.enhancedIndicatorMenu = new EnhancedIndicatorMenu();
    console.log('🎯 Enhanced Indicator Menu initialized');
  }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedIndicatorMenu;
}
